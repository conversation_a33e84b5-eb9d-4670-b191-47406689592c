export default [
  {
    name: 'baseCodeRule-page', // 编码规则分页
    method: 'POST',
    path: '/base/baseCodeRule/page'
  },
  {
    name: 'baseCodeRule-save', // 编码规则保存
    method: 'POST',
    path: '/base/baseCodeRule/save'
  },
  {
    name: 'baseCodeRule-get', // 获取编码规则
    method: 'OTHERGET',
    path: '/base/baseCodeRule/get'
  },
  {
    name: 'baseCodeRule-delete', // 编码规则删除
    method: 'DELETE',
    path: '/base/baseCodeRule/delete'
  },
  {
    name: 'baseCodeRuleSection-page', // 编码规则组成分页
    method: 'POST',
    path: '/base/baseCodeRuleSection/page'
  },
  {
    name: 'baseCodeRuleSection-save', // 编码规则组成保存
    method: 'POST',
    path: '/base/baseCodeRuleSection/save'
  },
  {
    name: 'baseCodeRuleSection-get', // 获取编码规则组成
    method: 'OTHERGET',
    path: '/base/baseCodeRuleSection/get'
  },
  {
    name: 'baseCodeRuleSection-delete', // 编码规则组成删除
    method: 'DELETE',
    path: '/base/baseCodeRuleSection/delete'
  }
];
