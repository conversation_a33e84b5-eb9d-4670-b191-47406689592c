<template>
  <el-drawer
    :title="'编辑'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <el-form :model="editForm" ref="editForm" :rules="rules">
        <el-form-item label="区域" prop="area">
          <el-input v-model="editForm.area" disabled />
        </el-form-item>

        <el-form-item label="日期" prop="passengerFlightDate">
          <el-date-picker
            v-model="editForm.passengerFlightDate"
            type="date"
            disabled
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="航班量" prop="flightCount">
          <el-input
            v-model="editForm.flightCount"
            placeholder="请输入航班量">
            <template slot="append">架次</template>
          </el-input>
        </el-form-item>

        <el-form-item label="旅客量" prop="passengerCount">
          <el-input
            v-model="editForm.passengerCount"
            placeholder="请输入旅客量">
            <template slot="append">人次</template>
          </el-input>
        </el-form-item>

        <!-- <el-form-item label="保障航班" prop="guaranteedFlightCount">
          <el-input
            v-model="editForm.guaranteedFlightCount"
            placeholder="请输入保障航班">
            <template slot="append">架次</template>
          </el-input>
        </el-form-item> -->
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EditPanel',
  data() {
    const validateNumber = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入数值'));
      } else if (isNaN(Number(value)) || Number(value) < 0) {
        callback(new Error('请输入大于0的数值'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      editForm: {
        area: '',
        passengerFlightDate: null,
        flightCount: '',
        passengerCount: '',
        guaranteedFlightCount: ''
      },
      rules: {
        flightCount: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ],
        passengerCount: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ],
        guaranteedFlightCount: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ]
      }
    };
  },
  methods: {
    open(data) {
      this.visible = true;
      this.editForm = _.cloneDeep(data);
    },
    handleCancel() {
      this.visible = false;
      this.$refs.editForm.resetFields();
    },
    handleSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const formData = {
            ...this.editForm,
            flightCount: Number(this.editForm.flightCount),
            guaranteedFlightCount: Number(this.editForm.guaranteedFlightCount),
            passengerCount: Number(this.editForm.passengerCount)
          };

          this.$api['boardData/dashboardPassengerFlight-save'](formData).then(() => {
            this.$message.success('保存成功');
            this.$emit('submit', formData);
            this.visible = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}
</style>
