<template>
  <el-drawer
    :title="mode === 'edit' ? '编辑事件' : '添加事件'"
    :visible.sync="visible"
    :size="'40%'"
    :destroy-on-close="true"
    :modal="false"
    :with-header="true"
  >
    <div class="drawer-content">
      <el-form :model="form" ref="form" :rules="rules" label-width="100px">
        <el-form-item label="事件名称" prop="eventName">
          <el-input v-model="form.eventName" placeholder="请输入事件名称" maxlength="50" show-word-limit></el-input>
        </el-form-item>


        <el-form-item label="数量" prop="number">
          <el-input-number v-model="form.number" :min="1" style="width: 100%" @change="calculateTotalScore"></el-input-number>
        </el-form-item>

        <el-form-item label="单值" prop="singleScore">
          <el-input v-model="form.singleScore" placeholder="请输入单值（负数表示扣分）" @input="calculateTotalScore"></el-input>
        </el-form-item>

        <el-form-item label="总分值">
          <el-input v-model="form.detailTotalScore" disabled></el-input>
          <div class="hint-text">总分值 = 数量 * 单值，自动计算</div>
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button size="small" type="primary" @click="handleSubmit" :loading="submitLoading">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EventPanel3',
  data() {
    const validateUnitValue = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入单值'));
        return;
      }

      const num = parseFloat(value);
      if (isNaN(num)) {
        callback(new Error('请输入有效的数字'));
        return;
      }

      callback();
    };

    return {
      visible: false,
      mode: 'add', // 'add' 或 'edit'
      form: {
        eventName: '',
        eventType: '',
        number: 1,
        singleScore: '',
        detailTotalScore: '',
        designId: null
      },
      rules: {
        eventName: [{required: true, message: '请输入事件名称', trigger: 'blur'}],
        eventType: [{required: true, message: '请选择事件类型', trigger: 'change'}],
        number: [{required: true, message: '请输入数量', trigger: 'blur'}],
        singleScore: [
          {required: true, message: '请输入单值', trigger: 'blur'},
          {validator: validateUnitValue, trigger: 'blur'}
        ]
      },
      submitLoading: false
    };
  },
  mounted() {
  },
  methods: { // 打开抽屉
    open(row, mode) {
      this.mode = mode;
      this.visible = true;
      if (mode == 'edit') {
        // 编辑模式
        this.form = {
          ...row
        };
      } else {
        // 新增模式
        this.resetForm();
        if (row.designId) {
          this.form.designId = row.designId;
        }
      }

      this.calculateTotalScore();
    },

    // 重置表单
    resetForm() {
      this.form = {
        eventName: '',
        eventType: '',
        number: 1,
        singleScore: '',
        detailTotalScore: '',
        designId: null
      };
    },

    // 计算总分值
    calculateTotalScore() {
      if (!this.form.number || !this.form.singleScore) {
        this.form.detailTotalScore = '';
        return;
      }

      const count = parseFloat(this.form.number);
      const unitValue = parseFloat(this.form.singleScore);

      if (isNaN(count) || isNaN(unitValue)) {
        this.form.detailTotalScore = '';
        return;
      }

      this.form.detailTotalScore = (count * unitValue).toString();
    },


    // 取消操作
    handleCancel() {
      this.visible = false;
    },
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true;

          // 获取父组件传递的设计ID
          const parentPanel = this.$parent;
          const designId = parentPanel && parentPanel.form && parentPanel.form.id;

          if (designId && !this.form.designId) {
            this.form.designId = designId;
          }

          if (!this.form.designId) {
            this.$message.error('缺少必要的关联ID');
            this.submitLoading = false;
            return;
          }

          // 计算总分值
          this.calculateTotalScore();

          const params = {...this.form};

          // 根据是否有ID判断是新增还是编辑
          const apiName = this.mode === 'edit'
            ? 'techDashboard/sysTechManagementRatingDesignDetail-save'
            : 'techDashboard/sysTechManagementRatingDesignDetail-save';

          this.$api[apiName](params)
            .then(res => {
              this.$message.success('操作成功');
              this.visible = false;
              // 通知父组件刷新数据
              this.$emit('refresh');
              this.submitLoading = false;
            })
            .catch(err => {
              this.submitLoading = false;
            });
        }
      });
    }
  },
  watch: {
    'form.number': function () {
      this.calculateTotalScore();
    },
    'form.singleScore': function () {
      this.calculateTotalScore();
    }
  }
};
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;

  .hint-text {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    text-align: right;
    background-color: #fff;
    border-top: 1px solid #e4e7ed;
  }
}
</style>