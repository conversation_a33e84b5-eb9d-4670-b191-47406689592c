{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue", "mtime": 1754276220642}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.string.includes\";\nimport _defineProperty from \"D:/boweiWorkSpace/pc/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { keys } from \"lodash\";\nexport default {\n  data: function data() {\n    return {\n      locationDesc: \"\",\n      treeData: [],\n      selectedNode: null,\n      deptAndUserData: [],\n      nodes: null,\n      resolves: null,\n      setTimer: null,\n      timer: null,\n      selectedNodes: [],\n      selectedUserList: [],\n      selectValue: \"\",\n      showSearchDeptTree: false,\n      checkedTreeList: [],\n      // 选中的值\n      checkedListKey: []\n    };\n  },\n  props: {\n    props: {\n      type: Object,\n      default: function _default() {\n        return {\n          label: \"primaryCode\",\n          children: \"children\",\n          isLeaf: \"leaf\"\n        };\n      }\n    },\n    value: {\n      type: String | Array,\n      default: \"\"\n    },\n    cnvalue: {\n      type: String,\n      default: \"\"\n    },\n    treeplaceholder: {\n      type: String,\n      default: \"请选择数据\"\n    },\n    highlightCurrent: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    isLazy: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    showCheckbox: {\n      type: Boolean,\n      default: function _default() {\n        return true;\n      }\n    },\n    apiUrl: {\n      type: String,\n      default: \"firstLineDept/get-loc-info-lazy-tree\"\n    },\n    // 已绑定的位置ID数组，用于设置节点的disabled状态\n    boundLocationIds: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  watch: {\n    value: function value(val) {\n      if (!val) {\n        if (!this.$attrs.multiple) {\n          this.clearSelect();\n        } else {\n          this.$refs.tree && this.$refs.tree.setCheckedKeys(val);\n        }\n      } else {\n        // this.selectValue = filterTree[0] ? filterTree[0][this.treeProps.label] : '';\n      }\n    },\n    cnvalue: function cnvalue(val) {\n      this.selectValue = val;\n    }\n  },\n  mounted: function mounted() {\n    this.setTimer = new Date().getTime();\n    this.selectValue = this.cnvalue;\n  },\n  methods: {\n    // 递归设置节点的disabled属性\n    setNodeDisabled: function setNodeDisabled(nodes) {\n      var _this = this;\n      if (!nodes || !Array.isArray(nodes)) {\n        return nodes;\n      }\n      return nodes.map(function (node) {\n        // 创建节点的副本以避免直接修改原数据\n        var newNode = _objectSpread({}, node);\n\n        // 检查当前节点的primaryId是否在已绑定的位置ID数组中\n        if (_this.boundLocationIds && _this.boundLocationIds.includes(newNode.primaryId)) {\n          newNode.disabled = true;\n        } else {\n          newNode.disabled = false;\n        }\n\n        // 如果有子节点，递归处理\n        if (newNode.children && Array.isArray(newNode.children)) {\n          newNode.children = _this.setNodeDisabled(newNode.children);\n        }\n        return newNode;\n      });\n    },\n    hideSearchTreeEvent: function hideSearchTreeEvent() {\n      this.locationDesc = \"\";\n    },\n    // 展示树时\n    showSearchTreeEvent: function showSearchTreeEvent() {\n      var _this2 = this;\n      this.$api[this.apiUrl]({\n        parentId: 0\n      }).then(function (data) {\n        // console.log(\"dataTree\", data);\n        // 设置节点的disabled属性\n        _this2.deptAndUserData = _this2.setNodeDisabled(data);\n      });\n\n      // this.$refs.tree && this.$refs.tree.setCheckedKeys([this.value]);\n    },\n    clearSelect: function clearSelect() {\n      this.selectValue = \"\";\n      this.$refs.tree && this.$refs.tree.setCheckedKeys([]);\n    },\n    setCheckedKeys: function setCheckedKeys(ids) {\n      this.$refs.tree && this.$refs.tree.setCheckedKeys(ids);\n    },\n    // 搜索树节点\n    onSearch: function onSearch() {\n      var _this3 = this;\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n      this.timer = setTimeout(function () {\n        if (!_this3.isLazy) {\n          // 非懒加载\n\n          if (_this3.locationDesc.length > 0) {\n            _this3.$api[_this3.apiUrl]({\n              desc: _this3.locationDesc\n            }).then(function (res) {\n              // 设置节点的disabled属性\n              _this3.deptAndUserData = _this3.setNodeDisabled(res);\n              console.log(\"this.showCheckbox1\", _this3.showCheckbox);\n              if (_this3.showCheckbox) {\n                console.log(\"this.showCheckbox2\", _this3.showCheckbox);\n                setTimeout(function () {\n                  _this3.selectedNodes.length && _this3.$refs.tree.setCheckedNodes(_this3.selectedNodes);\n                }, 500);\n              }\n            });\n          } else {\n            _this3.$api[_this3.apiUrl]({\n              parentId: 0\n            }).then(function (res) {\n              // 设置节点的disabled属性\n              _this3.deptAndUserData = _this3.setNodeDisabled(res);\n              console.log(\"this.showCheckbox1\", _this3.showCheckbox);\n              if (_this3.showCheckbox) {\n                console.log(\"this.showCheckbox2\", _this3.showCheckbox);\n                setTimeout(function () {\n                  _this3.selectedNodes.length && _this3.$refs.tree.setCheckedNodes(_this3.selectedNodes);\n                }, 500);\n              }\n            });\n          }\n          return;\n        }\n        if (_this3.locationDesc.length) {\n          _this3.$api[_this3.apiUrl]({\n            desc: _this3.locationDesc\n          }).then(function (res) {\n            // 设置节点的disabled属性\n            _this3.deptAndUserData = _this3.setNodeDisabled(res);\n            if (_this3.showCheckbox) {\n              setTimeout(function () {\n                _this3.selectedNodes.length && _this3.$refs.tree.setCheckedNodes(_this3.selectedNodes);\n              }, 500);\n            }\n          });\n        } else {\n          _this3.setTimer = new Date().getTime();\n          _this3.deptAndUserData = [];\n          _this3.nodes.data.primaryId = 0;\n          // console.log(\"====\", this.nodes);\n          _this3.loadNode(_this3.nodes, _this3.resolves);\n        }\n      }, 500);\n    },\n    getTreeDatas: function getTreeDatas(data, list) {\n      var _this4 = this;\n      if (!this.$attrs.multiple) {\n        var thisNode = this.$refs.tree.getNode(data.primaryId),\n          _keys = []; // 获取已勾选节点的key值\n        if (thisNode && thisNode.checked) {\n          _keys = [data];\n          // 当前节点若被选中\n          for (var i = thisNode.level; i > 1; i--) {\n            // 当前子节点选中，取消勾选父节点\n            this.$refs.tree.setChecked(thisNode.parent, false);\n            // 判断是否有父级节点\n            if (!thisNode.parent.checked) {\n              // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\n              thisNode = thisNode.parent;\n              _keys.unshift(thisNode.data);\n            }\n          }\n        }\n        var arr = [];\n        _keys.forEach(function (row) {\n          console.log(\"row\", row, row.primaryCode);\n          arr.push(\"\".concat(row.label, \" \").concat(row.primaryCode));\n        });\n        //   console.log(keys,arr.join('/'), 'keys++++++++++++++',data.primaryId)\n        this.selectedNodes = list.checkedNodes;\n        this.selectValue = arr.join(\"→\");\n        var checkedNodes = this.$refs.tree.getCheckedNodes();\n        if (checkedNodes.length > 0) {\n          this.$emit(\"getSelectCnData\", arr.join(\"→\"), data);\n          this.$emit(\"getSelectData\", data.primaryId);\n          this.$emit(\"getSelectTreeData\", data);\n          this.$emit(\"getCnValue\", this.selectValue);\n        } else {\n          this.$emit(\"getSelectCnData\", \"\", \"\");\n          this.$emit(\"getSelectData\", \"\");\n          this.$emit(\"getSelectTreeData\", \"\");\n          this.$emit(\"getCnValue\", \"\");\n        }\n        this.$forceUpdate();\n        this.showSearchDeptTree = false;\n      } else {\n        var _thisNode = this.$refs.tree.getNode(data.primaryId),\n          _keys2 = [data]; // 获取已勾选节点的key值\n        // 有取消子节点\n        if (_thisNode && _thisNode.checked && _thisNode.childNodes.length > 0) {\n          this.clearCheckedChildren(_thisNode.childNodes);\n        }\n        if (_thisNode && _thisNode.checked) {\n          // 当前节点若被选中\n          for (var _i2 = _thisNode.level; _i2 > 1; _i2--) {\n            // 当前子节点选中，取消勾选父节点\n            this.$refs.tree.setChecked(_thisNode.parent, false);\n          }\n        }\n\n        // 更新选中的节点列表\n        this.checkedTreeList = this.$refs.tree.getCheckedNodes() || [];\n\n        // 立即触发事件，传递选中的数据\n        var checkedListValue = [];\n        this.checkedListKey = [];\n        this.checkedTreeList.forEach(function (item) {\n          _this4.checkedListKey.push(item.primaryId);\n          var thisNode = _this4.$refs.tree.getNode(item.primaryId);\n          var keys = [thisNode.data];\n          if (thisNode && thisNode.checked) {\n            // 当前节点若被选中\n            for (var _i4 = thisNode.level; _i4 > 1; _i4--) {\n              // 判断是否有父级节点\n              if (!thisNode.parent.checked) {\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\n                thisNode = thisNode.parent;\n                keys.unshift(thisNode.data);\n              }\n            }\n          }\n          var arr = [];\n          keys.forEach(function (row) {\n            arr.push(\"\".concat(row.label, \" \").concat(row.primaryCode));\n          });\n          checkedListValue.push(arr.join(\"→\"));\n        });\n        this.selectValue = checkedListValue.join(\"||\");\n        this.$emit(\"getSelectData\", this.checkedListKey);\n        this.$emit(\"getSelectCnData\", this.selectValue);\n      }\n      //\n    },\n    clearCheckedChildren: function clearCheckedChildren(children) {\n      var _this5 = this;\n      children.length > 0 && children.forEach(function (child) {\n        _this5.$refs.tree.setChecked(child, false);\n        if (child.childNodes) {\n          _this5.clearCheckedChildren(child.childNodes);\n        }\n      });\n    },\n    submitBindPositionForm: function submitBindPositionForm() {\n      var _this6 = this;\n      if (this.$attrs.multiple) {\n        var checkedListValue = [];\n        this.checkedListKey = [];\n        // console.log(\"this.checkedTreeList\", this.checkedTreeList);\n        this.checkedTreeList.forEach(function (item) {\n          _this6.checkedListKey.push(item.primaryId);\n          var thisNode = _this6.$refs.tree.getNode(item.primaryId);\n          var keys = [thisNode.data];\n          if (thisNode && thisNode.checked) {\n            // 当前节点若被选中\n            for (var i = thisNode.level; i > 1; i--) {\n              // 判断是否有父级节点\n              if (!thisNode.parent.checked) {\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\n                thisNode = thisNode.parent;\n                keys.unshift(thisNode.data);\n              }\n            }\n          }\n          var arr = [];\n          keys.forEach(function (row) {\n            arr.push(\"\".concat(row.label, \" \").concat(row.primaryCode));\n          });\n          checkedListValue.push(arr.join(\"→\"));\n          _this6.selectValue = checkedListValue.join(\"||\");\n          _this6.$emit(\"getSelectData\", _this6.checkedListKey);\n          _this6.$emit(\"getSelectCnData\", _this6.selectValue);\n          _this6.showSearchDeptTree = false;\n        });\n      }\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      var _this7 = this;\n      if (this.showCheckbox) {\n        return;\n      }\n      if (data.primaryId.indexOf(\"org\") !== -1) {\n        setTimeout(function () {\n          _this7.$refs.tree.setCurrentKey(_this7.selectedNode);\n        }, 0);\n      } else {\n        this.selectedNodes = [data];\n        this.selectedNode = data.primaryId;\n        this.$emit(\"treeNode\", data);\n      }\n    },\n    // 点击懒加载\n    loadNode: function loadNode(node, resolve) {\n      var _this8 = this;\n      if (node.level === 0) {\n        this.showSearchTreeEvent();\n        return;\n      }\n      this.nodes = node;\n      this.resolves = resolve;\n      this.$api[this.apiUrl]({\n        parentId: node.data.primaryId\n      }).then(function (res) {\n        // 设置节点的disabled属性\n        var processedData = _this8.setNodeDisabled(res);\n        resolve(processedData);\n        if (_this8.showCheckbox) {\n          setTimeout(function () {\n            _this8.$refs.tree.setCheckedKeys([_this8.value]);\n          }, 100);\n        } else {\n          setTimeout(function () {\n            _this8.$refs.tree.setCurrentKey(_this8.selectedNode);\n          }, 0);\n        }\n      });\n    }\n  }\n};", {"version": 3, "names": ["keys", "data", "locationDesc", "treeData", "selectedNode", "deptAndUserData", "nodes", "resolves", "setTimer", "timer", "selectedNodes", "selectedUserList", "selectValue", "showSearchDeptTree", "checkedTreeList", "checked<PERSON><PERSON><PERSON><PERSON>", "props", "type", "Object", "default", "_default", "label", "children", "<PERSON><PERSON><PERSON><PERSON>", "value", "String", "Array", "cnvalue", "treeplaceholder", "highlightCurrent", "Boolean", "isLazy", "showCheckbox", "apiUrl", "boundLocationIds", "watch", "val", "$attrs", "multiple", "clearSelect", "$refs", "tree", "set<PERSON><PERSON><PERSON><PERSON>eys", "mounted", "Date", "getTime", "methods", "setNodeDisabled", "_this", "isArray", "map", "node", "newNode", "_objectSpread", "includes", "primaryId", "disabled", "hideSearchTreeEvent", "showSearchTreeEvent", "_this2", "$api", "parentId", "then", "ids", "onSearch", "_this3", "clearTimeout", "setTimeout", "length", "desc", "res", "console", "log", "setCheckedNodes", "loadNode", "getTreeDatas", "list", "_this4", "thisNode", "getNode", "checked", "i", "level", "setChecked", "parent", "unshift", "arr", "for<PERSON>ach", "row", "primaryCode", "push", "concat", "checkedNodes", "join", "getCheckedNodes", "$emit", "$forceUpdate", "childNodes", "clear<PERSON>he<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkedListValue", "item", "_this5", "child", "submitBindPositionForm", "_this6", "handleNodeClick", "_this7", "indexOf", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "_this8", "processedData"], "sources": ["src/components/treeComp/localtionTree.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-popover\r\n      v-model=\"showSearchDeptTree\"\r\n      placement=\"right\"\r\n      width=\"800\"\r\n      trigger=\"click\"\r\n      @show=\"showSearchTreeEvent\"\r\n      @hide=\"hideSearchTreeEvent\"\r\n    >\r\n      <div>\r\n        <el-input\r\n          size=\"small\"\r\n          style=\"width: 100%; margin-bottom: 8px\"\r\n          placeholder=\"请输入关键字搜索\"\r\n          v-model=\"locationDesc\"\r\n          @input=\"onSearch\"\r\n          @clear=\"onSearch\"\r\n          clearable\r\n        >\r\n          default-expand-all\r\n        </el-input>\r\n      </div>\r\n      <div style=\"height: 460px; overflow-y: scroll\">\r\n        <el-tree\r\n          :props=\"props\"\r\n          :data=\"deptAndUserData\"\r\n          :show-checkbox=\"showCheckbox\"\r\n          :load=\"loadNode\"\r\n          v-bind=\"$attrs\"\r\n          v-on=\"$listeners\"\r\n          :lazy=\"isLazy\"\r\n          @node-click=\"handleNodeClick\"\r\n          @check=\"getTreeDatas\"\r\n          :highlight-current=\"highlightCurrent\"\r\n          node-key=\"primaryId\"\r\n          :key=\"setTimer\"\r\n          check-strictly\r\n          ref=\"tree\"\r\n          :default-checked-keys=\"Array.isArray(value) ? value : [value]\"\r\n          v-if=\"showSearchDeptTree\"\r\n        >\r\n          <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n            <span>\r\n              <span style=\"margin-left: 2px\"\r\n                >{{ data.label }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{\r\n                  node.label\r\n                }}</span\r\n              >\r\n            </span>\r\n          </span>\r\n        </el-tree>\r\n      </div>\r\n\r\n      <el-input\r\n        size=\"small\"\r\n        style=\"width: 100%; margin-left: 8px\"\r\n        v-model=\"selectValue\"\r\n        type=\"textarea\"\r\n        :placeholder=\"treeplaceholder\"\r\n        readonly\r\n        clearable\r\n        @clear=\"clearSelect\"\r\n        slot=\"reference\"\r\n      ></el-input>\r\n\r\n      <span class=\"dialog-footer\" v-if=\"$attrs.multiple\">\r\n        <el-button size=\"small\" @click=\"showSearchDeptTree = false\"\r\n          >取 消</el-button\r\n        >\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBindPositionForm\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {keys} from \"lodash\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      locationDesc: \"\",\r\n      treeData: [],\r\n      selectedNode: null,\r\n      deptAndUserData: [],\r\n      nodes: null,\r\n      resolves: null,\r\n      setTimer: null,\r\n      timer: null,\r\n      selectedNodes: [],\r\n      selectedUserList: [],\r\n      selectValue: \"\",\r\n      showSearchDeptTree: false,\r\n      checkedTreeList: [], // 选中的值\r\n      checkedListKey: [],\r\n    };\r\n  },\r\n  props: {\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          label: \"primaryCode\",\r\n          children: \"children\",\r\n          isLeaf: \"leaf\",\r\n        };\r\n      },\r\n    },\r\n    value: {\r\n      type: String | Array,\r\n      default: \"\",\r\n    },\r\n    cnvalue: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    treeplaceholder: {\r\n      type: String,\r\n      default: \"请选择数据\",\r\n    },\r\n    highlightCurrent: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    isLazy: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showCheckbox: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    apiUrl: {\r\n      type: String,\r\n      default: \"firstLineDept/get-loc-info-lazy-tree\",\r\n    },\r\n    // 已绑定的位置ID数组，用于设置节点的disabled状态\r\n    boundLocationIds: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n  },\r\n  watch: {\r\n    value(val) {\r\n      if (!val) {\r\n        if (!this.$attrs.multiple) {\r\n          this.clearSelect();\r\n        } else {\r\n          this.$refs.tree && this.$refs.tree.setCheckedKeys(val);\r\n        }\r\n      } else {\r\n        // this.selectValue = filterTree[0] ? filterTree[0][this.treeProps.label] : '';\r\n      }\r\n    },\r\n    cnvalue(val) {\r\n      this.selectValue = val;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.setTimer = new Date().getTime();\r\n    this.selectValue = this.cnvalue;\r\n  },\r\n  methods: {\r\n    // 递归设置节点的disabled属性\r\n    setNodeDisabled(nodes) {\r\n      if (!nodes || !Array.isArray(nodes)) {\r\n        return nodes;\r\n      }\r\n\r\n      return nodes.map(node => {\r\n        // 创建节点的副本以避免直接修改原数据\r\n        const newNode = {...node};\r\n\r\n        // 检查当前节点的primaryId是否在已绑定的位置ID数组中\r\n        if (this.boundLocationIds && this.boundLocationIds.includes(newNode.primaryId)) {\r\n          newNode.disabled = true;\r\n        } else {\r\n          newNode.disabled = false;\r\n        }\r\n\r\n        // 如果有子节点，递归处理\r\n        if (newNode.children && Array.isArray(newNode.children)) {\r\n          newNode.children = this.setNodeDisabled(newNode.children);\r\n        }\r\n\r\n        return newNode;\r\n      });\r\n    },\r\n\r\n    hideSearchTreeEvent() {\r\n      this.locationDesc = \"\";\r\n    },\r\n    // 展示树时\r\n    showSearchTreeEvent() {\r\n      this.$api[this.apiUrl]({\r\n        parentId: 0,\r\n      }).then(data => {\r\n        // console.log(\"dataTree\", data);\r\n        // 设置节点的disabled属性\r\n        this.deptAndUserData = this.setNodeDisabled(data);\r\n      });\r\n\r\n      // this.$refs.tree && this.$refs.tree.setCheckedKeys([this.value]);\r\n    },\r\n    clearSelect() {\r\n      this.selectValue = \"\";\r\n      this.$refs.tree && this.$refs.tree.setCheckedKeys([]);\r\n    },\r\n    setCheckedKeys(ids) {\r\n      this.$refs.tree && this.$refs.tree.setCheckedKeys(ids);\r\n    },\r\n    // 搜索树节点\r\n    onSearch() {\r\n      if (this.timer) {\r\n        clearTimeout(this.timer);\r\n      }\r\n      this.timer = setTimeout(() => {\r\n        if (!this.isLazy) {\r\n          // 非懒加载\r\n\r\n          if (this.locationDesc.length > 0) {\r\n            this.$api[this.apiUrl]({\r\n              desc: this.locationDesc,\r\n            }).then(res => {\r\n              // 设置节点的disabled属性\r\n              this.deptAndUserData = this.setNodeDisabled(res);\r\n              console.log(\"this.showCheckbox1\", this.showCheckbox);\r\n              if (this.showCheckbox) {\r\n                console.log(\"this.showCheckbox2\", this.showCheckbox);\r\n                setTimeout(() => {\r\n                  this.selectedNodes.length\r\n                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n                }, 500);\r\n              }\r\n            });\r\n          } else {\r\n            this.$api[this.apiUrl]({\r\n              parentId: 0,\r\n            }).then(res => {\r\n              // 设置节点的disabled属性\r\n              this.deptAndUserData = this.setNodeDisabled(res);\r\n              console.log(\"this.showCheckbox1\", this.showCheckbox);\r\n              if (this.showCheckbox) {\r\n                console.log(\"this.showCheckbox2\", this.showCheckbox);\r\n                setTimeout(() => {\r\n                  this.selectedNodes.length\r\n                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n                }, 500);\r\n              }\r\n            });\r\n          }\r\n\r\n          return;\r\n        }\r\n        if (this.locationDesc.length) {\r\n          this.$api[this.apiUrl]({\r\n            desc: this.locationDesc,\r\n          }).then(res => {\r\n            // 设置节点的disabled属性\r\n            this.deptAndUserData = this.setNodeDisabled(res);\r\n            if (this.showCheckbox) {\r\n              setTimeout(() => {\r\n                this.selectedNodes.length\r\n                  && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n              }, 500);\r\n            }\r\n          });\r\n        } else {\r\n          this.setTimer = new Date().getTime();\r\n          this.deptAndUserData = [];\r\n          this.nodes.data.primaryId = 0;\r\n          // console.log(\"====\", this.nodes);\r\n          this.loadNode(this.nodes, this.resolves);\r\n        }\r\n      }, 500);\r\n    },\r\n    getTreeDatas(data, list) {\r\n      if (!this.$attrs.multiple) {\r\n        let thisNode = this.$refs.tree.getNode(data.primaryId),\r\n          keys = []; // 获取已勾选节点的key值\r\n        if (thisNode && thisNode.checked) {\r\n          keys = [data];\r\n          // 当前节点若被选中\r\n          for (let i = thisNode.level; i > 1; i--) {\r\n            // 当前子节点选中，取消勾选父节点\r\n            this.$refs.tree.setChecked(thisNode.parent, false);\r\n            // 判断是否有父级节点\r\n            if (!thisNode.parent.checked) {\r\n              // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n              thisNode = thisNode.parent;\r\n              keys.unshift(thisNode.data);\r\n            }\r\n          }\r\n        }\r\n        let arr = [];\r\n        keys.forEach(row => {\r\n          console.log(\"row\", row, row.primaryCode);\r\n\r\n          arr.push(`${row.label} ${row.primaryCode}`);\r\n        });\r\n        //   console.log(keys,arr.join('/'), 'keys++++++++++++++',data.primaryId)\r\n        this.selectedNodes = list.checkedNodes;\r\n        this.selectValue = arr.join(\"→\");\r\n        const checkedNodes = this.$refs.tree.getCheckedNodes();\r\n\r\n        if (checkedNodes.length > 0) {\r\n          this.$emit(\"getSelectCnData\", arr.join(\"→\"), data);\r\n          this.$emit(\"getSelectData\", data.primaryId);\r\n          this.$emit(\"getSelectTreeData\", data);\r\n          this.$emit(\"getCnValue\", this.selectValue);\r\n        } else {\r\n          this.$emit(\"getSelectCnData\", \"\", \"\");\r\n          this.$emit(\"getSelectData\", \"\");\r\n          this.$emit(\"getSelectTreeData\", \"\");\r\n          this.$emit(\"getCnValue\", \"\");\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.showSearchDeptTree = false;\r\n      } else {\r\n        let thisNode = this.$refs.tree.getNode(data.primaryId),\r\n          keys = [data]; // 获取已勾选节点的key值\r\n        // 有取消子节点\r\n        if (thisNode && thisNode.checked && thisNode.childNodes.length > 0) {\r\n          this.clearCheckedChildren(thisNode.childNodes);\r\n        }\r\n\r\n        if (thisNode && thisNode.checked) {\r\n          // 当前节点若被选中\r\n          for (let i = thisNode.level; i > 1; i--) {\r\n            // 当前子节点选中，取消勾选父节点\r\n            this.$refs.tree.setChecked(thisNode.parent, false);\r\n          }\r\n        }\r\n\r\n        // 更新选中的节点列表\r\n        this.checkedTreeList = this.$refs.tree.getCheckedNodes() || [];\r\n\r\n        // 立即触发事件，传递选中的数据\r\n        let checkedListValue = [];\r\n        this.checkedListKey = [];\r\n\r\n        this.checkedTreeList.forEach(item => {\r\n          this.checkedListKey.push(item.primaryId);\r\n          let thisNode = this.$refs.tree.getNode(item.primaryId);\r\n          let keys = [thisNode.data];\r\n          if (thisNode && thisNode.checked) {\r\n            // 当前节点若被选中\r\n            for (let i = thisNode.level; i > 1; i--) {\r\n              // 判断是否有父级节点\r\n              if (!thisNode.parent.checked) {\r\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n                thisNode = thisNode.parent;\r\n                keys.unshift(thisNode.data);\r\n              }\r\n            }\r\n          }\r\n          let arr = [];\r\n          keys.forEach(row => {\r\n            arr.push(`${row.label} ${row.primaryCode}`);\r\n          });\r\n          checkedListValue.push(arr.join(\"→\"));\r\n        });\r\n\r\n        this.selectValue = checkedListValue.join(\"||\");\r\n        this.$emit(\"getSelectData\", this.checkedListKey);\r\n        this.$emit(\"getSelectCnData\", this.selectValue);\r\n      }\r\n      //\r\n    },\r\n    clearCheckedChildren(children) {\r\n      children.length > 0\r\n        && children.forEach(child => {\r\n          this.$refs.tree.setChecked(child, false);\r\n          if (child.childNodes) {\r\n            this.clearCheckedChildren(child.childNodes);\r\n          }\r\n        });\r\n    },\r\n    submitBindPositionForm() {\r\n      if (this.$attrs.multiple) {\r\n        let checkedListValue = [];\r\n        this.checkedListKey = [];\r\n        // console.log(\"this.checkedTreeList\", this.checkedTreeList);\r\n        this.checkedTreeList.forEach(item => {\r\n          this.checkedListKey.push(item.primaryId);\r\n          let thisNode = this.$refs.tree.getNode(item.primaryId);\r\n          let keys = [thisNode.data];\r\n          if (thisNode && thisNode.checked) {\r\n            // 当前节点若被选中\r\n            for (let i = thisNode.level; i > 1; i--) {\r\n              // 判断是否有父级节点\r\n              if (!thisNode.parent.checked) {\r\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n                thisNode = thisNode.parent;\r\n                keys.unshift(thisNode.data);\r\n              }\r\n            }\r\n          }\r\n          let arr = [];\r\n          keys.forEach(row => {\r\n            arr.push(`${row.label} ${row.primaryCode}`);\r\n          });\r\n          checkedListValue.push(arr.join(\"→\"));\r\n          this.selectValue = checkedListValue.join(\"||\");\r\n          this.$emit(\"getSelectData\", this.checkedListKey);\r\n          this.$emit(\"getSelectCnData\", this.selectValue);\r\n          this.showSearchDeptTree = false;\r\n        });\r\n      }\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.showCheckbox) {\r\n        return;\r\n      }\r\n      if (data.primaryId.indexOf(\"org\") !== -1) {\r\n        setTimeout(() => {\r\n          this.$refs.tree.setCurrentKey(this.selectedNode);\r\n        }, 0);\r\n      } else {\r\n        this.selectedNodes = [data];\r\n        this.selectedNode = data.primaryId;\r\n        this.$emit(\"treeNode\", data);\r\n      }\r\n    },\r\n    // 点击懒加载\r\n    loadNode(node, resolve) {\r\n      if (node.level === 0) {\r\n        this.showSearchTreeEvent();\r\n        return;\r\n      }\r\n      this.nodes = node;\r\n      this.resolves = resolve;\r\n      this.$api[this.apiUrl]({\r\n        parentId: node.data.primaryId,\r\n      }).then(res => {\r\n        // 设置节点的disabled属性\r\n        const processedData = this.setNodeDisabled(res);\r\n        resolve(processedData);\r\n        if (this.showCheckbox) {\r\n          setTimeout(() => {\r\n            this.$refs.tree.setCheckedKeys([this.value]);\r\n          }, 100);\r\n        } else {\r\n          setTimeout(() => {\r\n            this.$refs.tree.setCurrentKey(this.selectedNode);\r\n          }, 0);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AA+EA,SAAAA,IAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,QAAA;MACAC,YAAA;MACAC,eAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,KAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,eAAA;MAAA;MACAC,cAAA;IACA;EACA;EACAC,KAAA;IACAA,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QACA;UACAC,KAAA;UACAC,QAAA;UACAC,MAAA;QACA;MACA;IACA;IACAC,KAAA;MACAP,IAAA,EAAAQ,MAAA,GAAAC,KAAA;MACAP,OAAA;IACA;IACAQ,OAAA;MACAV,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACAS,eAAA;MACAX,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACAU,gBAAA;MACAZ,IAAA,EAAAa,OAAA;MACAX,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAW,MAAA;MACAd,IAAA,EAAAa,OAAA;MACAX,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAY,YAAA;MACAf,IAAA,EAAAa,OAAA;MACAX,OAAA,WAAAC,SAAA;QACA;MACA;IACA;IACAa,MAAA;MACAhB,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACA;IACAe,gBAAA;MACAjB,IAAA,EAAAS,KAAA;MACAP,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAe,KAAA;IACAX,KAAA,WAAAA,MAAAY,GAAA;MACA,KAAAA,GAAA;QACA,UAAAC,MAAA,CAAAC,QAAA;UACA,KAAAC,WAAA;QACA;UACA,KAAAC,KAAA,CAAAC,IAAA,SAAAD,KAAA,CAAAC,IAAA,CAAAC,cAAA,CAAAN,GAAA;QACA;MACA;QACA;MAAA;IAEA;IACAT,OAAA,WAAAA,QAAAS,GAAA;MACA,KAAAxB,WAAA,GAAAwB,GAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAnC,QAAA,OAAAoC,IAAA,GAAAC,OAAA;IACA,KAAAjC,WAAA,QAAAe,OAAA;EACA;EACAmB,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAzC,KAAA;MAAA,IAAA0C,KAAA;MACA,KAAA1C,KAAA,KAAAoB,KAAA,CAAAuB,OAAA,CAAA3C,KAAA;QACA,OAAAA,KAAA;MACA;MAEA,OAAAA,KAAA,CAAA4C,GAAA,WAAAC,IAAA;QACA;QACA,IAAAC,OAAA,GAAAC,aAAA,KAAAF,IAAA;;QAEA;QACA,IAAAH,KAAA,CAAAd,gBAAA,IAAAc,KAAA,CAAAd,gBAAA,CAAAoB,QAAA,CAAAF,OAAA,CAAAG,SAAA;UACAH,OAAA,CAAAI,QAAA;QACA;UACAJ,OAAA,CAAAI,QAAA;QACA;;QAEA;QACA,IAAAJ,OAAA,CAAA9B,QAAA,IAAAI,KAAA,CAAAuB,OAAA,CAAAG,OAAA,CAAA9B,QAAA;UACA8B,OAAA,CAAA9B,QAAA,GAAA0B,KAAA,CAAAD,eAAA,CAAAK,OAAA,CAAA9B,QAAA;QACA;QAEA,OAAA8B,OAAA;MACA;IACA;IAEAK,mBAAA,WAAAA,oBAAA;MACA,KAAAvD,YAAA;IACA;IACA;IACAwD,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,IAAA,MAAA3B,MAAA;QACA4B,QAAA;MACA,GAAAC,IAAA,WAAA7D,IAAA;QACA;QACA;QACA0D,MAAA,CAAAtD,eAAA,GAAAsD,MAAA,CAAAZ,eAAA,CAAA9C,IAAA;MACA;;MAEA;IACA;IACAsC,WAAA,WAAAA,YAAA;MACA,KAAA3B,WAAA;MACA,KAAA4B,KAAA,CAAAC,IAAA,SAAAD,KAAA,CAAAC,IAAA,CAAAC,cAAA;IACA;IACAA,cAAA,WAAAA,eAAAqB,GAAA;MACA,KAAAvB,KAAA,CAAAC,IAAA,SAAAD,KAAA,CAAAC,IAAA,CAAAC,cAAA,CAAAqB,GAAA;IACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAAxD,KAAA;QACAyD,YAAA,MAAAzD,KAAA;MACA;MACA,KAAAA,KAAA,GAAA0D,UAAA;QACA,KAAAF,MAAA,CAAAlC,MAAA;UACA;;UAEA,IAAAkC,MAAA,CAAA/D,YAAA,CAAAkE,MAAA;YACAH,MAAA,CAAAL,IAAA,CAAAK,MAAA,CAAAhC,MAAA;cACAoC,IAAA,EAAAJ,MAAA,CAAA/D;YACA,GAAA4D,IAAA,WAAAQ,GAAA;cACA;cACAL,MAAA,CAAA5D,eAAA,GAAA4D,MAAA,CAAAlB,eAAA,CAAAuB,GAAA;cACAC,OAAA,CAAAC,GAAA,uBAAAP,MAAA,CAAAjC,YAAA;cACA,IAAAiC,MAAA,CAAAjC,YAAA;gBACAuC,OAAA,CAAAC,GAAA,uBAAAP,MAAA,CAAAjC,YAAA;gBACAmC,UAAA;kBACAF,MAAA,CAAAvD,aAAA,CAAA0D,MAAA,IACAH,MAAA,CAAAzB,KAAA,CAAAC,IAAA,CAAAgC,eAAA,CAAAR,MAAA,CAAAvD,aAAA;gBACA;cACA;YACA;UACA;YACAuD,MAAA,CAAAL,IAAA,CAAAK,MAAA,CAAAhC,MAAA;cACA4B,QAAA;YACA,GAAAC,IAAA,WAAAQ,GAAA;cACA;cACAL,MAAA,CAAA5D,eAAA,GAAA4D,MAAA,CAAAlB,eAAA,CAAAuB,GAAA;cACAC,OAAA,CAAAC,GAAA,uBAAAP,MAAA,CAAAjC,YAAA;cACA,IAAAiC,MAAA,CAAAjC,YAAA;gBACAuC,OAAA,CAAAC,GAAA,uBAAAP,MAAA,CAAAjC,YAAA;gBACAmC,UAAA;kBACAF,MAAA,CAAAvD,aAAA,CAAA0D,MAAA,IACAH,MAAA,CAAAzB,KAAA,CAAAC,IAAA,CAAAgC,eAAA,CAAAR,MAAA,CAAAvD,aAAA;gBACA;cACA;YACA;UACA;UAEA;QACA;QACA,IAAAuD,MAAA,CAAA/D,YAAA,CAAAkE,MAAA;UACAH,MAAA,CAAAL,IAAA,CAAAK,MAAA,CAAAhC,MAAA;YACAoC,IAAA,EAAAJ,MAAA,CAAA/D;UACA,GAAA4D,IAAA,WAAAQ,GAAA;YACA;YACAL,MAAA,CAAA5D,eAAA,GAAA4D,MAAA,CAAAlB,eAAA,CAAAuB,GAAA;YACA,IAAAL,MAAA,CAAAjC,YAAA;cACAmC,UAAA;gBACAF,MAAA,CAAAvD,aAAA,CAAA0D,MAAA,IACAH,MAAA,CAAAzB,KAAA,CAAAC,IAAA,CAAAgC,eAAA,CAAAR,MAAA,CAAAvD,aAAA;cACA;YACA;UACA;QACA;UACAuD,MAAA,CAAAzD,QAAA,OAAAoC,IAAA,GAAAC,OAAA;UACAoB,MAAA,CAAA5D,eAAA;UACA4D,MAAA,CAAA3D,KAAA,CAAAL,IAAA,CAAAsD,SAAA;UACA;UACAU,MAAA,CAAAS,QAAA,CAAAT,MAAA,CAAA3D,KAAA,EAAA2D,MAAA,CAAA1D,QAAA;QACA;MACA;IACA;IACAoE,YAAA,WAAAA,aAAA1E,IAAA,EAAA2E,IAAA;MAAA,IAAAC,MAAA;MACA,UAAAxC,MAAA,CAAAC,QAAA;QACA,IAAAwC,QAAA,QAAAtC,KAAA,CAAAC,IAAA,CAAAsC,OAAA,CAAA9E,IAAA,CAAAsD,SAAA;UACAvD,KAAA;QACA,IAAA8E,QAAA,IAAAA,QAAA,CAAAE,OAAA;UACAhF,KAAA,IAAAC,IAAA;UACA;UACA,SAAAgF,CAAA,GAAAH,QAAA,CAAAI,KAAA,EAAAD,CAAA,MAAAA,CAAA;YACA;YACA,KAAAzC,KAAA,CAAAC,IAAA,CAAA0C,UAAA,CAAAL,QAAA,CAAAM,MAAA;YACA;YACA,KAAAN,QAAA,CAAAM,MAAA,CAAAJ,OAAA;cACA;cACAF,QAAA,GAAAA,QAAA,CAAAM,MAAA;cACApF,KAAA,CAAAqF,OAAA,CAAAP,QAAA,CAAA7E,IAAA;YACA;UACA;QACA;QACA,IAAAqF,GAAA;QACAtF,KAAA,CAAAuF,OAAA,WAAAC,GAAA;UACAjB,OAAA,CAAAC,GAAA,QAAAgB,GAAA,EAAAA,GAAA,CAAAC,WAAA;UAEAH,GAAA,CAAAI,IAAA,IAAAC,MAAA,CAAAH,GAAA,CAAAnE,KAAA,OAAAsE,MAAA,CAAAH,GAAA,CAAAC,WAAA;QACA;QACA;QACA,KAAA/E,aAAA,GAAAkE,IAAA,CAAAgB,YAAA;QACA,KAAAhF,WAAA,GAAA0E,GAAA,CAAAO,IAAA;QACA,IAAAD,YAAA,QAAApD,KAAA,CAAAC,IAAA,CAAAqD,eAAA;QAEA,IAAAF,YAAA,CAAAxB,MAAA;UACA,KAAA2B,KAAA,oBAAAT,GAAA,CAAAO,IAAA,OAAA5F,IAAA;UACA,KAAA8F,KAAA,kBAAA9F,IAAA,CAAAsD,SAAA;UACA,KAAAwC,KAAA,sBAAA9F,IAAA;UACA,KAAA8F,KAAA,oBAAAnF,WAAA;QACA;UACA,KAAAmF,KAAA;UACA,KAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAA,KAAA;QACA;QAEA,KAAAC,YAAA;QACA,KAAAnF,kBAAA;MACA;QACA,IAAAiE,SAAA,QAAAtC,KAAA,CAAAC,IAAA,CAAAsC,OAAA,CAAA9E,IAAA,CAAAsD,SAAA;UACAvD,MAAA,IAAAC,IAAA;QACA;QACA,IAAA6E,SAAA,IAAAA,SAAA,CAAAE,OAAA,IAAAF,SAAA,CAAAmB,UAAA,CAAA7B,MAAA;UACA,KAAA8B,oBAAA,CAAApB,SAAA,CAAAmB,UAAA;QACA;QAEA,IAAAnB,SAAA,IAAAA,SAAA,CAAAE,OAAA;UACA;UACA,SAAAC,GAAA,GAAAH,SAAA,CAAAI,KAAA,EAAAD,GAAA,MAAAA,GAAA;YACA;YACA,KAAAzC,KAAA,CAAAC,IAAA,CAAA0C,UAAA,CAAAL,SAAA,CAAAM,MAAA;UACA;QACA;;QAEA;QACA,KAAAtE,eAAA,QAAA0B,KAAA,CAAAC,IAAA,CAAAqD,eAAA;;QAEA;QACA,IAAAK,gBAAA;QACA,KAAApF,cAAA;QAEA,KAAAD,eAAA,CAAAyE,OAAA,WAAAa,IAAA;UACAvB,MAAA,CAAA9D,cAAA,CAAA2E,IAAA,CAAAU,IAAA,CAAA7C,SAAA;UACA,IAAAuB,QAAA,GAAAD,MAAA,CAAArC,KAAA,CAAAC,IAAA,CAAAsC,OAAA,CAAAqB,IAAA,CAAA7C,SAAA;UACA,IAAAvD,IAAA,IAAA8E,QAAA,CAAA7E,IAAA;UACA,IAAA6E,QAAA,IAAAA,QAAA,CAAAE,OAAA;YACA;YACA,SAAAC,GAAA,GAAAH,QAAA,CAAAI,KAAA,EAAAD,GAAA,MAAAA,GAAA;cACA;cACA,KAAAH,QAAA,CAAAM,MAAA,CAAAJ,OAAA;gBACA;gBACAF,QAAA,GAAAA,QAAA,CAAAM,MAAA;gBACApF,IAAA,CAAAqF,OAAA,CAAAP,QAAA,CAAA7E,IAAA;cACA;YACA;UACA;UACA,IAAAqF,GAAA;UACAtF,IAAA,CAAAuF,OAAA,WAAAC,GAAA;YACAF,GAAA,CAAAI,IAAA,IAAAC,MAAA,CAAAH,GAAA,CAAAnE,KAAA,OAAAsE,MAAA,CAAAH,GAAA,CAAAC,WAAA;UACA;UACAU,gBAAA,CAAAT,IAAA,CAAAJ,GAAA,CAAAO,IAAA;QACA;QAEA,KAAAjF,WAAA,GAAAuF,gBAAA,CAAAN,IAAA;QACA,KAAAE,KAAA,uBAAAhF,cAAA;QACA,KAAAgF,KAAA,yBAAAnF,WAAA;MACA;MACA;IACA;IACAsF,oBAAA,WAAAA,qBAAA5E,QAAA;MAAA,IAAA+E,MAAA;MACA/E,QAAA,CAAA8C,MAAA,QACA9C,QAAA,CAAAiE,OAAA,WAAAe,KAAA;QACAD,MAAA,CAAA7D,KAAA,CAAAC,IAAA,CAAA0C,UAAA,CAAAmB,KAAA;QACA,IAAAA,KAAA,CAAAL,UAAA;UACAI,MAAA,CAAAH,oBAAA,CAAAI,KAAA,CAAAL,UAAA;QACA;MACA;IACA;IACAM,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnE,MAAA,CAAAC,QAAA;QACA,IAAA6D,gBAAA;QACA,KAAApF,cAAA;QACA;QACA,KAAAD,eAAA,CAAAyE,OAAA,WAAAa,IAAA;UACAI,MAAA,CAAAzF,cAAA,CAAA2E,IAAA,CAAAU,IAAA,CAAA7C,SAAA;UACA,IAAAuB,QAAA,GAAA0B,MAAA,CAAAhE,KAAA,CAAAC,IAAA,CAAAsC,OAAA,CAAAqB,IAAA,CAAA7C,SAAA;UACA,IAAAvD,IAAA,IAAA8E,QAAA,CAAA7E,IAAA;UACA,IAAA6E,QAAA,IAAAA,QAAA,CAAAE,OAAA;YACA;YACA,SAAAC,CAAA,GAAAH,QAAA,CAAAI,KAAA,EAAAD,CAAA,MAAAA,CAAA;cACA;cACA,KAAAH,QAAA,CAAAM,MAAA,CAAAJ,OAAA;gBACA;gBACAF,QAAA,GAAAA,QAAA,CAAAM,MAAA;gBACApF,IAAA,CAAAqF,OAAA,CAAAP,QAAA,CAAA7E,IAAA;cACA;YACA;UACA;UACA,IAAAqF,GAAA;UACAtF,IAAA,CAAAuF,OAAA,WAAAC,GAAA;YACAF,GAAA,CAAAI,IAAA,IAAAC,MAAA,CAAAH,GAAA,CAAAnE,KAAA,OAAAsE,MAAA,CAAAH,GAAA,CAAAC,WAAA;UACA;UACAU,gBAAA,CAAAT,IAAA,CAAAJ,GAAA,CAAAO,IAAA;UACAW,MAAA,CAAA5F,WAAA,GAAAuF,gBAAA,CAAAN,IAAA;UACAW,MAAA,CAAAT,KAAA,kBAAAS,MAAA,CAAAzF,cAAA;UACAyF,MAAA,CAAAT,KAAA,oBAAAS,MAAA,CAAA5F,WAAA;UACA4F,MAAA,CAAA3F,kBAAA;QACA;MACA;IACA;IACA4F,eAAA,WAAAA,gBAAAxG,IAAA;MAAA,IAAAyG,MAAA;MACA,SAAA1E,YAAA;QACA;MACA;MACA,IAAA/B,IAAA,CAAAsD,SAAA,CAAAoD,OAAA;QACAxC,UAAA;UACAuC,MAAA,CAAAlE,KAAA,CAAAC,IAAA,CAAAmE,aAAA,CAAAF,MAAA,CAAAtG,YAAA;QACA;MACA;QACA,KAAAM,aAAA,IAAAT,IAAA;QACA,KAAAG,YAAA,GAAAH,IAAA,CAAAsD,SAAA;QACA,KAAAwC,KAAA,aAAA9F,IAAA;MACA;IACA;IACA;IACAyE,QAAA,WAAAA,SAAAvB,IAAA,EAAA0D,OAAA;MAAA,IAAAC,MAAA;MACA,IAAA3D,IAAA,CAAA+B,KAAA;QACA,KAAAxB,mBAAA;QACA;MACA;MACA,KAAApD,KAAA,GAAA6C,IAAA;MACA,KAAA5C,QAAA,GAAAsG,OAAA;MACA,KAAAjD,IAAA,MAAA3B,MAAA;QACA4B,QAAA,EAAAV,IAAA,CAAAlD,IAAA,CAAAsD;MACA,GAAAO,IAAA,WAAAQ,GAAA;QACA;QACA,IAAAyC,aAAA,GAAAD,MAAA,CAAA/D,eAAA,CAAAuB,GAAA;QACAuC,OAAA,CAAAE,aAAA;QACA,IAAAD,MAAA,CAAA9E,YAAA;UACAmC,UAAA;YACA2C,MAAA,CAAAtE,KAAA,CAAAC,IAAA,CAAAC,cAAA,EAAAoE,MAAA,CAAAtF,KAAA;UACA;QACA;UACA2C,UAAA;YACA2C,MAAA,CAAAtE,KAAA,CAAAC,IAAA,CAAAmE,aAAA,CAAAE,MAAA,CAAA1G,YAAA;UACA;QACA;MACA;IACA;EACA;AACA"}]}