{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue", "mtime": 1754276220635}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\nexport default {\r\n  name: 'callout',\r\n  components: {\r\n  },\r\n  props: {\r\n    areaArr: Array,\r\n  },\r\n  data() {\r\n    return {\r\n      imgForm: {},\r\n      imgModal: false,\r\n      imgOkLoading: false,\r\n      imgFormValidate: {},\r\n\r\n      isOpenLike: false,\r\n      imgUrl: '',\r\n      roomNameList: [],\r\n      spinShow: false,\r\n      imgPath: '',\r\n      finalArr: [], // 绝对坐标数组，用于绘制\r\n      finalArrRelative: [], // 相对坐标数组，用于保存\r\n      dataArr: [],\r\n      isShow: false,\r\n      canvas: '',\r\n      ctx: '',\r\n      ctxX: 0,\r\n      ctxY: 0,\r\n      lineWidth: 3,\r\n      type: 'L',\r\n      typeOption: [\r\n        {label: '线', value: 'L'},\r\n      // { label: '矩形', value: 'R' }\r\n      //   {label: \"箭头\", value: \"A\"},\r\n      //   {label: \"文字\", value: \"T\"},\r\n      ],\r\n      canvasHistory: [],\r\n      step: 0,\r\n      loading: false,\r\n      fillStyle: '#CB0707',\r\n      strokeStyle: '#1E90FF',\r\n      lineNum: 2,\r\n      linePeak: [],\r\n      lineStep: 2,\r\n      ellipseR: 0.5,\r\n      dialogVisible: false,\r\n      isUnfold: true,\r\n      fontSize: 18,\r\n      fontColor: '#333333',\r\n      fontFamily: 'Microsoft YaHei, Arial, sans-serif',\r\n      img: new Image(),\r\n      hoveredAreaIndex: -1, // 当前鼠标悬停的区域索引\r\n      highlightColor: '#2E8AE6',\r\n      deleteMode: false, // 是否处于删除模式\r\n      lastHoveredAreaIndex: -1, // 用于表格行悬停时记录之前高亮的区域索引\r\n      zoomLevel: 1,\r\n      initialZoomLevel: 1,\r\n      defaultZoomFactor: 1, // 增大默认缩放系数到1.5\r\n      showAreaNames: true,\r\n      // 区域表单相关\r\n      areaFormVisible: false,\r\n      areaFormLoading: false,\r\n      areaForm: {\r\n        roomName: '',\r\n        roomType: '',\r\n        remark: ''\r\n      },\r\n      areaFormRules: {\r\n        roomName: [\r\n          {required: true, message: '请输入名称', trigger: 'blur'}\r\n        ]\r\n      },\r\n      tempAreaData: null, // 临时存储新绘制的区域数据\r\n      debounceTimer: null, // 添加防抖定时器\r\n      // 滚动控制相关\r\n      scrollLeftBtn: null,\r\n      scrollRightBtn: null,\r\n      // 保存当前drawingId，用于刷新页面时恢复\r\n      currentDrawingId: ''\r\n    };\r\n  },\r\n  computed: {\r\n    defaultZoomText() {\r\n      return `${Math.round(this.defaultZoomFactor * 100)}%`;\r\n    },\r\n    // 判断是否需要显示滚动按钮\r\n    showScrollButtons() {\r\n      if (!this.$refs.canvasContainer || !this.canvas) {\r\n        return false;\r\n      }\r\n      // 当画布宽度大于容器宽度时显示滚动按钮\r\n      return this.canvas.width * this.zoomLevel > this.$refs.canvasContainer.clientWidth;\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL参数或localStorage获取drawingId\r\n    const drawingId = this.$route.query.drawingId || localStorage.getItem('currentDrawingId');\r\n\r\n    // 如果有drawingId，保存到组件状态和localStorage\r\n    if (drawingId) {\r\n      this.currentDrawingId = drawingId;\r\n      localStorage.setItem('currentDrawingId', drawingId);\r\n\r\n      // 如果URL中没有drawingId但localStorage有，则更新URL\r\n      if (!this.$route.query.drawingId) {\r\n        this.$router.replace({\r\n          query: {...this.$route.query, drawingId}\r\n        });\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化监听器\r\n    window.removeEventListener('resize', this.checkScrollNeeded);\r\n  },\r\n  mounted() {\r\n    // 添加窗口大小变化监听器\r\n    window.addEventListener('resize', this.checkScrollNeeded);\r\n\r\n    // 获取drawingId并加载数据\r\n    const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n    if (drawingId) {\r\n      this.getList();\r\n    } else {\r\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\r\n      setTimeout(() => {\r\n        this.goBack();\r\n      }, 2000);\r\n    }\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        this.$message.warning('缺少图纸ID参数');\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceDrawingManagement-get']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        console.log(data, 'data');\r\n        if (data) {\r\n          // 获取base64图片数据\r\n          const drawingData = data;\r\n          if (drawingData.drawingInfo) {\r\n            // 设置图片路径为base64数据\r\n            this.imgPath = drawingData.drawingInfo;\r\n            // 图纸基本信息\r\n            this.imgUrl = drawingData.drawingInfo;\r\n\r\n            // 初始化画布，渲染图片\r\n            this.init();\r\n\r\n            // 图片渲染完成后，获取已有区域数据\r\n            this.getRegionList();\r\n          } else {\r\n            this.$message.error('图片数据不存在');\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取图纸数据失败:', error);\r\n        this.$message.error('获取图纸数据失败');\r\n      });\r\n    },\r\n\r\n    // 获取已有区域数据\r\n    getRegionList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceRegion-list']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        if (data && data.length > 0) {\r\n          // 将区域信息转换为需要的格式，并保存原始数据中的ID\r\n          this.roomNameList = data.map(region => ({\r\n            roomName: region.regionName,\r\n            roomType: '',\r\n            remark: region.remark || '',\r\n            id: region.id || null // 保存原始区域ID，用于后续删除操作\r\n          }));\r\n\r\n          // 解析区域坐标数据\r\n          const areaCoordinates = data.map(region => {\r\n            try {\r\n              return JSON.parse(region.pointJson || '[]');\r\n            } catch (e) {\r\n              console.error('Error parsing pointJson:', e);\r\n              return [];\r\n            }\r\n          }).filter(coords => coords.length > 0);\r\n\r\n          // 保存相对坐标数据\r\n          this.finalArrRelative = [...areaCoordinates];\r\n\r\n          // 处理区域数据并绘制到画布上\r\n          if (areaCoordinates.length > 0) {\r\n            // 转换成绝对坐标并保存\r\n            this.finalArr = this.processAreaData(areaCoordinates);\r\n\r\n            // 绘制所有区域\r\n            this.redrawCanvas();\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取区域数据失败:', error);\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\r\n    },\r\n    editAreaByIndex(index) {\r\n\r\n    },\r\n    handleFileChange(file) {\r\n      const rawFile = file.raw;\r\n      if (!rawFile) {\r\n        return;\r\n      }\r\n      const reader = new FileReader();\r\n      reader.onload = e => {\r\n        this.imgPath = e.target.result;\r\n        this.spinShow = true;\r\n        this.init();\r\n      };\r\n      reader.readAsDataURL(rawFile);\r\n    },\r\n    // 检查是否需要滚动按钮\r\n    checkScrollNeeded() {\r\n      if (this.$refs.canvasContainer && this.canvas) {\r\n      // 通过Vue的响应式系统触发计算属性重新计算\r\n        this.$forceUpdate();\r\n      // 不再需要调整容器高度\r\n      // this.adjustContainerHeight();\r\n      }\r\n    },\r\n    // 不再需要调整容器高度的方法\r\n    /*\r\n  adjustContainerHeight() {\r\n    if (this.$refs.canvasContainer && this.canvas) {\r\n      const canvasHeight = this.canvas.height * this.zoomLevel;\r\n      // 设置容器高度为画布高度加上一些边距\r\n      this.$refs.canvasContainer.style.height = `${canvasHeight + 20}px`;\r\n    }\r\n  },\r\n  */\r\n    // 向左滚动画布\r\n    scrollLeft() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft -= scrollAmount;\r\n      }\r\n    },\r\n    // 向右滚动画布\r\n    scrollRight() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft += scrollAmount;\r\n      }\r\n    },\r\n    init() {\r\n      let _this = this;\r\n      let image = new Image();\r\n      image.setAttribute('crossOrigin', 'anonymous');\r\n      image.src = this.imgPath;\r\n      image.onload = function () {\r\n      // 图片加载完，再draw 和 toDataURL\r\n        if (image.complete) {\r\n          _this.spinShow = false;\r\n          _this.img = image;\r\n          let content = _this.$refs.content;\r\n          _this.canvas = document.createElement('canvas');\r\n          _this.canvas.height = _this.img.height;\r\n          _this.canvas.width = _this.img.width;\r\n          _this.canvas.setAttribute('style', 'border:2px solid red;');\r\n          _this.canvas.setAttribute('id', 'myCanvas');\r\n          _this.ctx = _this.canvas.getContext('2d');\r\n          _this.ctx.globalAlpha = 1;\r\n          _this.ctx.drawImage(_this.img, 0, 0);\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.ctx.globalCompositeOperation = _this.type;\r\n\r\n          // 清空之前的内容\r\n          content.innerHTML = '';\r\n          content.appendChild(_this.canvas);\r\n\r\n          // 重置交互状态\r\n          _this.deleteMode = false;\r\n          _this.hoveredAreaIndex = -1;\r\n\r\n          // 预设最小缩放比例，防止图片太小\r\n          _this.defaultZoomFactor = Math.max(_this.defaultZoomFactor, 1.2);\r\n\r\n          // 自动适应屏幕\r\n          _this.$nextTick(() => {\r\n            _this.autoFit();\r\n            // 不再需要调整容器高度\r\n            // _this.adjustContainerHeight();\r\n            // 检查是否需要滚动按钮\r\n            _this.checkScrollNeeded();\r\n          });\r\n\r\n          _this.bindEventLisner();\r\n\r\n          if (_this.areaArr) {\r\n            _this.finalArr = _this.processAreaData(_this.areaArr);\r\n            _this.finalArr.forEach(i => {\r\n              _this.createL2(i);\r\n            });\r\n          }\r\n        }\r\n      };\r\n    },\r\n    radioClick(item) {\r\n      if (item != 'T') {\r\n        this.txtBlue();\r\n        this.resetTxt();\r\n      }\r\n    },\r\n    // 下载画布\r\n    downLoad() {\r\n      let _this = this;\r\n      let url = _this.canvas.toDataURL('image/png');\r\n      let fileName = 'canvas.png';\r\n      if ('download' in document.createElement('a')) {\r\n      // 非IE下载\r\n        const elink = document.createElement('a');\r\n        elink.download = fileName;\r\n        elink.style.display = 'none';\r\n        elink.href = url;\r\n        document.body.appendChild(elink);\r\n        elink.click();\r\n        document.body.removeChild(elink);\r\n      } else {\r\n      // IE10+下载\r\n        navigator.msSaveBlob(url, fileName);\r\n      }\r\n    },\r\n    // 重置所有内容\r\n    resetAll() {\r\n      this.dataArr = [];\r\n      this.finalArr = [];\r\n      this.finalArrRelative = []; // 同时清空相对坐标数组\r\n      this.roomNameList = [];\r\n      this.$emit('getPointArr', this.finalArr);\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.canvasHistory = [];\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.canvasHistory.push(this.canvas.toDataURL());\r\n      this.step = 0;\r\n      this.resetTxt();\r\n      this.hoveredAreaIndex = -1;\r\n      this.deleteMode = false;\r\n      // 应用缩放\r\n      this.applyZoom();\r\n    },\r\n    // 保存区域规划\r\n    save() {\r\n    // this.finalArr\r\n    },\r\n    // 清空当前画布\r\n    reset() {\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.resetTxt();\r\n    },\r\n    // 撤销方法\r\n    repeal() {\r\n      let _this = this;\r\n      if (this.isShow) {\r\n        _this.resetTxt();\r\n        _this._repeal();\r\n      } else {\r\n        _this._repeal();\r\n      }\r\n    },\r\n    _repeal() {\r\n      if (this.step >= 1) {\r\n        this.step = this.step - 1;\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n          this.loading = true;\r\n        });\r\n      } else {\r\n        this.$message.warning('不能再继续撤销了');\r\n      }\r\n    },\r\n    // 恢复方法\r\n    canvasRedo() {\r\n      if (this.step < this.canvasHistory.length - 1) {\r\n        if (this.step == 0) {\r\n          this.step = 1;\r\n        } else {\r\n          this.step++;\r\n        }\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n        });\r\n      } else {\r\n        this.$message.warning('已经是最新的记录了');\r\n      }\r\n    },\r\n    // 绘制历史数组中的最后一个\r\n    rebroadcast() {\r\n      let canvasPic = new Image();\r\n      canvasPic.src = this.canvasHistory[this.step];\r\n      canvasPic.addEventListener('load', () => {\r\n        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n        this.ctx.drawImage(canvasPic, 0, 0);\r\n        this.loading = true;\r\n      });\r\n    },\r\n    // 绑定事件,判断分支\r\n    bindEventLisner() {\r\n      let _this = this;\r\n      let r1, r2; // 绘制圆形，矩形需要\r\n      this.canvas.addEventListener('click', function (e) {\r\n        if (_this.type == 'L' && e.button == 0) {\r\n          const coords = _this.getAdjustedCoordinates(e);\r\n          // 如果处于删除模式，检查是否点击在区域内\r\n          if (_this.deleteMode) {\r\n            _this.checkAndDeleteArea(coords);\r\n          } else {\r\n            _this.createL(coords, 'begin');\r\n          }\r\n        }\r\n      });\r\n      this.canvas.oncontextmenu = function (e) {\r\n        const coords = _this.getAdjustedCoordinates(e);\r\n        _this.createL(coords, 'end');\r\n        return false;\r\n      };\r\n\r\n      // 添加鼠标移动事件监听器，用于区域高亮，使用防抖处理\r\n      this.canvas.addEventListener('mousemove', function (e) {\r\n      // 清除之前的定时器\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        // 创建新的定时器，延迟执行高亮逻辑\r\n        _this.debounceTimer = setTimeout(() => {\r\n          if (_this.finalArr && _this.finalArr.length > 0) {\r\n            const coords = _this.getAdjustedCoordinates(e);\r\n            let isOverArea = false;\r\n            let hoveredIndex = -1;\r\n\r\n            // 检查鼠标是否在任何一个区域内\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.isPointInPolygon(coords, _this.finalArr[i])) {\r\n                hoveredIndex = i;\r\n                isOverArea = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            // 只有当悬停状态发生变化时才重绘\r\n            if (_this.hoveredAreaIndex !== hoveredIndex) {\r\n              _this.hoveredAreaIndex = hoveredIndex;\r\n              _this.redrawWithHighlight();\r\n              document.body.style.cursor = isOverArea ? 'pointer' : 'default';\r\n            }\r\n          }\r\n        }, 10); // 10毫秒的防抖延迟，平衡响应性和性能\r\n      });\r\n\r\n      // 添加鼠标离开画布事件\r\n      this.canvas.addEventListener('mouseleave', function () {\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        if (_this.hoveredAreaIndex !== -1) {\r\n          _this.hoveredAreaIndex = -1;\r\n          _this.redrawCanvas();\r\n          document.body.style.cursor = 'default';\r\n        }\r\n      });\r\n    },\r\n    // 判断点是否在多边形内（原有方法，保留但不使用）\r\n    judge(dot, coordinates) {\r\n      return this.isPointInPolygon(dot, coordinates);\r\n    },\r\n    // 绘制线条\r\n    createL(coords, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 点击开始绘制前，检查点击位置是否在已有区域内\r\n        const mousePoint = {x: coords.x, y: coords.y};\r\n        for (let i = 0; i < this.finalArr.length; i++) {\r\n          if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n            this.$message.warning('您点击的位置已在其他区域内，请选择其他位置绘制');\r\n            return; // 阻止继续执行\r\n          }\r\n        }\r\n\r\n        if (_this.dataArr && _this.dataArr.length === 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.beginPath();\r\n          _this.ctx.moveTo(coords.x, coords.y);\r\n          _this.ctx.lineTo(coords.x + 1, coords.y + 1);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        } else if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.lineTo(coords.x, coords.y);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        }\r\n      } else if (status == 'end') {\r\n        if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.ctx.moveTo(\r\n            _this.dataArr[_this.dataArr.length - 1].x,\r\n            _this.dataArr[_this.dataArr.length - 1].y\r\n          );\r\n          _this.ctx.lineTo(_this.dataArr[0].x, _this.dataArr[0].y);\r\n          _this.ctx.stroke();\r\n          _this.ctx.closePath();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step;\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          if (_this.dataArr && _this.dataArr.length < 3) {\r\n            _this.$message.info('该区域点数少于三个，不保存');\r\n            _this._repeal();\r\n          } else {\r\n          // 检查新绘制的区域是否与现有区域有重叠\r\n            const newArea = _this.dataArr;\r\n            let hasOverlap = false;\r\n\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.checkPolygonsOverlap(newArea, _this.finalArr[i])) {\r\n                hasOverlap = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            if (hasOverlap) {\r\n              _this.$message.warning('新绘制的区域与现有区域重叠，请重新绘制');\r\n              _this._repeal();\r\n            } else {\r\n            // 存储新绘制的区域数据，等待表单提交后再保存\r\n              _this.tempAreaData = {\r\n                area: _this.dataArr,\r\n                relativeArea: _this.dataArr.map(point => _this.toRelativeCoordinates(point))\r\n              };\r\n\r\n              // 打开区域信息表单\r\n              _this.areaForm = {\r\n                roomName: `区域${_this.roomNameList.length + 1}`,\r\n                roomType: '',\r\n                remark: ''\r\n              };\r\n              _this.areaFormVisible = true;\r\n            }\r\n          }\r\n          this.$emit('getPointArr', _this.finalArr);\r\n          _this.dataArr = [];\r\n          _this.canvas.onmousemove = null;\r\n        }\r\n      }\r\n    },\r\n    // 重绘画布并高亮显示悬停区域\r\n    redrawWithHighlight() {\r\n    // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 重绘背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i], i === this.hoveredAreaIndex);\r\n      }\r\n    },\r\n\r\n    // 统一的区域绘制函数\r\n    drawArea(area, isHighlighted = false) {\r\n      if (!area || area.length < 3) {\r\n        return;\r\n      }\r\n\r\n      this.ctx.beginPath();\r\n      this.ctx.moveTo(area[0].x, area[0].y);\r\n\r\n      // 绘制区域轮廓\r\n      for (let i = 1; i < area.length; i++) {\r\n        this.ctx.lineTo(area[i].x, area[i].y);\r\n      }\r\n\r\n      // 闭合路径\r\n      this.ctx.lineTo(area[0].x, area[0].y);\r\n\r\n      // 设置样式\r\n      if (isHighlighted) {\r\n      // 高亮显示 - 增加线宽，使用更深的蓝色\r\n        this.ctx.strokeStyle = this.highlightColor;\r\n        this.ctx.lineWidth = this.lineWidth + 2;\r\n\r\n        // 填充半透明颜色\r\n        this.ctx.fillStyle = this.highlightColor + '22'; // 添加22作为透明度 (13%)\r\n        this.ctx.fill();\r\n      } else {\r\n        this.ctx.strokeStyle = this.strokeStyle;\r\n        this.ctx.lineWidth = this.lineWidth;\r\n      }\r\n\r\n      this.ctx.stroke();\r\n      this.ctx.closePath();\r\n\r\n      // 如果启用了显示区域名称，并且能找到对应的名\r\n      const areaIndex = this.finalArr.indexOf(area);\r\n      if (this.showAreaNames && areaIndex >= 0 && this.roomNameList[areaIndex]) {\r\n        this.drawAreaName(area, this.roomNameList[areaIndex].roomName);\r\n      }\r\n    },\r\n\r\n    // 绘制区域名称\r\n    drawAreaName(area, name) {\r\n      if (!area || area.length < 3 || !name) {\r\n        return;\r\n      }\r\n\r\n      // 计算区域的中心点\r\n      let centerX = 0, centerY = 0;\r\n      for (let i = 0; i < area.length; i++) {\r\n        centerX += area[i].x;\r\n        centerY += area[i].y;\r\n      }\r\n      centerX /= area.length;\r\n      centerY /= area.length;\r\n\r\n      // 保存当前上下文状态\r\n      this.ctx.save();\r\n\r\n      // 设置固定的字体大小，不受图片尺寸和缩放影响\r\n      // 首先获取画布的尺寸\r\n      const canvasWidth = this.canvas.width;\r\n      const canvasHeight = this.canvas.height;\r\n\r\n      // 计算固定的字体大小，基于画布尺寸的一个合适比例\r\n      // 这样可以确保在不同大小的图片上，字体大小相对于图片尺寸的比例是一致的\r\n      const baseFontSize = 36; // 固定基础字体大小\r\n\r\n      // 应用字体设置\r\n      this.ctx.font = `bold ${baseFontSize}px ${this.fontFamily}`;\r\n      this.ctx.textAlign = 'center';\r\n      this.ctx.textBaseline = 'middle';\r\n\r\n      // 测量文本宽度\r\n      const textWidth = this.ctx.measureText(name).width;\r\n\r\n      // 增强文字阴影以提高可读性\r\n      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';\r\n      this.ctx.shadowBlur = 4;\r\n      this.ctx.shadowOffsetX = 1;\r\n      this.ctx.shadowOffsetY = 1;\r\n\r\n      // 使用更亮的蓝色，但与线条颜色(#1E90FF)有区别\r\n      this.ctx.fillStyle = '#38B0DE'; // 天蓝色，比线条颜色略微偏青一些\r\n      this.ctx.fillText(name, centerX, centerY);\r\n\r\n      // 恢复上下文状态\r\n      this.ctx.restore();\r\n    },\r\n\r\n    // 绘制圆角矩形\r\n    roundRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {\r\n      if (typeof radius === 'undefined') {\r\n        radius = 5;\r\n      }\r\n      if (typeof radius === 'number') {\r\n        radius = {tl: radius, tr: radius, br: radius, bl: radius};\r\n      } else {\r\n        const defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};\r\n        for (const side in defaultRadius) {\r\n          radius[side] = radius[side] || defaultRadius[side];\r\n        }\r\n      }\r\n\r\n      ctx.beginPath();\r\n      ctx.moveTo(x + radius.tl, y);\r\n      ctx.lineTo(x + width - radius.tr, y);\r\n      ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);\r\n      ctx.lineTo(x + width, y + height - radius.br);\r\n      ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);\r\n      ctx.lineTo(x + radius.bl, y + height);\r\n      ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);\r\n      ctx.lineTo(x, y + radius.tl);\r\n      ctx.quadraticCurveTo(x, y, x + radius.tl, y);\r\n      ctx.closePath();\r\n\r\n      if (fillColor) {\r\n        ctx.fillStyle = fillColor;\r\n        ctx.fill();\r\n      }\r\n\r\n      if (strokeColor) {\r\n        ctx.strokeStyle = strokeColor;\r\n        ctx.lineWidth = 1;\r\n        ctx.stroke();\r\n      }\r\n    },\r\n\r\n    // 重绘画布\r\n    redrawCanvas() {\r\n      if (!this.canvas || !this.ctx) {\r\n        return;\r\n      }\r\n\r\n      // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 绘制背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有保留的区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i]);\r\n      }\r\n    },\r\n\r\n    // 创建区域2（读取已有区域）\r\n    createL2(e) {\r\n      let _this = this;\r\n\r\n      // 使用统一的绘制函数\r\n      this.drawArea(e);\r\n\r\n      _this.step = _this.step + 1;\r\n      if (_this.step < _this.canvasHistory.length - 1) {\r\n        _this.canvasHistory.length = _this.step;\r\n      }\r\n      _this.canvasHistory.push(_this.canvas.toDataURL());\r\n      this.$emit('getPointArr', _this.finalArr);\r\n    },\r\n    // 绘制矩形\r\n    createR(e, status, r1, r2) {\r\n      let _this = this;\r\n      let r;\r\n      if (status == 'begin') {\r\n      // console.log('onmousemove')\r\n        _this.canvas.onmousemove = function (e) {\r\n          _this.reset();\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n\r\n          // 保留之前绘画的图形\r\n          if (_this.step !== 0) {\r\n            let canvasPic = new Image();\r\n            canvasPic.src = _this.canvasHistory[_this.step];\r\n            _this.ctx.drawImage(canvasPic, 0, 0);\r\n          }\r\n\r\n          _this.ctx.beginPath();\r\n          _this.ctx.strokeRect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n        };\r\n      } else if (status == 'end') {\r\n        _this.rebroadcast();\r\n        let interval = setInterval(() => {\r\n          if (_this.loading) {\r\n            clearInterval(interval);\r\n            _this.loading = false;\r\n          } else {\r\n            return;\r\n          }\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n          _this.ctx.beginPath();\r\n          _this.ctx.rect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step; // 截断数组\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.canvas.onmousemove = null;\r\n        }, 1);\r\n      }\r\n    },\r\n\r\n    // 绘制箭头\r\n    drawArrow(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 获取起始位置\r\n        _this.arrowFromX = e.layerX;\r\n        _this.arrowFromY = e.layerY;\r\n        _this.ctx.beginPath();\r\n        _this.ctx.moveTo(e.layerX, e.layerY);\r\n      } else if (status == 'end') {\r\n      // 计算箭头及画线\r\n        let toX = e.layerX;\r\n        let toY = e.layerY;\r\n        let theta = 30;\r\n        let headlen = 10;\r\n        let _this = this;\r\n        let fromX = this.arrowFromX;\r\n        let fromY = this.arrowFromY;\r\n        // 计算各角度和对应的P2,P3坐标\r\n        let angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;\r\n        let angle1 = ((angle + theta) * Math.PI) / 180;\r\n        let angle2 = ((angle - theta) * Math.PI) / 180;\r\n        let topX = headlen * Math.cos(angle1);\r\n        let topY = headlen * Math.sin(angle1);\r\n        let botX = headlen * Math.cos(angle2);\r\n        let botY = headlen * Math.sin(angle2);\r\n        let arrowX = fromX - topX;\r\n        let arrowY = fromY - topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.moveTo(fromX, fromY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + topX;\r\n        arrowY = toY + topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + botX;\r\n        arrowY = toY + botY;\r\n        _this.ctx.lineTo(arrowX, arrowY);\r\n        _this.ctx.strokeStyle = _this.strokeStyle;\r\n        _this.ctx.lineWidth = _this.lineWidth;\r\n        _this.ctx.stroke();\r\n\r\n        _this.ctx.closePath();\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n\r\n    // 文字输入\r\n    createT(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 初始化文字输入相关参数\r\n        _this.isTextInputMode = true;\r\n      } else if (status == 'end') {\r\n        let offset = 0;\r\n        if (_this.fontSize >= 28) {\r\n          offset = _this.fontSize / 2 - 3;\r\n        } else {\r\n          offset = _this.fontSize / 2 - 2;\r\n        }\r\n\r\n        _this.ctxX = e.layerX + 2;\r\n        _this.ctxY = e.layerY + offset;\r\n\r\n        let index = this.getPointOnCanvas(e);\r\n        _this.$refs.txt.style.left = index.x + 'px';\r\n        _this.$refs.txt.style.top = index.y - _this.fontSize / 2 + 'px';\r\n        _this.$refs.txt.value = '';\r\n        _this.$refs.txt.style.height = _this.fontSize + 'px';\r\n        (_this.$refs.txt.style.width\r\n          = _this.canvas.width - e.layerX - 1 + 'px'),\r\n        (_this.$refs.txt.style.fontSize = _this.fontSize + 'px');\r\n        _this.$refs.txt.style.fontFamily = _this.fontFamily;\r\n        _this.$refs.txt.style.color = _this.fontColor;\r\n        _this.$refs.txt.style.maxlength = Math.floor(\r\n          (_this.canvas.width - e.layerX) / _this.fontSize\r\n        );\r\n        _this.isShow = true;\r\n        setTimeout(() => {\r\n          _this.$refs.txt.focus();\r\n        });\r\n      }\r\n    },\r\n    // 文字输入框失去光标时在画布上生成文字\r\n    txtBlue() {\r\n      let _this = this;\r\n      let txt = _this.$refs.txt.value;\r\n      if (txt) {\r\n        _this.ctx.font\r\n          = _this.$refs.txt.style.fontSize\r\n          + ' '\r\n          + _this.$refs.txt.style.fontFamily;\r\n        _this.ctx.fillStyle = _this.$refs.txt.style.color;\r\n        _this.ctx.fillText(txt, _this.ctxX, _this.ctxY);\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n    // 计算文字框定位位置\r\n    getPointOnCanvas(e) {\r\n      let cs = this.canvas;\r\n      let content = document.getElementsByClassName('content')[0];\r\n      return {\r\n        x: e.layerX + (content.clientWidth - cs.width) / 2,\r\n        y: e.layerY,\r\n      };\r\n    },\r\n    // 清空文字\r\n    resetTxt() {\r\n      let _this = this;\r\n      _this.$refs.txt.value = '';\r\n      _this.isShow = false;\r\n    },\r\n    exportJson() {\r\n      // 直接使用已经准备好的相对坐标数组\r\n      let exportArr = JSON.parse(JSON.stringify(this.finalArrRelative));\r\n\r\n      // 获取URL中的drawingId参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      // 准备导出数据，使用正确的字段名\r\n      let maintenanceRegionList = this.roomNameList.map((room, index) => {\r\n        return {\r\n          id: room.id || null,\r\n          regionName: room.roomName, // 使用regionName作为区域名称字段\r\n          pointJson: JSON.stringify(exportArr[index]), // 使用pointJson作为区域坐标JSON字段\r\n          remark: room.remark || '', // 备注字段\r\n          drawingId: drawingId // 从URL参数获取的图纸ID\r\n        };\r\n      });\r\n\r\n      // 如果没有区域数据，提示用户\r\n      if (maintenanceRegionList.length === 0) {\r\n        this.$message.warning('尚未绘制任何区域，无法保存');\r\n        return;\r\n      }\r\n\r\n      // 二次确认保存操作\r\n      this.$confirm('确认保存当前绘制的所有区域？', '保存确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 用户确认，调用保存接口\r\n        console.log('保存的区域数据:', maintenanceRegionList);\r\n\r\n        // 调用保存接口，传入整合后的区域列表\r\n        this.$api['areaManagement/maintenanceRegion-save']({\r\n          maintenanceRegionList: maintenanceRegionList\r\n        }).then(data => {\r\n          this.$message.success('保存成功');\r\n        }).catch(error => {\r\n          this.$message.error('保存失败，请重试');\r\n        });\r\n      });\r\n    },\r\n    ToBase64() {\r\n      let that = this;\r\n      var img = document.getElementById('imgfile');\r\n      var imgFile = new FileReader();\r\n      imgFile.readAsDataURL(img.files[0]);\r\n\r\n      imgFile.onload = function () {\r\n        var imgData = this.result; // base64数据\r\n        that.imgPath = imgData;\r\n        that.init();\r\n      };\r\n    },\r\n    handleMultiUpload(v) {\r\n      this.imgUrl = v[0];\r\n    },\r\n    handleMultiUpload2(v) {\r\n      this.imgForm.imgUrl = v[0];\r\n    },\r\n    changeImgUrl() {\r\n      this.imgForm = {\r\n        floorNo: '',\r\n        imgUrl: ''\r\n      };\r\n      this.imgModal = true;\r\n    },\r\n    imgOk() {\r\n      this.$refs.imgForm.validate(valid => {\r\n        if (valid) {\r\n          this.imgOkLoading = true;\r\n          this.$api['iotHome/changeFloorMap'](this.imgForm)\r\n            .then(data => {\r\n              this.$message.success('更换成功');\r\n              this.imgModal = false;\r\n            })\r\n            .finally(() => {\r\n              this.imgOkLoading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    imgCancel() {\r\n    // 取消关闭\r\n      this.imgModal = false;\r\n      this.$refs.imgForm.resetFields();\r\n    },\r\n    // 将绝对坐标转换为相对坐标（0-1之间的比例值）\r\n    toRelativeCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: parseFloat((point.x / this.canvas.width).toFixed(4)),\r\n        y: parseFloat((point.y / this.canvas.height).toFixed(4))\r\n      };\r\n    },\r\n\r\n    // 将相对坐标转换为绝对坐标（实际像素值）\r\n    toAbsoluteCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: Math.round(point.x * this.canvas.width),\r\n        y: Math.round(point.y * this.canvas.height)\r\n      };\r\n    },\r\n\r\n    // 判断一个点是否为相对坐标（值在0-1之间）\r\n    isRelativeCoordinate(point) {\r\n      return point.x >= 0 && point.x <= 1 && point.y >= 0 && point.y <= 1;\r\n    },\r\n\r\n    // 确保点使用绝对坐标\r\n    ensureAbsoluteCoordinate(point) {\r\n      if (this.isRelativeCoordinate(point)) {\r\n        return this.toAbsoluteCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 确保点使用相对坐标\r\n    ensureRelativeCoordinate(point) {\r\n      if (!this.isRelativeCoordinate(point)) {\r\n        return this.toRelativeCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 处理输入的区域数据，确保使用正确的坐标类型\r\n    processAreaData(areaData) {\r\n      if (!areaData || !areaData.length) {\r\n        return [];\r\n      }\r\n\r\n      // 深拷贝避免修改原始数据\r\n      const processedData = JSON.parse(JSON.stringify(areaData));\r\n\r\n      // 检查第一个点的第一个坐标，判断是否为相对坐标\r\n      const firstArea = processedData[0];\r\n      if (firstArea && firstArea.length > 0) {\r\n        const firstPoint = firstArea[0];\r\n        const isRelative = this.isRelativeCoordinate(firstPoint);\r\n\r\n        // 如果是相对坐标，转换为绝对坐标用于绘制\r\n        if (isRelative) {\r\n        // 同时保存相对坐标版本\r\n          this.finalArrRelative = JSON.parse(JSON.stringify(processedData));\r\n\r\n          processedData.forEach(area => {\r\n            area.forEach((point, index) => {\r\n              area[index] = this.toAbsoluteCoordinates(point);\r\n            });\r\n          });\r\n        } else {\r\n        // 如果是绝对坐标，生成相对坐标版本\r\n          this.finalArrRelative = processedData.map(area =>\r\n            area.map(point => this.toRelativeCoordinates(point))\r\n          );\r\n        }\r\n      }\r\n\r\n      return processedData;\r\n    },\r\n    deleteAllAreas() {\r\n      if (this.finalArr.length > 0) {\r\n        // 二次确认删除所有区域操作，强调可能影响已绑定设备\r\n        this.$confirm('警告：删除所有区域可能会影响已绑定的设备！确定要删除吗？', '批量删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">严重警告：</strong>删除所有区域将会：</p>\r\n            <ul>\r\n              <li>永久移除所有区域的绘制信息</li>\r\n              <li>导致所有与这些区域关联的设备失去位置信息</li>\r\n              <li>需要重新绘制区域并重新绑定所有设备</li>\r\n            </ul>\r\n            <p>此操作不可恢复，确定要继续删除所有区域吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 收集所有区域的ID\r\n          const regionIds = this.roomNameList\r\n            .filter(room => room.id) // 只收集有ID的区域\r\n            .map(room => room.id);\r\n\r\n          if (regionIds.length > 0) {\r\n            // 有保存过的区域，调用API删除\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: regionIds\r\n            }).then(data => {\r\n              this.$message.success('已删除所有区域');\r\n\r\n              // 清空前端数据\r\n              this.finalArr = [];\r\n              this.finalArrRelative = [];\r\n              this.roomNameList = [];\r\n\r\n              // 重绘画布\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            }).catch(error => {\r\n              this.$message.error('删除失败，请重试');\r\n              console.error('批量删除区域失败:', error);\r\n            });\r\n          } else {\r\n            // 没有保存过的区域，直接清空前端数据\r\n            this.finalArr = [];\r\n            this.finalArrRelative = [];\r\n            this.roomNameList = [];\r\n\r\n            // 重绘画布\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success('已删除所有区域');\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.info('没有可删除的区域');\r\n      }\r\n    },\r\n    // 检查并删除点击的区域\r\n    checkAndDeleteArea(coords) {\r\n      const mousePoint = {x: coords.x, y: coords.y};\r\n\r\n      // 检查点击是否在任何一个区域内\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n        // 弹出确认对话框\r\n          this.$confirm(`确定要删除第${i + 1}个区域吗?`, '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n          // 删除该区域\r\n            this.finalArr.splice(i, 1);\r\n            this.finalArrRelative.splice(i, 1);\r\n\r\n            // 如果有对应的名称，也要删除\r\n            if (this.roomNameList.length > i) {\r\n              this.roomNameList.splice(i, 1);\r\n            }\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除第${i + 1}个区域`);\r\n          });\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 判断点是否在多边形内（射线法）\r\n    isPointInPolygon(point, polygon) {\r\n      if (!polygon || polygon.length < 3) {\r\n        return false;\r\n      }\r\n\r\n      let inside = false;\r\n      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\r\n        const xi = polygon[i].x, yi = polygon[i].y;\r\n        const xj = polygon[j].x, yj = polygon[j].y;\r\n\r\n        const intersect = ((yi > point.y) !== (yj > point.y))\r\n        && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);\r\n\r\n        if (intersect) {\r\n          inside = !inside;\r\n        }\r\n      }\r\n\r\n      return inside;\r\n    },\r\n\r\n\r\n    // 通过表格行点击高亮并选择区域\r\n    highlightArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 通过表格行悬停高亮区域\r\n    mouseoverArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.lastHoveredAreaIndex = this.hoveredAreaIndex;\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 表格行鼠标移出恢复之前的高亮状态\r\n    mouseoutArea(row, column, event) {\r\n      this.hoveredAreaIndex = this.lastHoveredAreaIndex;\r\n      this.redrawCanvas();\r\n    },\r\n\r\n    // 通过索引删除区域\r\n    deleteAreaByIndex(index) {\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        // 二次确认删除操作，强调可能影响已绑定设备\r\n        this.$confirm(`警告：删除\"${this.roomNameList[index].roomName}\"区域可能会影响已绑定的设备！确定要删除吗？`, '删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">警告：</strong>删除\"${this.roomNameList[index].roomName}\"区域将会：</p>\r\n            <ul>\r\n              <li>永久移除该区域的绘制信息</li>\r\n              <li>可能导致与该区域关联的设备失去位置信息</li>\r\n              <li>需要重新绑定相关设备到其他区域</li>\r\n            </ul>\r\n            <p>确定要继续删除操作吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 获取要删除的区域ID\r\n          const regionId = this.roomNameList[index].id;\r\n\r\n          // 如果有ID，调用API删除\r\n          if (regionId) {\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: [regionId]\r\n            }).then(data => {\r\n              this.$message.success(`已删除区域`);\r\n\r\n              // 删除前端数据\r\n              this.finalArr.splice(index, 1);\r\n              this.finalArrRelative.splice(index, 1);\r\n              this.roomNameList.splice(index, 1);\r\n\r\n              // 重绘画布\r\n              this.hoveredAreaIndex = -1;\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            });\r\n          } else {\r\n            // 新绘制的区域没有ID，直接从前端删除\r\n            this.finalArr.splice(index, 1);\r\n            this.finalArrRelative.splice(index, 1);\r\n            this.roomNameList.splice(index, 1);\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除区域`);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查两个多边形是否重叠\r\n    checkPolygonsOverlap(poly1, poly2) {\r\n    // 简单实现：检查每个多边形的点是否在另一个多边形内\r\n    // 或者检查多边形的边是否相交\r\n\r\n      // 检查poly1的点是否在poly2内\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        if (this.isPointInPolygon(poly1[i], poly2)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查poly2的点是否在poly1内\r\n      for (let i = 0; i < poly2.length; i++) {\r\n        if (this.isPointInPolygon(poly2[i], poly1)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查边是否相交\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        const a1 = poly1[i];\r\n        const a2 = poly1[(i + 1) % poly1.length];\r\n\r\n        for (let j = 0; j < poly2.length; j++) {\r\n          const b1 = poly2[j];\r\n          const b2 = poly2[(j + 1) % poly2.length];\r\n\r\n          if (this.doLinesIntersect(a1, a2, b1, b2)) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 检查两条线段是否相交\r\n    doLinesIntersect(p1, p2, p3, p4) {\r\n    // 线段1的方向\r\n      const d1x = p2.x - p1.x;\r\n      const d1y = p2.y - p1.y;\r\n\r\n      // 线段2的方向\r\n      const d2x = p4.x - p3.x;\r\n      const d2y = p4.y - p3.y;\r\n\r\n      // 行列式\r\n      const denominator = d2y * d1x - d2x * d1y;\r\n\r\n      // 如果行列式为0，则线段平行或共线\r\n      if (denominator === 0) {\r\n        return false;\r\n      }\r\n\r\n      // 参数t和u\r\n      const u_a = (d2x * (p1.y - p3.y) - d2y * (p1.x - p3.x)) / denominator;\r\n      const u_b = (d1x * (p1.y - p3.y) - d1y * (p1.x - p3.x)) / denominator;\r\n\r\n      // 如果t和u都在[0,1]范围内，则线段相交\r\n      return (u_a >= 0 && u_a <= 1 && u_b >= 0 && u_b <= 1);\r\n    },\r\n\r\n    // 放大画布\r\n    zoomIn() {\r\n      this.zoomLevel *= 1.1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 缩小画布\r\n    zoomOut() {\r\n      this.zoomLevel *= 0.9;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 重置缩放\r\n    resetZoom() {\r\n      this.zoomLevel = this.initialZoomLevel || 1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 应用缩放\r\n    applyZoom() {\r\n      if (!this.canvas) {\r\n        return;\r\n      }\r\n\r\n      // 应用缩放变换\r\n      this.canvas.style.transform = `scale(${this.zoomLevel})`;\r\n      this.canvas.style.transformOrigin = 'top left';\r\n\r\n      // 重绘以更新区域名称大小\r\n      this.redrawCanvas();\r\n\r\n      // 检查是否需要滚动按钮\r\n      this.checkScrollNeeded();\r\n\r\n    // 不再需要调整容器高度\r\n    // this.adjustContainerHeight();\r\n    },\r\n\r\n    // 自适应屏幕\r\n    autoFit() {\r\n      if (!this.canvas || !this.$refs.canvasContainer) {\r\n        return;\r\n      }\r\n\r\n      const container = this.$refs.canvasContainer;\r\n      const containerWidth = container.clientWidth;\r\n      const containerHeight = window.innerHeight - 100; // 减去头部和边距\r\n\r\n      const imgRatio = this.canvas.width / this.canvas.height;\r\n      const containerRatio = containerWidth / containerHeight;\r\n\r\n      let newZoomLevel;\r\n\r\n      if (imgRatio > containerRatio) {\r\n      // 宽度适配\r\n        newZoomLevel = containerWidth / this.canvas.width;\r\n      } else {\r\n      // 高度适配\r\n        newZoomLevel = containerHeight / this.canvas.height;\r\n      }\r\n\r\n      // 确保缩放系数不会过小\r\n      newZoomLevel = Math.max(newZoomLevel, 0.1);\r\n\r\n      // 应用默认缩放系数，使图片显示更大\r\n      // 限制最大缩放为2倍，避免图片过大导致性能问题\r\n      newZoomLevel = Math.min(newZoomLevel * this.defaultZoomFactor, 2.5);\r\n\r\n      this.zoomLevel = newZoomLevel;\r\n      this.initialZoomLevel = newZoomLevel;\r\n      this.applyZoom();\r\n\r\n      // 确保画布在容器中居中\r\n      this.$nextTick(() => {\r\n        const canvasContainer = this.$refs.canvasContainer;\r\n        if (canvasContainer) {\r\n        // 如果画布宽度小于容器宽度，添加水平居中样式\r\n          if (this.canvas.offsetWidth * this.zoomLevel < canvasContainer.offsetWidth) {\r\n            this.canvas.style.marginLeft = 'auto';\r\n            this.canvas.style.marginRight = 'auto';\r\n            this.canvas.style.display = 'block';\r\n          }\r\n        }\r\n      });\r\n\r\n      // this.$message.success('已自动调整图片大小');\r\n    },\r\n\r\n    // 更新事件坐标计算，考虑缩放因素\r\n    getAdjustedCoordinates(e) {\r\n      if (!this.canvas) {\r\n        return {x: e.layerX, y: e.layerY};\r\n      }\r\n\r\n      // 考虑缩放因素\r\n      const rect = this.canvas.getBoundingClientRect();\r\n      const scaleX = this.canvas.width / rect.width;\r\n      const scaleY = this.canvas.height / rect.height;\r\n\r\n      // 相对于画布的坐标\r\n      const x = (e.clientX - rect.left) * scaleX;\r\n      const y = (e.clientY - rect.top) * scaleY;\r\n\r\n      return {x, y};\r\n    },\r\n\r\n    // 切换显示区域名称\r\n    toggleAreaNames() {\r\n      this.showAreaNames = !this.showAreaNames;\r\n      this.redrawCanvas();\r\n      this.$message.success(this.showAreaNames ? '已显示区域名称' : '已隐藏区域名称');\r\n    },\r\n\r\n    // 处理默认缩放变化\r\n    handleDefaultZoomChange(command) {\r\n      const newFactor = parseFloat(command);\r\n      if (!isNaN(newFactor)) {\r\n        this.defaultZoomFactor = newFactor;\r\n        this.$message.success(`默认缩放已设置为${Math.round(newFactor * 100)}%`);\r\n        this.autoFit(); // 立即应用新的缩放设置\r\n      }\r\n    },\r\n\r\n    // 处理区域表单提交\r\n    handleAreaFormSubmit() {\r\n      this.$refs.areaForm.validate(valid => {\r\n        if (valid && this.tempAreaData) {\r\n          this.areaFormLoading = true;\r\n\r\n          // 将区域数据添加到相应的数组\r\n          this.finalArr.push(this.tempAreaData.area);\r\n          this.finalArrRelative.push(this.tempAreaData.relativeArea);\r\n\r\n          // 添加信息\r\n          this.roomNameList.push({\r\n            roomName: this.areaForm.roomName,\r\n            roomType: this.areaForm.roomType,\r\n            remark: this.areaForm.remark\r\n          });\r\n\r\n          // 清除临时数据\r\n          this.tempAreaData = null;\r\n\r\n          // 关闭表单对话框\r\n          setTimeout(() => {\r\n            this.areaFormLoading = false;\r\n            this.areaFormVisible = false;\r\n            this.$message.success('区域信息已保存');\r\n            // 重绘画布以显示新添加的区域名称\r\n            this.redrawCanvas();\r\n          }, 300);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理区域表单取消\r\n    handleAreaFormCancel() {\r\n      if (this.tempAreaData) {\r\n      // 如果取消表单，需要撤销绘制的区域\r\n        this._repeal();\r\n        this.tempAreaData = null;\r\n      }\r\n      this.areaFormVisible = false;\r\n    },\r\n  },\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/visualOpsManagement/areaManagement", "sourcesContent": ["<template>\r\n  <div v-loading=\"spinShow\" class=\"draw\">\r\n    <!-- <el-loading :fullscreen=\"false\" v-if=\"spinShow\">\r\n      <div>正在加载图片</div>\r\n    </el-loading> -->\r\n    <div class=\"drawTop\" ref=\"drawTop\" v-if=\"lineStep == lineNum\">\r\n      <!-- <div>\r\n        <el-upload\r\n          class=\"upload-demo\"\r\n          action=\"\"\r\n          :auto-upload=\"false\"\r\n          :show-file-list=\"false\"\r\n          :on-change=\"handleFileChange\">\r\n          <el-button type=\"primary\" size=\"small\">选择文件</el-button>\r\n        </el-upload>\r\n      </div> -->\r\n      <div>\r\n        <el-button @click=\"goBack\" size=\"small\">返回</el-button>\r\n      </div>\r\n      <!-- <div>\r\n        是否开启模糊查询（标准层开启，其它关闭）：<el-switch v-model=\"isOpenLike\"/>\r\n      </div> -->\r\n      <!-- <div>\r\n        <el-button @click=\"resetAll\" size=\"small\">重新绘制</el-button>\r\n      </div> -->\r\n      <div>\r\n        <el-button @click=\"deleteAllAreas\" size=\"small\" type=\"danger\">删除全部区域</el-button>\r\n      </div>\r\n      <div>\r\n        <el-button @click=\"toggleAreaNames\" size=\"small\" :type=\"showAreaNames ? 'success' : 'info'\">\r\n          {{ showAreaNames ? '隐藏区域名称' : '显示区域名称' }}\r\n        </el-button>\r\n      </div>\r\n\r\n      <div>\r\n        <el-button-group>\r\n          <el-button @click=\"zoomIn\" icon=\"el-icon-zoom-in\" size=\"small\">放大</el-button>\r\n          <el-button @click=\"zoomOut\" icon=\"el-icon-zoom-out\" size=\"small\">缩小</el-button>\r\n          <!-- <el-button @click=\"resetZoom\" size=\"small\">重置缩放</el-button>\r\n          <el-button @click=\"autoFit\" type=\"primary\" size=\"small\">自适应屏幕</el-button> -->\r\n        </el-button-group>\r\n      </div>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"exportJson\" size=\"small\">保存区域</el-button>\r\n      </div>\r\n      <!-- <div>\r\n        <el-dropdown @command=\"handleDefaultZoomChange\">\r\n          <el-button size=\"small\">\r\n            默认缩放: {{ defaultZoomText }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n          </el-button>\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <el-dropdown-item command=\"1.0\">100%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.3\">130%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.5\">150%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.8\">180%</el-dropdown-item>\r\n            <el-dropdown-item command=\"2.0\">200%</el-dropdown-item>\r\n            <el-dropdown-item command=\"2.2\">220%</el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div> -->\r\n    </div>\r\n    <div>\r\n      （鼠标左键点击图片绘制区域，任意位置点击右键自动连接起点和终点完成区域绘制，可重复此步骤绘制多个区域）\r\n    </div>\r\n    <div>\r\n      （鼠标悬停在已绘制区域上可高亮显示，点击区域内任意位置即可删除该区域）\r\n    </div>\r\n    <div style=\"display: flex;\">\r\n      <div class=\"canvas-container\" ref=\"canvasContainer\">\r\n        <!-- 固定在容器右上角的滚动控制按钮，只在需要时显示 -->\r\n        <div v-if=\"showScrollButtons\" class=\"scroll-controls\">\r\n          <el-button-group>\r\n            <el-button @click=\"scrollLeft\" icon=\"el-icon-arrow-left\" size=\"mini\" title=\"向左滚动\"></el-button>\r\n            <el-button @click=\"scrollRight\" icon=\"el-icon-arrow-right\" size=\"mini\" title=\"向右滚动\"></el-button>\r\n          </el-button-group>\r\n        </div>\r\n        <div class=\"content\" ref=\"content\"></div>\r\n        <input\r\n          v-show=\"isShow\"\r\n          type=\"text\"\r\n          @blur=\"txtBlue\"\r\n          ref=\"txt\"\r\n          id=\"txt\"\r\n          style=\"\r\n            z-index: 9999;\r\n            position: absolute;\r\n            border: 0;\r\n            background: none;\r\n            outline: none;\r\n          \"\r\n        />\r\n      </div>\r\n      <!-- 区域列表显示面板 -->\r\n      <div style=\"width: 25%; padding-left: 10px; max-height: 600px; overflow-y: auto;\">\r\n        <h4>已绘制区域列表</h4>\r\n        <el-empty v-if=\"roomNameList.length === 0\" description=\"暂无区域\"></el-empty>\r\n        <el-table\r\n          v-else\r\n          :data=\"roomNameList\"\r\n          style=\"width: 100%\"\r\n          size=\"small\"\r\n          :max-height=\"550\"\r\n          @row-click=\"highlightArea\"\r\n          @row-mouseover=\"mouseoverArea\"\r\n          @row-mouseout=\"mouseoutArea\">\r\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"roomName\" label=\"名称\" align=\"center\"></el-table-column>\r\n          <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <a\r\n                style=\"margin-left: 10px;\"\r\n                @click.stop=\"deleteAreaByIndex(scope.$index)\">\r\n                删除\r\n              </a>\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :visible.sync=\"areaFormVisible\"\r\n      title=\"区域信息\"\r\n      width=\"400px\"\r\n      :close-on-click-modal=\"false\"\r\n      @closed=\"handleAreaFormCancel\">\r\n      <el-form\r\n        ref=\"areaForm\"\r\n        :model=\"areaForm\"\r\n        :rules=\"areaFormRules\"\r\n        label-width=\"100px\"\r\n        size=\"small\">\r\n        <el-form-item label=\"名称\" prop=\"roomName\">\r\n          <el-input v-model=\"areaForm.roomName\" placeholder=\"请输入名称\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleAreaFormCancel\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleAreaFormSubmit\" :loading=\"areaFormLoading\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"imgModal\"\r\n      title=\"更换图片\"\r\n      width=\"400px\"\r\n      @closed=\"imgCancel\">\r\n      <el-form\r\n        ref=\"imgForm\"\r\n        :model=\"imgForm\"\r\n        :rules=\"imgFormValidate\"\r\n        label-width=\"110px\">\r\n        <el-form-item label=\"楼层号\" prop=\"floorNo\">\r\n          <el-input\r\n            v-model.trim=\"imgForm.floorNo\"\r\n            maxlength=\"32\"\r\n            placeholder=\"楼层号\">\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"图片地址\" prop=\"imgUrl\">\r\n          <el-input\r\n            v-model.trim=\"imgForm.imgUrl\"\r\n            maxlength=\"256\"\r\n            placeholder=\"图片地址\">\r\n          </el-input>\r\n          <!-- <multi-upload-pic-input :maxCount=\"1\" :maxSize=\"10240\" @on-change=\"handleMultiUpload2\" width=\"235px\" ref=\"multiUploadImage\"></multi-upload-pic-input> -->\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"imgCancel\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"imgOkLoading\" @click=\"imgOk\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'callout',\r\n  components: {\r\n  },\r\n  props: {\r\n    areaArr: Array,\r\n  },\r\n  data() {\r\n    return {\r\n      imgForm: {},\r\n      imgModal: false,\r\n      imgOkLoading: false,\r\n      imgFormValidate: {},\r\n\r\n      isOpenLike: false,\r\n      imgUrl: '',\r\n      roomNameList: [],\r\n      spinShow: false,\r\n      imgPath: '',\r\n      finalArr: [], // 绝对坐标数组，用于绘制\r\n      finalArrRelative: [], // 相对坐标数组，用于保存\r\n      dataArr: [],\r\n      isShow: false,\r\n      canvas: '',\r\n      ctx: '',\r\n      ctxX: 0,\r\n      ctxY: 0,\r\n      lineWidth: 3,\r\n      type: 'L',\r\n      typeOption: [\r\n        {label: '线', value: 'L'},\r\n      // { label: '矩形', value: 'R' }\r\n      //   {label: \"箭头\", value: \"A\"},\r\n      //   {label: \"文字\", value: \"T\"},\r\n      ],\r\n      canvasHistory: [],\r\n      step: 0,\r\n      loading: false,\r\n      fillStyle: '#CB0707',\r\n      strokeStyle: '#1E90FF',\r\n      lineNum: 2,\r\n      linePeak: [],\r\n      lineStep: 2,\r\n      ellipseR: 0.5,\r\n      dialogVisible: false,\r\n      isUnfold: true,\r\n      fontSize: 18,\r\n      fontColor: '#333333',\r\n      fontFamily: 'Microsoft YaHei, Arial, sans-serif',\r\n      img: new Image(),\r\n      hoveredAreaIndex: -1, // 当前鼠标悬停的区域索引\r\n      highlightColor: '#2E8AE6',\r\n      deleteMode: false, // 是否处于删除模式\r\n      lastHoveredAreaIndex: -1, // 用于表格行悬停时记录之前高亮的区域索引\r\n      zoomLevel: 1,\r\n      initialZoomLevel: 1,\r\n      defaultZoomFactor: 1, // 增大默认缩放系数到1.5\r\n      showAreaNames: true,\r\n      // 区域表单相关\r\n      areaFormVisible: false,\r\n      areaFormLoading: false,\r\n      areaForm: {\r\n        roomName: '',\r\n        roomType: '',\r\n        remark: ''\r\n      },\r\n      areaFormRules: {\r\n        roomName: [\r\n          {required: true, message: '请输入名称', trigger: 'blur'}\r\n        ]\r\n      },\r\n      tempAreaData: null, // 临时存储新绘制的区域数据\r\n      debounceTimer: null, // 添加防抖定时器\r\n      // 滚动控制相关\r\n      scrollLeftBtn: null,\r\n      scrollRightBtn: null,\r\n      // 保存当前drawingId，用于刷新页面时恢复\r\n      currentDrawingId: ''\r\n    };\r\n  },\r\n  computed: {\r\n    defaultZoomText() {\r\n      return `${Math.round(this.defaultZoomFactor * 100)}%`;\r\n    },\r\n    // 判断是否需要显示滚动按钮\r\n    showScrollButtons() {\r\n      if (!this.$refs.canvasContainer || !this.canvas) {\r\n        return false;\r\n      }\r\n      // 当画布宽度大于容器宽度时显示滚动按钮\r\n      return this.canvas.width * this.zoomLevel > this.$refs.canvasContainer.clientWidth;\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL参数或localStorage获取drawingId\r\n    const drawingId = this.$route.query.drawingId || localStorage.getItem('currentDrawingId');\r\n\r\n    // 如果有drawingId，保存到组件状态和localStorage\r\n    if (drawingId) {\r\n      this.currentDrawingId = drawingId;\r\n      localStorage.setItem('currentDrawingId', drawingId);\r\n\r\n      // 如果URL中没有drawingId但localStorage有，则更新URL\r\n      if (!this.$route.query.drawingId) {\r\n        this.$router.replace({\r\n          query: {...this.$route.query, drawingId}\r\n        });\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化监听器\r\n    window.removeEventListener('resize', this.checkScrollNeeded);\r\n  },\r\n  mounted() {\r\n    // 添加窗口大小变化监听器\r\n    window.addEventListener('resize', this.checkScrollNeeded);\r\n\r\n    // 获取drawingId并加载数据\r\n    const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n    if (drawingId) {\r\n      this.getList();\r\n    } else {\r\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\r\n      setTimeout(() => {\r\n        this.goBack();\r\n      }, 2000);\r\n    }\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        this.$message.warning('缺少图纸ID参数');\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceDrawingManagement-get']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        console.log(data, 'data');\r\n        if (data) {\r\n          // 获取base64图片数据\r\n          const drawingData = data;\r\n          if (drawingData.drawingInfo) {\r\n            // 设置图片路径为base64数据\r\n            this.imgPath = drawingData.drawingInfo;\r\n            // 图纸基本信息\r\n            this.imgUrl = drawingData.drawingInfo;\r\n\r\n            // 初始化画布，渲染图片\r\n            this.init();\r\n\r\n            // 图片渲染完成后，获取已有区域数据\r\n            this.getRegionList();\r\n          } else {\r\n            this.$message.error('图片数据不存在');\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取图纸数据失败:', error);\r\n        this.$message.error('获取图纸数据失败');\r\n      });\r\n    },\r\n\r\n    // 获取已有区域数据\r\n    getRegionList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceRegion-list']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        if (data && data.length > 0) {\r\n          // 将区域信息转换为需要的格式，并保存原始数据中的ID\r\n          this.roomNameList = data.map(region => ({\r\n            roomName: region.regionName,\r\n            roomType: '',\r\n            remark: region.remark || '',\r\n            id: region.id || null // 保存原始区域ID，用于后续删除操作\r\n          }));\r\n\r\n          // 解析区域坐标数据\r\n          const areaCoordinates = data.map(region => {\r\n            try {\r\n              return JSON.parse(region.pointJson || '[]');\r\n            } catch (e) {\r\n              console.error('Error parsing pointJson:', e);\r\n              return [];\r\n            }\r\n          }).filter(coords => coords.length > 0);\r\n\r\n          // 保存相对坐标数据\r\n          this.finalArrRelative = [...areaCoordinates];\r\n\r\n          // 处理区域数据并绘制到画布上\r\n          if (areaCoordinates.length > 0) {\r\n            // 转换成绝对坐标并保存\r\n            this.finalArr = this.processAreaData(areaCoordinates);\r\n\r\n            // 绘制所有区域\r\n            this.redrawCanvas();\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取区域数据失败:', error);\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\r\n    },\r\n    editAreaByIndex(index) {\r\n\r\n    },\r\n    handleFileChange(file) {\r\n      const rawFile = file.raw;\r\n      if (!rawFile) {\r\n        return;\r\n      }\r\n      const reader = new FileReader();\r\n      reader.onload = e => {\r\n        this.imgPath = e.target.result;\r\n        this.spinShow = true;\r\n        this.init();\r\n      };\r\n      reader.readAsDataURL(rawFile);\r\n    },\r\n    // 检查是否需要滚动按钮\r\n    checkScrollNeeded() {\r\n      if (this.$refs.canvasContainer && this.canvas) {\r\n      // 通过Vue的响应式系统触发计算属性重新计算\r\n        this.$forceUpdate();\r\n      // 不再需要调整容器高度\r\n      // this.adjustContainerHeight();\r\n      }\r\n    },\r\n    // 不再需要调整容器高度的方法\r\n    /*\r\n  adjustContainerHeight() {\r\n    if (this.$refs.canvasContainer && this.canvas) {\r\n      const canvasHeight = this.canvas.height * this.zoomLevel;\r\n      // 设置容器高度为画布高度加上一些边距\r\n      this.$refs.canvasContainer.style.height = `${canvasHeight + 20}px`;\r\n    }\r\n  },\r\n  */\r\n    // 向左滚动画布\r\n    scrollLeft() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft -= scrollAmount;\r\n      }\r\n    },\r\n    // 向右滚动画布\r\n    scrollRight() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft += scrollAmount;\r\n      }\r\n    },\r\n    init() {\r\n      let _this = this;\r\n      let image = new Image();\r\n      image.setAttribute('crossOrigin', 'anonymous');\r\n      image.src = this.imgPath;\r\n      image.onload = function () {\r\n      // 图片加载完，再draw 和 toDataURL\r\n        if (image.complete) {\r\n          _this.spinShow = false;\r\n          _this.img = image;\r\n          let content = _this.$refs.content;\r\n          _this.canvas = document.createElement('canvas');\r\n          _this.canvas.height = _this.img.height;\r\n          _this.canvas.width = _this.img.width;\r\n          _this.canvas.setAttribute('style', 'border:2px solid red;');\r\n          _this.canvas.setAttribute('id', 'myCanvas');\r\n          _this.ctx = _this.canvas.getContext('2d');\r\n          _this.ctx.globalAlpha = 1;\r\n          _this.ctx.drawImage(_this.img, 0, 0);\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.ctx.globalCompositeOperation = _this.type;\r\n\r\n          // 清空之前的内容\r\n          content.innerHTML = '';\r\n          content.appendChild(_this.canvas);\r\n\r\n          // 重置交互状态\r\n          _this.deleteMode = false;\r\n          _this.hoveredAreaIndex = -1;\r\n\r\n          // 预设最小缩放比例，防止图片太小\r\n          _this.defaultZoomFactor = Math.max(_this.defaultZoomFactor, 1.2);\r\n\r\n          // 自动适应屏幕\r\n          _this.$nextTick(() => {\r\n            _this.autoFit();\r\n            // 不再需要调整容器高度\r\n            // _this.adjustContainerHeight();\r\n            // 检查是否需要滚动按钮\r\n            _this.checkScrollNeeded();\r\n          });\r\n\r\n          _this.bindEventLisner();\r\n\r\n          if (_this.areaArr) {\r\n            _this.finalArr = _this.processAreaData(_this.areaArr);\r\n            _this.finalArr.forEach(i => {\r\n              _this.createL2(i);\r\n            });\r\n          }\r\n        }\r\n      };\r\n    },\r\n    radioClick(item) {\r\n      if (item != 'T') {\r\n        this.txtBlue();\r\n        this.resetTxt();\r\n      }\r\n    },\r\n    // 下载画布\r\n    downLoad() {\r\n      let _this = this;\r\n      let url = _this.canvas.toDataURL('image/png');\r\n      let fileName = 'canvas.png';\r\n      if ('download' in document.createElement('a')) {\r\n      // 非IE下载\r\n        const elink = document.createElement('a');\r\n        elink.download = fileName;\r\n        elink.style.display = 'none';\r\n        elink.href = url;\r\n        document.body.appendChild(elink);\r\n        elink.click();\r\n        document.body.removeChild(elink);\r\n      } else {\r\n      // IE10+下载\r\n        navigator.msSaveBlob(url, fileName);\r\n      }\r\n    },\r\n    // 重置所有内容\r\n    resetAll() {\r\n      this.dataArr = [];\r\n      this.finalArr = [];\r\n      this.finalArrRelative = []; // 同时清空相对坐标数组\r\n      this.roomNameList = [];\r\n      this.$emit('getPointArr', this.finalArr);\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.canvasHistory = [];\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.canvasHistory.push(this.canvas.toDataURL());\r\n      this.step = 0;\r\n      this.resetTxt();\r\n      this.hoveredAreaIndex = -1;\r\n      this.deleteMode = false;\r\n      // 应用缩放\r\n      this.applyZoom();\r\n    },\r\n    // 保存区域规划\r\n    save() {\r\n    // this.finalArr\r\n    },\r\n    // 清空当前画布\r\n    reset() {\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.resetTxt();\r\n    },\r\n    // 撤销方法\r\n    repeal() {\r\n      let _this = this;\r\n      if (this.isShow) {\r\n        _this.resetTxt();\r\n        _this._repeal();\r\n      } else {\r\n        _this._repeal();\r\n      }\r\n    },\r\n    _repeal() {\r\n      if (this.step >= 1) {\r\n        this.step = this.step - 1;\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n          this.loading = true;\r\n        });\r\n      } else {\r\n        this.$message.warning('不能再继续撤销了');\r\n      }\r\n    },\r\n    // 恢复方法\r\n    canvasRedo() {\r\n      if (this.step < this.canvasHistory.length - 1) {\r\n        if (this.step == 0) {\r\n          this.step = 1;\r\n        } else {\r\n          this.step++;\r\n        }\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n        });\r\n      } else {\r\n        this.$message.warning('已经是最新的记录了');\r\n      }\r\n    },\r\n    // 绘制历史数组中的最后一个\r\n    rebroadcast() {\r\n      let canvasPic = new Image();\r\n      canvasPic.src = this.canvasHistory[this.step];\r\n      canvasPic.addEventListener('load', () => {\r\n        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n        this.ctx.drawImage(canvasPic, 0, 0);\r\n        this.loading = true;\r\n      });\r\n    },\r\n    // 绑定事件,判断分支\r\n    bindEventLisner() {\r\n      let _this = this;\r\n      let r1, r2; // 绘制圆形，矩形需要\r\n      this.canvas.addEventListener('click', function (e) {\r\n        if (_this.type == 'L' && e.button == 0) {\r\n          const coords = _this.getAdjustedCoordinates(e);\r\n          // 如果处于删除模式，检查是否点击在区域内\r\n          if (_this.deleteMode) {\r\n            _this.checkAndDeleteArea(coords);\r\n          } else {\r\n            _this.createL(coords, 'begin');\r\n          }\r\n        }\r\n      });\r\n      this.canvas.oncontextmenu = function (e) {\r\n        const coords = _this.getAdjustedCoordinates(e);\r\n        _this.createL(coords, 'end');\r\n        return false;\r\n      };\r\n\r\n      // 添加鼠标移动事件监听器，用于区域高亮，使用防抖处理\r\n      this.canvas.addEventListener('mousemove', function (e) {\r\n      // 清除之前的定时器\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        // 创建新的定时器，延迟执行高亮逻辑\r\n        _this.debounceTimer = setTimeout(() => {\r\n          if (_this.finalArr && _this.finalArr.length > 0) {\r\n            const coords = _this.getAdjustedCoordinates(e);\r\n            let isOverArea = false;\r\n            let hoveredIndex = -1;\r\n\r\n            // 检查鼠标是否在任何一个区域内\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.isPointInPolygon(coords, _this.finalArr[i])) {\r\n                hoveredIndex = i;\r\n                isOverArea = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            // 只有当悬停状态发生变化时才重绘\r\n            if (_this.hoveredAreaIndex !== hoveredIndex) {\r\n              _this.hoveredAreaIndex = hoveredIndex;\r\n              _this.redrawWithHighlight();\r\n              document.body.style.cursor = isOverArea ? 'pointer' : 'default';\r\n            }\r\n          }\r\n        }, 10); // 10毫秒的防抖延迟，平衡响应性和性能\r\n      });\r\n\r\n      // 添加鼠标离开画布事件\r\n      this.canvas.addEventListener('mouseleave', function () {\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        if (_this.hoveredAreaIndex !== -1) {\r\n          _this.hoveredAreaIndex = -1;\r\n          _this.redrawCanvas();\r\n          document.body.style.cursor = 'default';\r\n        }\r\n      });\r\n    },\r\n    // 判断点是否在多边形内（原有方法，保留但不使用）\r\n    judge(dot, coordinates) {\r\n      return this.isPointInPolygon(dot, coordinates);\r\n    },\r\n    // 绘制线条\r\n    createL(coords, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 点击开始绘制前，检查点击位置是否在已有区域内\r\n        const mousePoint = {x: coords.x, y: coords.y};\r\n        for (let i = 0; i < this.finalArr.length; i++) {\r\n          if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n            this.$message.warning('您点击的位置已在其他区域内，请选择其他位置绘制');\r\n            return; // 阻止继续执行\r\n          }\r\n        }\r\n\r\n        if (_this.dataArr && _this.dataArr.length === 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.beginPath();\r\n          _this.ctx.moveTo(coords.x, coords.y);\r\n          _this.ctx.lineTo(coords.x + 1, coords.y + 1);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        } else if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.lineTo(coords.x, coords.y);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        }\r\n      } else if (status == 'end') {\r\n        if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.ctx.moveTo(\r\n            _this.dataArr[_this.dataArr.length - 1].x,\r\n            _this.dataArr[_this.dataArr.length - 1].y\r\n          );\r\n          _this.ctx.lineTo(_this.dataArr[0].x, _this.dataArr[0].y);\r\n          _this.ctx.stroke();\r\n          _this.ctx.closePath();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step;\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          if (_this.dataArr && _this.dataArr.length < 3) {\r\n            _this.$message.info('该区域点数少于三个，不保存');\r\n            _this._repeal();\r\n          } else {\r\n          // 检查新绘制的区域是否与现有区域有重叠\r\n            const newArea = _this.dataArr;\r\n            let hasOverlap = false;\r\n\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.checkPolygonsOverlap(newArea, _this.finalArr[i])) {\r\n                hasOverlap = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            if (hasOverlap) {\r\n              _this.$message.warning('新绘制的区域与现有区域重叠，请重新绘制');\r\n              _this._repeal();\r\n            } else {\r\n            // 存储新绘制的区域数据，等待表单提交后再保存\r\n              _this.tempAreaData = {\r\n                area: _this.dataArr,\r\n                relativeArea: _this.dataArr.map(point => _this.toRelativeCoordinates(point))\r\n              };\r\n\r\n              // 打开区域信息表单\r\n              _this.areaForm = {\r\n                roomName: `区域${_this.roomNameList.length + 1}`,\r\n                roomType: '',\r\n                remark: ''\r\n              };\r\n              _this.areaFormVisible = true;\r\n            }\r\n          }\r\n          this.$emit('getPointArr', _this.finalArr);\r\n          _this.dataArr = [];\r\n          _this.canvas.onmousemove = null;\r\n        }\r\n      }\r\n    },\r\n    // 重绘画布并高亮显示悬停区域\r\n    redrawWithHighlight() {\r\n    // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 重绘背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i], i === this.hoveredAreaIndex);\r\n      }\r\n    },\r\n\r\n    // 统一的区域绘制函数\r\n    drawArea(area, isHighlighted = false) {\r\n      if (!area || area.length < 3) {\r\n        return;\r\n      }\r\n\r\n      this.ctx.beginPath();\r\n      this.ctx.moveTo(area[0].x, area[0].y);\r\n\r\n      // 绘制区域轮廓\r\n      for (let i = 1; i < area.length; i++) {\r\n        this.ctx.lineTo(area[i].x, area[i].y);\r\n      }\r\n\r\n      // 闭合路径\r\n      this.ctx.lineTo(area[0].x, area[0].y);\r\n\r\n      // 设置样式\r\n      if (isHighlighted) {\r\n      // 高亮显示 - 增加线宽，使用更深的蓝色\r\n        this.ctx.strokeStyle = this.highlightColor;\r\n        this.ctx.lineWidth = this.lineWidth + 2;\r\n\r\n        // 填充半透明颜色\r\n        this.ctx.fillStyle = this.highlightColor + '22'; // 添加22作为透明度 (13%)\r\n        this.ctx.fill();\r\n      } else {\r\n        this.ctx.strokeStyle = this.strokeStyle;\r\n        this.ctx.lineWidth = this.lineWidth;\r\n      }\r\n\r\n      this.ctx.stroke();\r\n      this.ctx.closePath();\r\n\r\n      // 如果启用了显示区域名称，并且能找到对应的名\r\n      const areaIndex = this.finalArr.indexOf(area);\r\n      if (this.showAreaNames && areaIndex >= 0 && this.roomNameList[areaIndex]) {\r\n        this.drawAreaName(area, this.roomNameList[areaIndex].roomName);\r\n      }\r\n    },\r\n\r\n    // 绘制区域名称\r\n    drawAreaName(area, name) {\r\n      if (!area || area.length < 3 || !name) {\r\n        return;\r\n      }\r\n\r\n      // 计算区域的中心点\r\n      let centerX = 0, centerY = 0;\r\n      for (let i = 0; i < area.length; i++) {\r\n        centerX += area[i].x;\r\n        centerY += area[i].y;\r\n      }\r\n      centerX /= area.length;\r\n      centerY /= area.length;\r\n\r\n      // 保存当前上下文状态\r\n      this.ctx.save();\r\n\r\n      // 设置固定的字体大小，不受图片尺寸和缩放影响\r\n      // 首先获取画布的尺寸\r\n      const canvasWidth = this.canvas.width;\r\n      const canvasHeight = this.canvas.height;\r\n\r\n      // 计算固定的字体大小，基于画布尺寸的一个合适比例\r\n      // 这样可以确保在不同大小的图片上，字体大小相对于图片尺寸的比例是一致的\r\n      const baseFontSize = 36; // 固定基础字体大小\r\n\r\n      // 应用字体设置\r\n      this.ctx.font = `bold ${baseFontSize}px ${this.fontFamily}`;\r\n      this.ctx.textAlign = 'center';\r\n      this.ctx.textBaseline = 'middle';\r\n\r\n      // 测量文本宽度\r\n      const textWidth = this.ctx.measureText(name).width;\r\n\r\n      // 增强文字阴影以提高可读性\r\n      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';\r\n      this.ctx.shadowBlur = 4;\r\n      this.ctx.shadowOffsetX = 1;\r\n      this.ctx.shadowOffsetY = 1;\r\n\r\n      // 使用更亮的蓝色，但与线条颜色(#1E90FF)有区别\r\n      this.ctx.fillStyle = '#38B0DE'; // 天蓝色，比线条颜色略微偏青一些\r\n      this.ctx.fillText(name, centerX, centerY);\r\n\r\n      // 恢复上下文状态\r\n      this.ctx.restore();\r\n    },\r\n\r\n    // 绘制圆角矩形\r\n    roundRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {\r\n      if (typeof radius === 'undefined') {\r\n        radius = 5;\r\n      }\r\n      if (typeof radius === 'number') {\r\n        radius = {tl: radius, tr: radius, br: radius, bl: radius};\r\n      } else {\r\n        const defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};\r\n        for (const side in defaultRadius) {\r\n          radius[side] = radius[side] || defaultRadius[side];\r\n        }\r\n      }\r\n\r\n      ctx.beginPath();\r\n      ctx.moveTo(x + radius.tl, y);\r\n      ctx.lineTo(x + width - radius.tr, y);\r\n      ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);\r\n      ctx.lineTo(x + width, y + height - radius.br);\r\n      ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);\r\n      ctx.lineTo(x + radius.bl, y + height);\r\n      ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);\r\n      ctx.lineTo(x, y + radius.tl);\r\n      ctx.quadraticCurveTo(x, y, x + radius.tl, y);\r\n      ctx.closePath();\r\n\r\n      if (fillColor) {\r\n        ctx.fillStyle = fillColor;\r\n        ctx.fill();\r\n      }\r\n\r\n      if (strokeColor) {\r\n        ctx.strokeStyle = strokeColor;\r\n        ctx.lineWidth = 1;\r\n        ctx.stroke();\r\n      }\r\n    },\r\n\r\n    // 重绘画布\r\n    redrawCanvas() {\r\n      if (!this.canvas || !this.ctx) {\r\n        return;\r\n      }\r\n\r\n      // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 绘制背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有保留的区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i]);\r\n      }\r\n    },\r\n\r\n    // 创建区域2（读取已有区域）\r\n    createL2(e) {\r\n      let _this = this;\r\n\r\n      // 使用统一的绘制函数\r\n      this.drawArea(e);\r\n\r\n      _this.step = _this.step + 1;\r\n      if (_this.step < _this.canvasHistory.length - 1) {\r\n        _this.canvasHistory.length = _this.step;\r\n      }\r\n      _this.canvasHistory.push(_this.canvas.toDataURL());\r\n      this.$emit('getPointArr', _this.finalArr);\r\n    },\r\n    // 绘制矩形\r\n    createR(e, status, r1, r2) {\r\n      let _this = this;\r\n      let r;\r\n      if (status == 'begin') {\r\n      // console.log('onmousemove')\r\n        _this.canvas.onmousemove = function (e) {\r\n          _this.reset();\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n\r\n          // 保留之前绘画的图形\r\n          if (_this.step !== 0) {\r\n            let canvasPic = new Image();\r\n            canvasPic.src = _this.canvasHistory[_this.step];\r\n            _this.ctx.drawImage(canvasPic, 0, 0);\r\n          }\r\n\r\n          _this.ctx.beginPath();\r\n          _this.ctx.strokeRect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n        };\r\n      } else if (status == 'end') {\r\n        _this.rebroadcast();\r\n        let interval = setInterval(() => {\r\n          if (_this.loading) {\r\n            clearInterval(interval);\r\n            _this.loading = false;\r\n          } else {\r\n            return;\r\n          }\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n          _this.ctx.beginPath();\r\n          _this.ctx.rect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step; // 截断数组\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.canvas.onmousemove = null;\r\n        }, 1);\r\n      }\r\n    },\r\n\r\n    // 绘制箭头\r\n    drawArrow(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 获取起始位置\r\n        _this.arrowFromX = e.layerX;\r\n        _this.arrowFromY = e.layerY;\r\n        _this.ctx.beginPath();\r\n        _this.ctx.moveTo(e.layerX, e.layerY);\r\n      } else if (status == 'end') {\r\n      // 计算箭头及画线\r\n        let toX = e.layerX;\r\n        let toY = e.layerY;\r\n        let theta = 30;\r\n        let headlen = 10;\r\n        let _this = this;\r\n        let fromX = this.arrowFromX;\r\n        let fromY = this.arrowFromY;\r\n        // 计算各角度和对应的P2,P3坐标\r\n        let angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;\r\n        let angle1 = ((angle + theta) * Math.PI) / 180;\r\n        let angle2 = ((angle - theta) * Math.PI) / 180;\r\n        let topX = headlen * Math.cos(angle1);\r\n        let topY = headlen * Math.sin(angle1);\r\n        let botX = headlen * Math.cos(angle2);\r\n        let botY = headlen * Math.sin(angle2);\r\n        let arrowX = fromX - topX;\r\n        let arrowY = fromY - topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.moveTo(fromX, fromY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + topX;\r\n        arrowY = toY + topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + botX;\r\n        arrowY = toY + botY;\r\n        _this.ctx.lineTo(arrowX, arrowY);\r\n        _this.ctx.strokeStyle = _this.strokeStyle;\r\n        _this.ctx.lineWidth = _this.lineWidth;\r\n        _this.ctx.stroke();\r\n\r\n        _this.ctx.closePath();\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n\r\n    // 文字输入\r\n    createT(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 初始化文字输入相关参数\r\n        _this.isTextInputMode = true;\r\n      } else if (status == 'end') {\r\n        let offset = 0;\r\n        if (_this.fontSize >= 28) {\r\n          offset = _this.fontSize / 2 - 3;\r\n        } else {\r\n          offset = _this.fontSize / 2 - 2;\r\n        }\r\n\r\n        _this.ctxX = e.layerX + 2;\r\n        _this.ctxY = e.layerY + offset;\r\n\r\n        let index = this.getPointOnCanvas(e);\r\n        _this.$refs.txt.style.left = index.x + 'px';\r\n        _this.$refs.txt.style.top = index.y - _this.fontSize / 2 + 'px';\r\n        _this.$refs.txt.value = '';\r\n        _this.$refs.txt.style.height = _this.fontSize + 'px';\r\n        (_this.$refs.txt.style.width\r\n          = _this.canvas.width - e.layerX - 1 + 'px'),\r\n        (_this.$refs.txt.style.fontSize = _this.fontSize + 'px');\r\n        _this.$refs.txt.style.fontFamily = _this.fontFamily;\r\n        _this.$refs.txt.style.color = _this.fontColor;\r\n        _this.$refs.txt.style.maxlength = Math.floor(\r\n          (_this.canvas.width - e.layerX) / _this.fontSize\r\n        );\r\n        _this.isShow = true;\r\n        setTimeout(() => {\r\n          _this.$refs.txt.focus();\r\n        });\r\n      }\r\n    },\r\n    // 文字输入框失去光标时在画布上生成文字\r\n    txtBlue() {\r\n      let _this = this;\r\n      let txt = _this.$refs.txt.value;\r\n      if (txt) {\r\n        _this.ctx.font\r\n          = _this.$refs.txt.style.fontSize\r\n          + ' '\r\n          + _this.$refs.txt.style.fontFamily;\r\n        _this.ctx.fillStyle = _this.$refs.txt.style.color;\r\n        _this.ctx.fillText(txt, _this.ctxX, _this.ctxY);\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n    // 计算文字框定位位置\r\n    getPointOnCanvas(e) {\r\n      let cs = this.canvas;\r\n      let content = document.getElementsByClassName('content')[0];\r\n      return {\r\n        x: e.layerX + (content.clientWidth - cs.width) / 2,\r\n        y: e.layerY,\r\n      };\r\n    },\r\n    // 清空文字\r\n    resetTxt() {\r\n      let _this = this;\r\n      _this.$refs.txt.value = '';\r\n      _this.isShow = false;\r\n    },\r\n    exportJson() {\r\n      // 直接使用已经准备好的相对坐标数组\r\n      let exportArr = JSON.parse(JSON.stringify(this.finalArrRelative));\r\n\r\n      // 获取URL中的drawingId参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      // 准备导出数据，使用正确的字段名\r\n      let maintenanceRegionList = this.roomNameList.map((room, index) => {\r\n        return {\r\n          id: room.id || null,\r\n          regionName: room.roomName, // 使用regionName作为区域名称字段\r\n          pointJson: JSON.stringify(exportArr[index]), // 使用pointJson作为区域坐标JSON字段\r\n          remark: room.remark || '', // 备注字段\r\n          drawingId: drawingId // 从URL参数获取的图纸ID\r\n        };\r\n      });\r\n\r\n      // 如果没有区域数据，提示用户\r\n      if (maintenanceRegionList.length === 0) {\r\n        this.$message.warning('尚未绘制任何区域，无法保存');\r\n        return;\r\n      }\r\n\r\n      // 二次确认保存操作\r\n      this.$confirm('确认保存当前绘制的所有区域？', '保存确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 用户确认，调用保存接口\r\n        console.log('保存的区域数据:', maintenanceRegionList);\r\n\r\n        // 调用保存接口，传入整合后的区域列表\r\n        this.$api['areaManagement/maintenanceRegion-save']({\r\n          maintenanceRegionList: maintenanceRegionList\r\n        }).then(data => {\r\n          this.$message.success('保存成功');\r\n        }).catch(error => {\r\n          this.$message.error('保存失败，请重试');\r\n        });\r\n      });\r\n    },\r\n    ToBase64() {\r\n      let that = this;\r\n      var img = document.getElementById('imgfile');\r\n      var imgFile = new FileReader();\r\n      imgFile.readAsDataURL(img.files[0]);\r\n\r\n      imgFile.onload = function () {\r\n        var imgData = this.result; // base64数据\r\n        that.imgPath = imgData;\r\n        that.init();\r\n      };\r\n    },\r\n    handleMultiUpload(v) {\r\n      this.imgUrl = v[0];\r\n    },\r\n    handleMultiUpload2(v) {\r\n      this.imgForm.imgUrl = v[0];\r\n    },\r\n    changeImgUrl() {\r\n      this.imgForm = {\r\n        floorNo: '',\r\n        imgUrl: ''\r\n      };\r\n      this.imgModal = true;\r\n    },\r\n    imgOk() {\r\n      this.$refs.imgForm.validate(valid => {\r\n        if (valid) {\r\n          this.imgOkLoading = true;\r\n          this.$api['iotHome/changeFloorMap'](this.imgForm)\r\n            .then(data => {\r\n              this.$message.success('更换成功');\r\n              this.imgModal = false;\r\n            })\r\n            .finally(() => {\r\n              this.imgOkLoading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    imgCancel() {\r\n    // 取消关闭\r\n      this.imgModal = false;\r\n      this.$refs.imgForm.resetFields();\r\n    },\r\n    // 将绝对坐标转换为相对坐标（0-1之间的比例值）\r\n    toRelativeCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: parseFloat((point.x / this.canvas.width).toFixed(4)),\r\n        y: parseFloat((point.y / this.canvas.height).toFixed(4))\r\n      };\r\n    },\r\n\r\n    // 将相对坐标转换为绝对坐标（实际像素值）\r\n    toAbsoluteCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: Math.round(point.x * this.canvas.width),\r\n        y: Math.round(point.y * this.canvas.height)\r\n      };\r\n    },\r\n\r\n    // 判断一个点是否为相对坐标（值在0-1之间）\r\n    isRelativeCoordinate(point) {\r\n      return point.x >= 0 && point.x <= 1 && point.y >= 0 && point.y <= 1;\r\n    },\r\n\r\n    // 确保点使用绝对坐标\r\n    ensureAbsoluteCoordinate(point) {\r\n      if (this.isRelativeCoordinate(point)) {\r\n        return this.toAbsoluteCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 确保点使用相对坐标\r\n    ensureRelativeCoordinate(point) {\r\n      if (!this.isRelativeCoordinate(point)) {\r\n        return this.toRelativeCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 处理输入的区域数据，确保使用正确的坐标类型\r\n    processAreaData(areaData) {\r\n      if (!areaData || !areaData.length) {\r\n        return [];\r\n      }\r\n\r\n      // 深拷贝避免修改原始数据\r\n      const processedData = JSON.parse(JSON.stringify(areaData));\r\n\r\n      // 检查第一个点的第一个坐标，判断是否为相对坐标\r\n      const firstArea = processedData[0];\r\n      if (firstArea && firstArea.length > 0) {\r\n        const firstPoint = firstArea[0];\r\n        const isRelative = this.isRelativeCoordinate(firstPoint);\r\n\r\n        // 如果是相对坐标，转换为绝对坐标用于绘制\r\n        if (isRelative) {\r\n        // 同时保存相对坐标版本\r\n          this.finalArrRelative = JSON.parse(JSON.stringify(processedData));\r\n\r\n          processedData.forEach(area => {\r\n            area.forEach((point, index) => {\r\n              area[index] = this.toAbsoluteCoordinates(point);\r\n            });\r\n          });\r\n        } else {\r\n        // 如果是绝对坐标，生成相对坐标版本\r\n          this.finalArrRelative = processedData.map(area =>\r\n            area.map(point => this.toRelativeCoordinates(point))\r\n          );\r\n        }\r\n      }\r\n\r\n      return processedData;\r\n    },\r\n    deleteAllAreas() {\r\n      if (this.finalArr.length > 0) {\r\n        // 二次确认删除所有区域操作，强调可能影响已绑定设备\r\n        this.$confirm('警告：删除所有区域可能会影响已绑定的设备！确定要删除吗？', '批量删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">严重警告：</strong>删除所有区域将会：</p>\r\n            <ul>\r\n              <li>永久移除所有区域的绘制信息</li>\r\n              <li>导致所有与这些区域关联的设备失去位置信息</li>\r\n              <li>需要重新绘制区域并重新绑定所有设备</li>\r\n            </ul>\r\n            <p>此操作不可恢复，确定要继续删除所有区域吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 收集所有区域的ID\r\n          const regionIds = this.roomNameList\r\n            .filter(room => room.id) // 只收集有ID的区域\r\n            .map(room => room.id);\r\n\r\n          if (regionIds.length > 0) {\r\n            // 有保存过的区域，调用API删除\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: regionIds\r\n            }).then(data => {\r\n              this.$message.success('已删除所有区域');\r\n\r\n              // 清空前端数据\r\n              this.finalArr = [];\r\n              this.finalArrRelative = [];\r\n              this.roomNameList = [];\r\n\r\n              // 重绘画布\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            }).catch(error => {\r\n              this.$message.error('删除失败，请重试');\r\n              console.error('批量删除区域失败:', error);\r\n            });\r\n          } else {\r\n            // 没有保存过的区域，直接清空前端数据\r\n            this.finalArr = [];\r\n            this.finalArrRelative = [];\r\n            this.roomNameList = [];\r\n\r\n            // 重绘画布\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success('已删除所有区域');\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.info('没有可删除的区域');\r\n      }\r\n    },\r\n    // 检查并删除点击的区域\r\n    checkAndDeleteArea(coords) {\r\n      const mousePoint = {x: coords.x, y: coords.y};\r\n\r\n      // 检查点击是否在任何一个区域内\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n        // 弹出确认对话框\r\n          this.$confirm(`确定要删除第${i + 1}个区域吗?`, '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n          // 删除该区域\r\n            this.finalArr.splice(i, 1);\r\n            this.finalArrRelative.splice(i, 1);\r\n\r\n            // 如果有对应的名称，也要删除\r\n            if (this.roomNameList.length > i) {\r\n              this.roomNameList.splice(i, 1);\r\n            }\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除第${i + 1}个区域`);\r\n          });\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 判断点是否在多边形内（射线法）\r\n    isPointInPolygon(point, polygon) {\r\n      if (!polygon || polygon.length < 3) {\r\n        return false;\r\n      }\r\n\r\n      let inside = false;\r\n      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\r\n        const xi = polygon[i].x, yi = polygon[i].y;\r\n        const xj = polygon[j].x, yj = polygon[j].y;\r\n\r\n        const intersect = ((yi > point.y) !== (yj > point.y))\r\n        && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);\r\n\r\n        if (intersect) {\r\n          inside = !inside;\r\n        }\r\n      }\r\n\r\n      return inside;\r\n    },\r\n\r\n\r\n    // 通过表格行点击高亮并选择区域\r\n    highlightArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 通过表格行悬停高亮区域\r\n    mouseoverArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.lastHoveredAreaIndex = this.hoveredAreaIndex;\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 表格行鼠标移出恢复之前的高亮状态\r\n    mouseoutArea(row, column, event) {\r\n      this.hoveredAreaIndex = this.lastHoveredAreaIndex;\r\n      this.redrawCanvas();\r\n    },\r\n\r\n    // 通过索引删除区域\r\n    deleteAreaByIndex(index) {\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        // 二次确认删除操作，强调可能影响已绑定设备\r\n        this.$confirm(`警告：删除\"${this.roomNameList[index].roomName}\"区域可能会影响已绑定的设备！确定要删除吗？`, '删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">警告：</strong>删除\"${this.roomNameList[index].roomName}\"区域将会：</p>\r\n            <ul>\r\n              <li>永久移除该区域的绘制信息</li>\r\n              <li>可能导致与该区域关联的设备失去位置信息</li>\r\n              <li>需要重新绑定相关设备到其他区域</li>\r\n            </ul>\r\n            <p>确定要继续删除操作吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 获取要删除的区域ID\r\n          const regionId = this.roomNameList[index].id;\r\n\r\n          // 如果有ID，调用API删除\r\n          if (regionId) {\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: [regionId]\r\n            }).then(data => {\r\n              this.$message.success(`已删除区域`);\r\n\r\n              // 删除前端数据\r\n              this.finalArr.splice(index, 1);\r\n              this.finalArrRelative.splice(index, 1);\r\n              this.roomNameList.splice(index, 1);\r\n\r\n              // 重绘画布\r\n              this.hoveredAreaIndex = -1;\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            });\r\n          } else {\r\n            // 新绘制的区域没有ID，直接从前端删除\r\n            this.finalArr.splice(index, 1);\r\n            this.finalArrRelative.splice(index, 1);\r\n            this.roomNameList.splice(index, 1);\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除区域`);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查两个多边形是否重叠\r\n    checkPolygonsOverlap(poly1, poly2) {\r\n    // 简单实现：检查每个多边形的点是否在另一个多边形内\r\n    // 或者检查多边形的边是否相交\r\n\r\n      // 检查poly1的点是否在poly2内\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        if (this.isPointInPolygon(poly1[i], poly2)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查poly2的点是否在poly1内\r\n      for (let i = 0; i < poly2.length; i++) {\r\n        if (this.isPointInPolygon(poly2[i], poly1)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查边是否相交\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        const a1 = poly1[i];\r\n        const a2 = poly1[(i + 1) % poly1.length];\r\n\r\n        for (let j = 0; j < poly2.length; j++) {\r\n          const b1 = poly2[j];\r\n          const b2 = poly2[(j + 1) % poly2.length];\r\n\r\n          if (this.doLinesIntersect(a1, a2, b1, b2)) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 检查两条线段是否相交\r\n    doLinesIntersect(p1, p2, p3, p4) {\r\n    // 线段1的方向\r\n      const d1x = p2.x - p1.x;\r\n      const d1y = p2.y - p1.y;\r\n\r\n      // 线段2的方向\r\n      const d2x = p4.x - p3.x;\r\n      const d2y = p4.y - p3.y;\r\n\r\n      // 行列式\r\n      const denominator = d2y * d1x - d2x * d1y;\r\n\r\n      // 如果行列式为0，则线段平行或共线\r\n      if (denominator === 0) {\r\n        return false;\r\n      }\r\n\r\n      // 参数t和u\r\n      const u_a = (d2x * (p1.y - p3.y) - d2y * (p1.x - p3.x)) / denominator;\r\n      const u_b = (d1x * (p1.y - p3.y) - d1y * (p1.x - p3.x)) / denominator;\r\n\r\n      // 如果t和u都在[0,1]范围内，则线段相交\r\n      return (u_a >= 0 && u_a <= 1 && u_b >= 0 && u_b <= 1);\r\n    },\r\n\r\n    // 放大画布\r\n    zoomIn() {\r\n      this.zoomLevel *= 1.1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 缩小画布\r\n    zoomOut() {\r\n      this.zoomLevel *= 0.9;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 重置缩放\r\n    resetZoom() {\r\n      this.zoomLevel = this.initialZoomLevel || 1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 应用缩放\r\n    applyZoom() {\r\n      if (!this.canvas) {\r\n        return;\r\n      }\r\n\r\n      // 应用缩放变换\r\n      this.canvas.style.transform = `scale(${this.zoomLevel})`;\r\n      this.canvas.style.transformOrigin = 'top left';\r\n\r\n      // 重绘以更新区域名称大小\r\n      this.redrawCanvas();\r\n\r\n      // 检查是否需要滚动按钮\r\n      this.checkScrollNeeded();\r\n\r\n    // 不再需要调整容器高度\r\n    // this.adjustContainerHeight();\r\n    },\r\n\r\n    // 自适应屏幕\r\n    autoFit() {\r\n      if (!this.canvas || !this.$refs.canvasContainer) {\r\n        return;\r\n      }\r\n\r\n      const container = this.$refs.canvasContainer;\r\n      const containerWidth = container.clientWidth;\r\n      const containerHeight = window.innerHeight - 100; // 减去头部和边距\r\n\r\n      const imgRatio = this.canvas.width / this.canvas.height;\r\n      const containerRatio = containerWidth / containerHeight;\r\n\r\n      let newZoomLevel;\r\n\r\n      if (imgRatio > containerRatio) {\r\n      // 宽度适配\r\n        newZoomLevel = containerWidth / this.canvas.width;\r\n      } else {\r\n      // 高度适配\r\n        newZoomLevel = containerHeight / this.canvas.height;\r\n      }\r\n\r\n      // 确保缩放系数不会过小\r\n      newZoomLevel = Math.max(newZoomLevel, 0.1);\r\n\r\n      // 应用默认缩放系数，使图片显示更大\r\n      // 限制最大缩放为2倍，避免图片过大导致性能问题\r\n      newZoomLevel = Math.min(newZoomLevel * this.defaultZoomFactor, 2.5);\r\n\r\n      this.zoomLevel = newZoomLevel;\r\n      this.initialZoomLevel = newZoomLevel;\r\n      this.applyZoom();\r\n\r\n      // 确保画布在容器中居中\r\n      this.$nextTick(() => {\r\n        const canvasContainer = this.$refs.canvasContainer;\r\n        if (canvasContainer) {\r\n        // 如果画布宽度小于容器宽度，添加水平居中样式\r\n          if (this.canvas.offsetWidth * this.zoomLevel < canvasContainer.offsetWidth) {\r\n            this.canvas.style.marginLeft = 'auto';\r\n            this.canvas.style.marginRight = 'auto';\r\n            this.canvas.style.display = 'block';\r\n          }\r\n        }\r\n      });\r\n\r\n      // this.$message.success('已自动调整图片大小');\r\n    },\r\n\r\n    // 更新事件坐标计算，考虑缩放因素\r\n    getAdjustedCoordinates(e) {\r\n      if (!this.canvas) {\r\n        return {x: e.layerX, y: e.layerY};\r\n      }\r\n\r\n      // 考虑缩放因素\r\n      const rect = this.canvas.getBoundingClientRect();\r\n      const scaleX = this.canvas.width / rect.width;\r\n      const scaleY = this.canvas.height / rect.height;\r\n\r\n      // 相对于画布的坐标\r\n      const x = (e.clientX - rect.left) * scaleX;\r\n      const y = (e.clientY - rect.top) * scaleY;\r\n\r\n      return {x, y};\r\n    },\r\n\r\n    // 切换显示区域名称\r\n    toggleAreaNames() {\r\n      this.showAreaNames = !this.showAreaNames;\r\n      this.redrawCanvas();\r\n      this.$message.success(this.showAreaNames ? '已显示区域名称' : '已隐藏区域名称');\r\n    },\r\n\r\n    // 处理默认缩放变化\r\n    handleDefaultZoomChange(command) {\r\n      const newFactor = parseFloat(command);\r\n      if (!isNaN(newFactor)) {\r\n        this.defaultZoomFactor = newFactor;\r\n        this.$message.success(`默认缩放已设置为${Math.round(newFactor * 100)}%`);\r\n        this.autoFit(); // 立即应用新的缩放设置\r\n      }\r\n    },\r\n\r\n    // 处理区域表单提交\r\n    handleAreaFormSubmit() {\r\n      this.$refs.areaForm.validate(valid => {\r\n        if (valid && this.tempAreaData) {\r\n          this.areaFormLoading = true;\r\n\r\n          // 将区域数据添加到相应的数组\r\n          this.finalArr.push(this.tempAreaData.area);\r\n          this.finalArrRelative.push(this.tempAreaData.relativeArea);\r\n\r\n          // 添加信息\r\n          this.roomNameList.push({\r\n            roomName: this.areaForm.roomName,\r\n            roomType: this.areaForm.roomType,\r\n            remark: this.areaForm.remark\r\n          });\r\n\r\n          // 清除临时数据\r\n          this.tempAreaData = null;\r\n\r\n          // 关闭表单对话框\r\n          setTimeout(() => {\r\n            this.areaFormLoading = false;\r\n            this.areaFormVisible = false;\r\n            this.$message.success('区域信息已保存');\r\n            // 重绘画布以显示新添加的区域名称\r\n            this.redrawCanvas();\r\n          }, 300);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理区域表单取消\r\n    handleAreaFormCancel() {\r\n      if (this.tempAreaData) {\r\n      // 如果取消表单，需要撤销绘制的区域\r\n        this._repeal();\r\n        this.tempAreaData = null;\r\n      }\r\n      this.areaFormVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scope>\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.draw {\r\n  min-width: 420px;\r\n  overflow-y: hidden;\r\n}\r\n\r\n.content {\r\n  flex-grow: 1;\r\n  width: 100%;\r\n  display: block; /* 修改为block，避免居中导致的偏移 */\r\n}\r\n\r\n.canvas-container {\r\n  width: 75%;\r\n  position: relative;\r\n  overflow: auto; /* 保持滚动功能 */\r\n  border: 1px solid #ebeef5;\r\n  height: 600px; /* 固定高度 */\r\n}\r\n\r\n/* 滚动控制按钮样式 */\r\n.scroll-controls {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  z-index: 100;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  border-radius: 4px;\r\n  padding: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawTop {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 5px;\r\n}\r\n\r\n.drawTop > div {\r\n  display: flex;\r\n  padding: 5px 5px;\r\n}\r\n\r\ndiv.drawTopContrllor {\r\n  // display: none;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .drawTop {\r\n    position: absolute;\r\n    background-color: white;\r\n    width: 100%;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .drawTopContrllor {\r\n    display: flex !important;\r\n    width: 100%;\r\n    padding: 0 !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}