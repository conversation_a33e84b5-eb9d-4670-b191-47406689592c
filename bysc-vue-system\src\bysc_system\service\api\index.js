// account
import account from './account/account';
import boardData from './account/boardData';
import systems from './account/systems';
import sysDict from './account/sysDict';
import codeGeneration from './account/codeGeneration';
import tenant from './account/tenant';
import thirdLogin from './account/thirdLogin';
import codeRule from './account/codeRule';
import commonModule from './account/commonModule';
import config from './account/config';
import log from './account/log';
import hrDashboard from './account/hrDashboard';
import techDashboard from './account/techDashboard';
import visualOpsManagement from './account/visualOpsManagement';
import areaManagement from './account/areaManagement';
import firstLineDept from './account/firstLineDept';
export default {
  // system module
  account,
  systems,
  sysDict,
  codeGeneration,
  tenant,
  thirdLogin,
  codeRule,
  commonModule,
  config,
  log,
  boardData,
  hrDashboard,
  techDashboard,
  visualOpsManagement,
  areaManagement,
  firstLineDept
};
