<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="log/log-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="操作人">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.opUser"
                  size="small"
                  maxlength="32"
                  placeholder="请输入操作人"
                ></el-input>
              </el-form-item>
              <el-form-item label="操作内容">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.opKeyword"
                  size="small"
                  maxlength="32"
                  placeholder="请输入操作内容"
                ></el-input>
              </el-form-item>
              <el-form-item label="操作时间">
                <el-date-picker
                  size="small"
                  v-model="operationTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  align="right"
                  @change="getOperationTime">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- <div slot="action">
            <el-button size="small" type="primary" @click="handleAdd"
              >新增</el-button
            >
            <el-button size="small" type="primary" @click="handleSyncData"
              >同步</el-button
            >
          </div> -->
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot == 'caiwubumen'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <div v-for="(cell,i) in caiwubumenList" :key="i">
                    <div v-if="cell.value == scope.row.caiwubumen">{{cell.label}}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <!-- <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  @click="handleDelete(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column> -->
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px;margin-bottom:100px;">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="110px"
          class="demo-form"
        >
          <div style="margin:0 0 10px 0;">
            <el-row>
              <el-col :span="12">
                <el-form-item label="人力部门" prop="renlibumen">
                  <el-select
                    v-model="form.renlibumen"
                    placeholder="请选择人力部门"
                    clearable
                    style="width: 100%;"
                    size="small"
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in renlibumenList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="员工姓名" prop="yuangongxingming">
                  <el-select
                    v-model="yuangongxingming"
                    placeholder="请选择员工姓名"
                    clearable
                    style="width: 100%;"
                    size="small"
                    filterable
                    multiple
                  >
                    <el-option
                      v-for="(item, index) in yuangongxingmingList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="财务组织" prop="caiwuzuzhi">
                  <el-select
                    v-model="form.caiwuzuzhi"
                    placeholder="请选择财务组织"
                    clearable
                    style="width: 100%;"
                    size="small"
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in caiwuzuzhiList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="财务部门" prop="caiwubumen">
                  <el-select
                    v-model="form.caiwubumen"
                    placeholder="请选择财务部门"
                    clearable
                    style="width: 100%;"
                    size="small"
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in caiwubumenList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            :loading="okLoading"
            @click="submitForm"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <el-dialog :title="'同步'" :visible.sync="syncDataDialog" :close-on-click-modal="false" width="30%">
      <div>
        <el-form
          :model="syncDataForm"
          :rules="syncDataFormRules"
          ref="syncDataForm"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="月份" prop="month">
                <el-select
                  v-model="syncDataForm.month"
                  placeholder="请选择月份"
                  clearable
                  style="width: 100%;"
                  size="small"
                  filterable
                >
                  <el-option
                    v-for="(item, index) in monthList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <div style="font-size:12px;color:blue;margin:0 0 0 20px;">每个月份只能同步一次，请确认该月份的数据是否填充完毕？</div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeSyncDataDialog"
          >取 消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="submitSyncData"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultForm = {
  renlibumen: '',
  yuangongxingming: '',
  caiwuzuzhi: '',
  caiwubumen: '',
};
const defaultSyncDataForm = {
  month: ''
};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    const validateYuangongxingming = (rule, value, callback) => {
      if (!this.yuangongxingming || this.yuangongxingming.length == 0) {
        callback(new Error('请选择员工姓名'));
      } else {
        callback();
      }
    };
    return {
      operationTime: null,

      monthList: [],
      syncDataFormRules: {
        month: [
          {required: true, message: '请选择月份', trigger: 'blur,change'},
        ],
      },
      syncDataForm: _.cloneDeep(defaultSyncDataForm),
      syncDataDialog: false,

      renlibumenList: [],
      yuangongxingming: [],
      yuangongxingmingList: [],
      caiwuzuzhiList: [],
      caiwubumenList: [],
      form: _.cloneDeep(defaultForm),
      okLoading: false,
      rules: {
        renlibumen: [
          {required: true, message: '请输入人力部门', trigger: 'blur'},
        ],
        yuangongxingming: [
          {required: true, validator: validateYuangongxingming, trigger: 'blur'},
        ],
        caiwuzuzhi: [
          {required: true, message: '请选择财务组织', trigger: 'blur'},
        ],
        caiwubumen: [
          {required: true, message: '请选择财务部门', trigger: 'blur'},
        ],
      },
      drawerTitle: '新增',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '操作人',
          key: 'userRealName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '操作内容',
          key: 'operationName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '操作时间',
          key: 'operationTime',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    // this.getYuangongxingmingList();
  },
  methods: {
    getYuangongxingmingList() {
      this.$api["sysDict/getParam"]({id: "QIYEJIBIE"}).then(data => {
        this.yuangongxingmingList = [];
        data.forEach(e => {
          this.yuangongxingmingList.push({
            label: e.dictName,
            value: e.dictCode,
          });
        });
      });
    },
    handleSyncData() {
      this.syncDataForm = _.cloneDeep(defaultSyncDataForm);
      this.syncDataDialog = true;
    },
    submitSyncData() {
      this.$refs.syncDataForm.validate(valid => {
        if (valid) {
          this.$confirm('确定同步数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            var loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$api['commonModule/KingdeeSupplier-save'](this.syncDataForm).then(data => {
              loading.close();
              this.$refs.grid.query();
              this.$message({
                type: 'success',
                message: '同步数据成功',
              });
              this.closeSyncDataDialog();
            }).catch(() => {
              loading.close();
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消同步数据'
            });
          });
        } else {
          return false;
        }
      });
    },
    closeSyncDataDialog() {
      this.syncDataForm = _.cloneDeep(defaultSyncDataForm);
      this.$refs.syncDataForm.resetFields();
      this.syncDataDialog = false;
    },
    handleAdd() {
      this.yuangongxingming = [];
      this.form = {};
      this.drawerTitle = '新增';
      this.drawer = true;
    },
    handleEdit(row) {
      this.yuangongxingming = [];
      this.$api['commonModule/materialFinancialAccounts-get']({id: row.id}).then(data => {
        this.form = data;
        let arr = data.yuangongxingming ? data.yuangongxingming.split(',') : [];
        arr.splice(arr.length - 1, 1);
        arr.forEach(e => {
          this.yuangongxingming.push(e);
        });
        this.drawerTitle = '编辑';
        this.drawer = true;
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['commonModule/materialFinancialAccounts-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    submitForm() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          if (this.yuangongxingming.length > 0) {
            this.form.yuangongxingming = this.yuangongxingming.join(',');
            this.form.yuangongxingming = this.form.yuangongxingming + ',';
            let arr1 = this.yuangongxingmingList.filter(v => this.yuangongxingming.some(val => val === v.value));
            let arr2 = [];
            arr1.forEach(e => {
              arr2.push(e.label);
            });
            this.form.yuangongxingmingName = arr2.join(',');
            this.form.yuangongxingmingName = this.form.yuangongxingmingName + ',';
          }
          this.$api['commonModule/materialFinancialAccounts-save'](this.form).then(data => {
            this.drawer = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.form = _.cloneDeep(defaultForm);
      this.$refs.form.resetFields();
      this.drawer = false;
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.operationTime = null;
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
    getTimes(date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? ('0' + m) : m;
      var d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      var h = date.getHours();
      h = h < 10 ? ('0' + h) : h;
      var min = date.getMinutes();
      min = min < 10 ? ('0' + min) : min;
      var s = date.getSeconds();
      s = s < 10 ? ('0' + s) : s;
      return y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + s;
    },
    getOperationTime(e) {
      if (e) {
        this.searchForm.startTime = this.getTimes(e[0]);
        this.searchForm.endTime = this.getTimes(e[1]);
      } else {
        this.searchForm.startTime = null;
        this.searchForm.endTime = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
