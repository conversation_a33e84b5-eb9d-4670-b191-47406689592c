<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="log/changePwd-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="账户名">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.account"
                  size="small"
                  maxlength="32"
                  placeholder="请输入账户名"
                ></el-input>
              </el-form-item>
              <el-form-item label="姓名">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.username"
                  size="small"
                  maxlength="32"
                  placeholder="请输入姓名"
                ></el-input>
              </el-form-item>

              <el-form-item label="修改时间">
                <el-date-picker
                  size="small"
                  v-model="operationTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  align="right"
                  @change="getOperationTime">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="tableData"
            stripe
            ref="table"
            style="width: 100%"
          >
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Vue from "vue";
import Grid from "@/components/Grid";
import _ from "lodash";
const defaultSearchForm = {};

export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      okLoading: false,
      direction: "rtl",
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: "账户名",
          key: "account",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "姓名",
          key: "username",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "修改时间",
          key: "updateTime",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "修改人",
          key: "operatorName",
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      operationTime: null,
    };
  },
  watch: {},
  methods: {
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.operationTime = null;
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
    getTimes(date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      var d = date.getDate();
      d = d < 10 ? "0" + d : d;
      var h = date.getHours();
      h = h < 10 ? "0" + h : h;
      var min = date.getMinutes();
      min = min < 10 ? "0" + min : min;
      var s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return y + "-" + m + "-" + d + " " + h + ":" + min + ":" + s;
    },
    getOperationTime(e) {
      if (e) {
        this.searchForm.beginDateTime = this.getTimes(e[0]);
        this.searchForm.endDateTime = this.getTimes(e[1]);
      } else {
        this.searchForm.beginDateTime = null;
        this.searchForm.endDateTime = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
