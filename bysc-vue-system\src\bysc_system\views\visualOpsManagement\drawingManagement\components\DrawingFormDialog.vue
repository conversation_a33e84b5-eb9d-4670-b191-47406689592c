<template>
  <el-drawer
    :title="dialogTitle"
    :visible.sync="visible"
    direction="rtl"
    size="600px"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="图纸名称" prop="drawingName">
        <el-input
          v-model.trim="formData.drawingName"
          placeholder="请输入图纸名称"
          maxlength="50"
          show-word-limit
           size="small"
          clearable
        >
        </el-input>
      </el-form-item>

      <el-form-item label="所属部门" prop="belongDeptKey">
        <el-select
          style="width: 100%"
          v-model="formData.belongDeptKey"
          placeholder="请选择"
          size="small"
          filterable
          clearable
        >
          <el-option
            :label="i.name"
            :value="i.id"
            v-for="i in deptList"
            :key="i.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="drawingInfo" required>
        <span slot="label"> 上传图片 </span>
        <el-upload
          ref="upload"
          action="#"
           size="small"
          :file-list="fileList"
          :on-change="handleFileChange"
          :before-remove="handleBeforeRemove"
          :on-remove="handleRemove"
          :on-preview="handlePreview"
          :on-exceed="handleExceed"
          :auto-upload="false"
          accept=".png,.jpg,.jpeg"
          list-type="picture-card"
          :limit="1"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">
            支持格式：PNG、JPG、JPEG格式<br />
            文件大小：图片各尺寸不超过10MB<br />
            尺寸要求：分辨率控制在2000×2000像素内<br />
            上传数量：仅一张
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="drawer-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saveLoading"
        >保存</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: "DrawingFormDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "add", // add: 新增, edit: 编辑
      validator: value => ["add", "edit"].includes(value),
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.getInit();
  },
  data() {
    return {
      deptList: [],
      formData: {
        drawingName: "",
        belongDeptKey: "",
        drawingInfo: "", // 存储base64编码的图片
      },
      fileList: [],
      saveLoading: false,
      rules: {
        drawingName: [
          {required: true, message: "请输入图纸名称", trigger: "blur"},
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        belongDeptKey: [
          {required: true, message: "请选择所属部门", trigger: "change"},
        ],

        drawingInfo: [
          {
            validator: (_rule, _value, callback) => {
              // 检查是否有图片数据（新上传的或编辑时已有的）
              if (this.formData.drawingInfo && this.formData.drawingInfo.trim() !== "") {
                callback();
              } else {
                callback(new Error("请上传图片"));
              }
            },
            trigger: ["change", "blur"],
          },
        ],
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.mode === "add" ? "新增图纸" : "编辑图纸";
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData();
      }
    },
    editData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initFormData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getInit() {
      this.$api["visualOpsManagement/workorderFirstLineDept-findAll"]({}).then(
        res => {
          this.deptList = res;
        }
      );
    },
    // 处理部门变化
    handleDeptChange(value) {
      this.formData.belongDeptKey = value;
    },

    // 初始化表单数据
    initFormData() {
      if (this.mode === "edit" && this.editData) {
        this.formData = {
          drawingName: this.editData.drawingName || "",
          belongDeptKey: this.editData.belongDeptKey || "",
          drawingInfo: this.editData.drawingInfo || "",
        };
        // 如果有已上传的图片，可以在这里处理
        if (this.editData.drawingInfo) {
          this.fileList = [
            {
              name: this.editData.imageName || "已上传图片",
              url: this.editData.drawingInfo,
              uid: Date.now(),
            },
          ];
        } else {
          this.fileList = [];
        }
      } else {
        this.formData = {
          drawingName: "",
          belongDeptKey: "",
          drawingInfo: "",
        };
        this.fileList = [];
      }

      // 清除表单验证，在编辑模式下如果有图片则重新验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          // 在编辑模式下，如果有图片数据，触发一次验证以清除可能的错误提示
          if (this.mode === "edit" && this.formData.drawingInfo) {
            this.$refs.form.validateField("drawingInfo");
          }
        }
      });
    },

    // 上传前验证
    beforeUpload(file) {
      console.log("beforeUpload 被调用:", file);

      // 检查是否已经有文件
      if (this.fileList.length >= 1) {
        this.$message.error("只能上传一张图片，请先删除已有图片!");
        return false;
      }

      const isImage = /^image\/(png|jpe?g)$/i.test(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      console.log("文件类型检查:", isImage, file.type);
      console.log("文件大小检查:", isLt10M, file.size);

      if (!isImage) {
        this.$message.error("上传图片只能是 PNG、JPG、JPEG 格式!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("上传图片大小不能超过 10MB!");
        return false;
      }

      console.log("开始转换为base64");
      // 将文件转换为base64
      this.convertToBase64(file);

      // 阻止默认上传行为，因为我们使用base64方式
      return false;
    },

    // 将文件转换为base64
    convertToBase64(file) {
      const reader = new FileReader();
      reader.onload = e => {
        this.formData.drawingInfo = e.target.result;

        // 手动添加到文件列表用于显示
        this.fileList = [
          {
            name: file.name,
            url: e.target.result,
            uid: file.uid || Date.now(),
          },
        ];

        // 触发表单验证，清除drawingInfo字段的错误提示
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.validateField("drawingInfo");
          }
        });

        this.$message.success("图片上传成功");
      };
      reader.onerror = error => {
        console.error("FileReader 错误:", error);
        this.$message.error("图片读取失败");
      };
      reader.readAsDataURL(file);
    },

    // 预览图片（Element UI 上传组件的预览回调）
    handlePreview(file) {
      // 使用文件的 url 或者 formData 中的 drawingInfo进行预览
      const imageUrl = file.url || this.formData.drawingInfo;
      if (imageUrl) {
        // 创建一个新窗口来显示图片
        const newWindow = window.open("", "_blank");
        newWindow.document.write(`
          <html>
            <head><title>图片预览 - ${file.name || "图片"}</title></head>
            <body style="margin:0;padding:20px;text-align:center;background:#f5f5f5;">
              <div style="margin-bottom:10px;font-size:16px;color:#333;">
                ${file.name || "图片预览"}
              </div>
              <img src="${imageUrl}" style="max-width:100%;max-height:90vh;object-fit:contain;border:1px solid #ddd;border-radius:4px;" alt="预览图片" />
            </body>
          </html>
        `);
        newWindow.document.close();
      } else {
        this.$message.warning("无法预览图片");
      }
    },

    // 预览图片（通用方法，保留兼容性）
    previewImage() {
      if (this.formData.drawingInfo) {
        // 创建一个新窗口来显示图片
        const newWindow = window.open("", "_blank");
        newWindow.document.write(`
          <html>
            <head><title>图片预览</title></head>
            <body style="margin:0;padding:20px;text-align:center;background:#f5f5f5;">
              <img src="${this.formData.drawingInfo}" style="max-width:100%;max-height:100vh;object-fit:contain;" alt="预览图片" />
            </body>
          </html>
        `);
        newWindow.document.close();
      }
    },

    // 替换图片
    replaceImage() {
      this.$refs.hiddenFileInput.click();
    },

    // 删除图片
    removeImage() {
      this.$confirm("确定要删除当前图片吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.formData.drawingInfo = "";
          this.currentImageName = "";
          this.currentImageSize = 0;
          this.fileList = [];
          this.$message.success("图片已删除");
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 处理文件选择变化（Element UI 的 on-change 事件）
    handleFileChange(file, fileList) {
      // 检查是否已经有文件
      if (fileList.length > 1) {
        this.$message.error("只能上传一张图片，请先删除已有图片!");
        // 移除新添加的文件，保留第一个
        this.fileList = [fileList[0]];
        return;
      }

      if (file && file.raw) {
        const rawFile = file.raw;

        // 验证文件
        const isImage = /^image\/(png|jpe?g)$/i.test(rawFile.type);
        const isLt10M = rawFile.size / 1024 / 1024 < 10;

        if (!isImage) {
          this.$message.error("上传图片只能是 PNG、JPG、JPEG 格式!");
          this.fileList = [];
          return;
        }
        if (!isLt10M) {
          this.$message.error("上传图片大小不能超过 10MB!");
          this.fileList = [];
          return;
        }

        // 转换为base64
        this.convertToBase64(rawFile);
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) {
        return "0 B";
      }
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },

    // 删除前确认（Element UI 的 before-remove 钩子）
    handleBeforeRemove() {
      return new Promise((resolve, reject) => {
        this.$confirm("确定要删除当前图片吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 用户确认删除，允许删除操作
            resolve(); // 允许删除
          })
          .catch(() => {
            // 用户取消删除
            reject(); // 阻止删除
          });
      });
    },

    // 移除文件（Element UI 删除完成后的回调）
    handleRemove() {
      // 文件删除完成后，清空相关数据
      this.formData.drawingInfo = "";
      this.currentImageName = "";
      this.currentImageSize = 0;

      // 触发表单验证，显示drawingInfo字段的错误提示
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField("drawingInfo");
        }
      });

      this.$message.success("图片已删除");
      console.log("文件删除完成");
    },

    // 超出文件数量限制（保留方法以兼容旧代码）
    handleExceed() {
      this.$message.warning(
        "只能上传一张图片，请先删除已有图片后再上传新图片!"
      );
    },

    // 保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveLoading = true;

          // 根据选中的部门ID找到对应的部门名称
          const selectedDept = this.deptList.find(dept => dept.id === this.formData.belongDeptKey);
          const belongDept = selectedDept ? selectedDept.name : '';

          // 准备保存的数据
          const saveData = {
            drawingName: this.formData.drawingName,
            belongDeptKey: this.formData.belongDeptKey,
            belongDept: belongDept, // 添加部门名称（中文）
            drawingInfo: this.formData.drawingInfo,
            id: this.editData && this.editData.id,
            // ...this.formData,
            // mode: this.mode,
            // editId: this.editData && this.editData.id,
          };

          console.log("保存数据:", saveData);
          console.log("选中的部门:", selectedDept);
          console.log("部门名称:", belongDept);
          console.log("图片Base64长度:", this.formData.drawingInfo ? this.formData.drawingInfo.length : 0);

          this.$api["visualOpsManagement/visualOpsManagement-save"](saveData).then(
            () => {
              this.saveLoading = false;
              this.$message.success(
                this.mode === "add" ? "新增成功" : "编辑成功"
              );

              // 触发保存事件，传递表单数据
              this.$emit("save", saveData);

              this.handleClose();
            }
          );
        }
      });
    },

    // 取消
    handleCancel() {
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: right;
  z-index: 1;
}

.el-form {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
}

/deep/ .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

/deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

/deep/ .el-drawer__body {
  position: relative;
  padding: 20px;
}
</style>
