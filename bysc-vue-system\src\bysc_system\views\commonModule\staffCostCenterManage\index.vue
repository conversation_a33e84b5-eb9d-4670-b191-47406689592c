<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="systems/costStaffRel-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="人力部门">
                <el-input
                  v-model="searchForm.organizationName"
                  placeholder="请输入"
                  size="small"
                  clearable
                />
              </el-form-item>
              <el-form-item label="员工">
                <el-input
                  v-model="searchForm.userRealName"
                  placeholder="请输入"
                  size="small"
                  clearable
                />
              </el-form-item>
              <el-form-item label="成本中心编码">
                <el-input
                  v-model="searchForm.costCenterNo"
                  placeholder="请输入"
                  size="small"
                  clearable
                />
              </el-form-item>
              <el-form-item label="成本中心">
                <el-input
                  v-model="searchForm.costCenterName"
                  placeholder="请输入"
                  size="small"
                  clearable
                />
              </el-form-item>

              <el-form-item label="是否找到多个成本中心" prop="trainPlan">
                <el-select
                  style="width: 100%"
                  v-model="searchForm.multiModule"
                  placeholder="请选择"
                  size="small"
                  filterable
                >
                  <el-option
                    :label="i.label"
                    :value="i.value"
                    v-for="i in isMultiModule"
                    :key="i.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <!-- <el-button
              v-permission="'staffCostCenterManage_add'"
              size="small"
              type="primary"
              @click="handleEdit(0)"
              >新增</el-button
            > -->
            <el-button
              v-permission="'staffCostCenterManage_async'"
              size="small"
              type="primary"
              @click="handleAsync(0)"
              >同步</el-button
            >
            <el-button
              v-permission="'staffCostCenterManage_add'"
              size="small"
              type="primary"
              @click="bindData(0)"
              >自动绑定</el-button
            >
          </div>
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="tableData"
            stripe
            ref="table"
            style="width: 100%"
          >
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="organizationName"
              label="人力部门"
              min-width="180"
            >
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="userRealName"
              label="员工"
              min-width="180"
            >
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="costCenterNo"
              label="成本中心编码"
              min-width="180"
            >
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="costCenterName"
              label="成本中心"
              min-width="180"
            >
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="moduleNames"
              label="人员模块"
              min-width="180"
            >
            </el-table-column>

            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="moduleNames"
              label="是否同步"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ scope.row.sync ? "是":'否'}}
              </template>
            </el-table-column>

            <el-table-column
              :show-overflow-tooltip="true"
              :align="'center'"
              prop="moduleNames"
              label="是否找到多个成本中心"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ scope.row.multiModule ? "是":'否'}}
              </template>
            </el-table-column>

            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'staffCostCenterManage_edit'"
                  @click="handleEdit(1, scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  v-permission="'staffCostCenterManage_del'"
                  type="text"
                  size="small"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <!-- 新增 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      width="40%"
    >
      <el-form
        :model="dialogForm"
        :rules="rules"
        ref="form"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item
          label="人力部门"
          prop="organizationId"
          v-if="showType == 0"
        >
          <!-- <selectFilterTree :value="dialogForm.organizationId" :cnvalue="dialogForm.organizationName" ref="depttrees"
            :defaultData="deptTreeList" :treeProps="deptprops" @getSelectData="getSearchDeptData" treeplaceholder="请选择"
            :treeKey="'id'">
          </selectFilterTree> -->
          <el-cascader
            ref="cascader"
            v-model="organizationIds"
            :options="deptTreeList"
            size="small"
            :props="deptprops"
            clearable
            filterable
            @change="changeOrganization"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="员工" prop="userIdList" v-if="showType == 0">
          <el-select
            v-model="dialogForm.userIdList"
            placeholder="请选择"
            collapse-tags
            clearable
            filterable
            multiple
            size="small"
          >
            <el-option
              v-for="(item, index) in userList"
              :key="index"
              :label="item.realName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenterId">
          <el-select
            v-model="dialogForm.costCenterId"
            placeholder="请选择"
            clearable
            filterable
            size="small"
          >
          <!-- :label="item.costCenterName + '(' + item.departName + ')'" -->
            <el-option
              v-for="(item, index) in costCenterList"
              :key="index"
              :label="`${item.costCenterName}(${item.departNo})`"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否同步" prop="sync">
          <el-radio-group v-model="dialogForm.sync">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="okLoading"
          @click="submitForm()"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from "vue";
import Grid from "@/components/Grid";
import selectFilterTree from "@/components/treeComp/selectFilterTree.vue";
import {getFathersById} from "@/utils/tools.js";
import _ from "lodash";
const defaultSearchForm = {};
const defaultDialogForm = {
  organizationId: "",
  userIdList: [],
  costCenterId: "",
};
export default {
  components: {
    Grid,
    selectFilterTree,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      isMultiModule: [
        {value: true, label: "是"},
        {value: false, label: "否"},
      ],
      loading: false,
      okLoading: false,
      rules: {
        organizationId: [
          {required: true, message: "请选择人力部门", trigger: "blur,change"},
        ],
        userIdList: [
          {required: true, message: "请选择员工", trigger: "blur,change"},
        ],
        costCenterId: [
          {required: true, message: "请选择成本中心", trigger: "blur,change"},
        ],
        sync: [
          {required: true, message: "请选择是否同步", trigger: "blur,change"},
        ],
      },
      showDialog: false,
      searchForm: _.cloneDeep(defaultSearchForm),
      tableData: [],
      dialogForm: _.cloneDeep(defaultDialogForm),
      dialogTitle: "新增",
      showType: 0, // 0新增 1编辑
      costCenterList: [],
      userList: [],
      deptTreeList: [],
      organizationIds: [],
      deptprops: {
        children: "children",
        label: "organizationName",
        value: "id",
      },
    };
  },
  watch: {},
  computed: {
    organizationId() {
      return this.$store.state.common.userInfo.organizationId;
    },
  },
  mounted() {
    this.getDeptData();
  },
  methods: {
    getDeptData() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.deptTreeList = data;
      });
    },
    changeOrganization(e) {
      this.dialogForm.userIdList = [];
      this.dialogForm.costCenterId = "";
      this.dialogForm.organizationId = e[e.length - 1];
      this.getUserList();
      this.getcostCenterList();
    },
    getSearchDeptData(e) {
      this.dialogForm.organizationId = e;
      this.getUserList();
      this.getcostCenterList();
    },
    getUserList() {
      // 员工列表
      this.$api["systems/userList"]({
        organizationId: this.dialogForm.organizationId,
      }).then(data => {
        this.userList = data;
        this.dialogForm.userIdList = data.map(item => {
          return item.id;
        });
      });
    },
    async getcostCenterList() {
      // 成本中心列表
      // let id = this.$refs.cascader.getCheckedNodes(true)
      console.log("111", this.dialogForm, this.currentRow);
      await this.$api["systems/costCenter-list-new"]({
        // organizationIds: this.dialogForm.organizationId,
        moduleId: this.currentRow.moduleIds,
        // orgId: this.currentRow.organizationId,
        orgId: this.currentRow.organizationId
      }).then(data => {
        this.costCenterList = data;
      });
    },
    bindData() {
      const loading = this.$loading({
        lock: true,
        text: "绑定数据中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$api["systems/bindData"]()
        .then(data => {
          this.$message({
            type: "success",
            message: "绑定成功",
          });
          loading.close();
        })
        .catch(err => {
          loading.close();
        });
    },
    async handleAsync() {
      this.$confirm("确定同步数据？", "同步提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: "同步中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          this.$api["systems/bind-cost-staff"]({}).then(data => {
            loading.close();
            (this.loading = false), this.$refs.grid.query();
            this.$message({
              type: "success",
              message: "同步成功",
            });
          });
        })
        .catch(() => {
          loading.close();
          this.$message({
            type: "info",
            message: "已取消同步",
          });
        });
    },
    async handleEdit(type, row) {
      this.showType = type;
      this.currentRow = row;
      this.dialogTitle = type == 0 ? "新增" : "编辑";
      if (type == 0) {
        this.dialogForm.organizationId = this.organizationId;
        this.dialogForm.costCenterId = "";
        await this.getUserList();
      } else {
        await this.$api["systems/costStaffRel-dts"]({id: row.id}).then(
          data => {
            this.dialogForm = _.cloneDeep(data);
          }
        );
      }
      let deptData = getFathersById(
        this.dialogForm.organizationId,
        this.deptTreeList
      );
      this.organizationIds = deptData;
      await this.getcostCenterList();
      this.showDialog = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleDelete(e) {
      this.$confirm("确定删除该数据？", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api["systems/costStaffRel-del"]({id: e.id}).then(data => {
            this.$refs.grid.query();
            this.$message({
              type: "success",
              message: "删除成功",
            });
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    submitForm() {
      this.okLoading = true;
      this.$refs.form.validate(valid => {
        if (valid) {
          let url
            = this.showType == 0
              ? "systems/costStaffRel-save"
              : "systems/costStaffRel-change";
          this.$api[url](this.dialogForm)
            .then(data => {
              this.showDialog = false;
              this.okLoading = false;
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$nextTick(() => {
                this.$refs.grid.query();
              });
            })
            .catch(() => {
              this.okLoading = false;
            });
        } else {
          this.okLoading = false;
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select,
.el-cascader {
  width: 100%;
}
</style>
