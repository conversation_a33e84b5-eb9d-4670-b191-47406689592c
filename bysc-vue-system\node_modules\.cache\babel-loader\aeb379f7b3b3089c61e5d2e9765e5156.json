{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\routes\\system.js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\routes\\system.js", "mtime": 1754276220624}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1752725539784}], "contextDependencies": [], "result": ["import Main from '@/components/main/Main';\n// 支付模块的路由\n\nvar systemRoutes = [{\n  name: 'system/cfg',\n  path: '/system/cfg',\n  component: Main,\n  redirect: '/permission',\n  meta: {\n    showInMenu: true,\n    showAlways: true,\n    // 表示一定要展示子菜单\n    title: '系统管理'\n  },\n  children: [{\n    name: 'systemHome',\n    path: '/system/home',\n    component: function component() {\n      return import('@/bysc_system/views/home');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/home',\n      title: '首页',\n      icon: 'logo-apple'\n    }\n  }, {\n    name: 'permission',\n    path: '/permission',\n    redirect: '/system/user/table',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/permission',\n      title: '权限管理',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'userTable',\n      path: '/system/user/table',\n      component: function component() {\n        return import('@/bysc_system/views/user');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/user/table',\n        title: '用户管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'roleTable',\n      path: '/system/role/table',\n      component: function component() {\n        return import('@/bysc_system/views/role');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/role/table',\n        title: '角色管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'resourceTable',\n      path: '/system/resource/table',\n      component: function component() {\n        return import('@/bysc_system/views/menus');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/resource/table',\n        title: '资源管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'organizationTable',\n      path: '/system/organization/table',\n      component: function component() {\n        return import('@/bysc_system/views/organization');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/organization/table',\n        title: '组织管理',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'systemBase',\n    path: '/system/base',\n    redirect: '/system/dict/table',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/base',\n      title: '基础配置',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'dictTable',\n      path: '/system/dict/table',\n      component: function component() {\n        return import('@/bysc_system/views/sysDict');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/dict/table',\n        title: '系统字典',\n        icon: 'logo-apple'\n      }\n    },\n    // {\n    //   name: 'dictBusinessTable',\n    //   path: '/system/dict/business/table',\n    //   component: () => import('@/bysc_system/views/businessDict'),\n    //   meta: {\n    //     showInMenu: true,\n    //     path: '/system/dict/business/table',\n    //     title: '业务字典',\n    //     icon: 'logo-apple'\n    //   }\n    // },\n    {\n      name: 'tenantTable',\n      path: '/base/tenant/table',\n      component: function component() {\n        return import('@/bysc_system/views/tenant');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/base/tenant/table',\n        title: '租户管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'codeRuleTable',\n      path: '/base/codeRule/table',\n      component: function component() {\n        return import('@/bysc_system/views/codeRule');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/base/codeRule/table',\n        title: '编码规则',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'codeRuleSectionTable',\n      path: '/base/codeRuleSection/table',\n      component: function component() {\n        return import('@/bysc_system/views/codeRule/codeRuleSection');\n      },\n      meta: {\n        showInMenu: false,\n        path: '/base/codeRuleSection/table',\n        title: '编码规则组成',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'config',\n      path: '/system/base/config',\n      component: function component() {\n        return import('@/bysc_system/views/config');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/base/config',\n        title: '系统设置',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'systemCode',\n    path: '/system/code',\n    redirect: '/system/code/generation',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/code',\n      title: '代码生成',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'codeGeneration',\n      path: '/system/code/generation',\n      component: function component() {\n        return import('@/bysc_system/views/codeGeneration');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/code/generation',\n        title: '代码生成',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'thirdLogin',\n    path: '/system/thirdLogin',\n    redirect: '/system/thirdLogin/thirdList',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/thirdLogin',\n      title: '第三方登录',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'thirdList',\n      path: '/system/thirdLogin/thirdList',\n      component: function component() {\n        return import('@/bysc_system/views/thirdLogin');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/thirdLogin/thirdList',\n        title: '第三方列表',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'commonModule',\n    path: '/commonModule',\n    redirect: '/system/commonModule/supplier',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      showAlways: true,\n      // 表示一定要展示子菜单\n      title: '公共模块'\n    },\n    children: [{\n      name: 'supplier',\n      path: '/system/commonModule/supplier',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/supplier');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/supplier',\n        title: '供应商',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'organization',\n      path: '/system/commonModule/organization',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/organization');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/organization',\n        title: '组织管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'kingdeeUser',\n      path: '/system/commonModule/kingdeeUser',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/kingdeeUser');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/kingdeeUser',\n        title: '金蝶账号',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'jdUser',\n      path: '/system/commonModule/jdUser',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/jdUser');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/jdUser',\n        title: '京东慧采账号',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'costCenterManage',\n      path: '/system/commonModule/costCenterManage',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/costCenterManage');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/costCenterManage',\n        title: '成本中心管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'jobNumberManage',\n      path: '/system/commonModule/jobNumberManage',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/jobNumberManage');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/jobNumberManage',\n        title: '工号管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'staffCostCenterManage',\n      path: '/system/commonModule/staffCostCenterManage',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/staffCostCenterManage');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/staffCostCenterManage',\n        title: '员工成本中心管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'finance',\n      path: '/system/commonModule/finance',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/finance');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/finance',\n        title: '财务科目',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'staffJobNumberManage',\n      path: '/system/commonModule/staffJobNumberManage',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/staffJobNumberManage');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/staffJobNumberManage',\n        title: '员工工时管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'salaryReport',\n      path: '/system/commonModule/salaryReport',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/salaryReport');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/salaryReport',\n        title: '工资报表生成',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'expense',\n      path: '/system/commonModule/expense',\n      component: function component() {\n        return import('@/bysc_system/views/commonModule/expense');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/commonModule/expense',\n        title: '费用项目',\n        icon: 'logo-apple'\n      }\n    }]\n  }, {\n    name: 'log',\n    path: '/log',\n    redirect: '/system/log/auditLog',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      showAlways: true,\n      // 表示一定要展示子菜单\n      title: '日志管理'\n    },\n    children: [{\n      name: 'auditLog',\n      path: '/system/log/auditLog',\n      component: function component() {\n        return import('@/bysc_system/views/log/auditLog');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/log/auditLog',\n        title: '审计日志',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'loginLog',\n      path: '/system/log/loginLog',\n      component: function component() {\n        return import('@/bysc_system/views/log/loginLog');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/log/loginLog',\n        title: '登录审计',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'changePwdLog',\n      path: '/system/log/changePwdLog',\n      component: function component() {\n        return import('@/bysc_system/views/log/changePwdLog');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/log/changePwdLog',\n        title: '修改密码日志',\n        icon: 'logo-apple'\n      }\n    }]\n  },\n  // 看板管理\n  {\n    name: 'boardData',\n    path: '/system/boardData',\n    redirect: '/system/boardData/capital',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/boardData',\n      title: '看板数据管理',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'capitalAirport',\n      path: '/system/boardData/capital',\n      redirect: '/system/boardData/capital/forecast',\n      component: function component() {\n        return import('@/components/main/components/empty-router-view/empty');\n      },\n      meta: {\n        showInMenu: true,\n        title: '首都机场',\n        icon: 'logo-apple'\n      },\n      children: [{\n        name: 'forecastManage',\n        path: '/system/boardData/capital/forecast',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/forecast');\n        },\n        meta: {\n          showInMenu: true,\n          title: '值班领导管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'yesterdayFlights',\n        path: '/system/boardData/capital/yesterdayFlights',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/yesterdayFlights');\n        },\n        meta: {\n          showInMenu: true,\n          title: '昨日行李量管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'flightThreeTable',\n        path: '/system/boardData/capital/flightThree',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/flightThree');\n        },\n        meta: {\n          showInMenu: true,\n          title: '首都行李数据管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'yesterdayFlightData',\n        path: '/system/boardData/capital/yesterdayFlightData',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/yesterdayFlightData');\n        },\n        meta: {\n          showInMenu: true,\n          // title: '昨日航班/航班/保障航班架次数据管理',\n          title: '航班及旅客管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'yesterdayRouteDistance',\n        path: '/system/boardData/capital/yesterdayRouteDistance',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/yesterdayRouteDistance');\n        },\n        meta: {\n          showInMenu: true,\n          title: '昨日行驶里程管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'operationPassengerVolume',\n        path: '/system/boardData/capital/operationPassengerVolume',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/operationPassengerVolume');\n        },\n        meta: {\n          showInMenu: true,\n          title: '首都捷运数据管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'capitalAPU',\n        path: '/system/boardData/capital/APU',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/capital/APU');\n        },\n        meta: {\n          showInMenu: true,\n          title: '首都客桥数据管理',\n          icon: 'logo-apple'\n        }\n      }]\n    },\n    // 大兴机场\n    {\n      name: 'daxingAirport',\n      path: '/system/boardData/daxing',\n      redirect: '/system/boardData/daxing/forecast',\n      component: function component() {\n        return import('@/components/main/components/empty-router-view/empty');\n      },\n      meta: {\n        showInMenu: true,\n        title: '大兴机场',\n        icon: 'logo-apple'\n      },\n      children: [{\n        name: 'daxingForecastManage',\n        path: '/system/boardData/daxing/forecast',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/daxing/forecast');\n        },\n        meta: {\n          showInMenu: true,\n          title: '值班领导管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'daxingYesterdayFlights',\n        path: '/system/boardData/daxing/yesterdayFlights',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/daxing/yesterdayFlights');\n        },\n        meta: {\n          showInMenu: true,\n          title: '昨日行李量管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'daxingFlightThreeTable',\n        path: '/system/boardData/daxing/flightThree',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/daxing/flightThree');\n        },\n        meta: {\n          showInMenu: true,\n          title: '大兴行李数据管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'daxingYesterdayFlightData',\n        path: '/system/boardData/daxing/yesterdayFlightData',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/daxing/yesterdayFlightData');\n        },\n        meta: {\n          showInMenu: true,\n          // title: '昨日航班/航班/保障航班架次数据管理',\n          title: '航班及旅客管理',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'daxingAPU',\n        path: '/system/boardData/daxing/APU',\n        component: function component() {\n          return import('@/bysc_system/views/boardData/daxing/APU');\n        },\n        meta: {\n          showInMenu: true,\n          title: '大兴客桥数据管理',\n          icon: 'logo-apple'\n        }\n      }]\n    }]\n  },\n  // 新增技术部看板\n  {\n    name: 'techDashboard',\n    path: '/system/techDashboard',\n    redirect: '/system/techDashboard/techData',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      path: '/system/techDashboard',\n      title: '技术部看板',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'techData',\n      path: '/system/techDashboard/techData',\n      component: function component() {\n        return import('@/bysc_system/views/techDashboard/techData');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/techDashboard/techData',\n        title: '技术管理数据',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'safetyManagement',\n      path: '/system/techDashboard/safety',\n      redirect: '/system/techDashboard/safety/capital',\n      component: function component() {\n        return import('@/components/main/components/empty-router-view/empty');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/techDashboard/safety',\n        title: '安全管理数据',\n        icon: 'logo-apple'\n      },\n      children: [{\n        name: 'capitalSafetyData',\n        path: '/system/techDashboard/safety/capital',\n        component: function component() {\n          return import('@/bysc_system/views/techDashboard/safety/capital');\n        },\n        meta: {\n          showInMenu: true,\n          path: '/system/techDashboard/safety/capital',\n          title: '首都安全管理数据',\n          icon: 'logo-apple'\n        }\n      }, {\n        name: 'daxingSafetyData',\n        path: '/system/techDashboard/safety/daxing',\n        component: function component() {\n          return import('@/bysc_system/views/techDashboard/safety/daxing');\n        },\n        meta: {\n          showInMenu: true,\n          path: '/system/techDashboard/safety/daxing',\n          title: '大兴安全管理数据',\n          icon: 'logo-apple'\n        }\n      }]\n    }]\n  },\n  // 新增人力看板菜单\n  {\n    name: 'hrDashboard',\n    path: '/system/hrDashboard',\n    redirect: '/system/hrDashboard/overview',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      showAlways: true,\n      title: '人力看板',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'hrOverview',\n      path: '/system/hrDashboard/overview',\n      component: function component() {\n        return import('@/bysc_system/views/hrDashboard/overview');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/hrDashboard/overview',\n        title: '人力概览',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'enterData',\n      path: '/system/hrDashboard/enterData',\n      component: function component() {\n        return import('@/bysc_system/views/hrDashboard/enterData');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/hrDashboard/enterData',\n        title: '数据录入',\n        icon: 'logo-apple'\n      }\n    }]\n  },\n  // 可视化运维管理\n\n  {\n    name: 'visualOpsManagement',\n    path: '/system/visualOpsManagement',\n    redirect: '/system/visualOpsManagement/drawingManagement',\n    component: function component() {\n      return import('@/components/main/components/empty-router-view/empty');\n    },\n    meta: {\n      showInMenu: true,\n      showAlways: true,\n      title: '可视化运维管理',\n      icon: 'logo-apple'\n    },\n    children: [{\n      name: 'drawingManagement',\n      path: '/system/visualOpsManagement/drawingManagement',\n      component: function component() {\n        return import('@/bysc_system/views/visualOpsManagement/drawingManagement');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/visualOpsManagement/drawingManagement',\n        title: '图纸管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'areaManagement',\n      path: '/system/visualOpsManagement/areaManagement',\n      component: function component() {\n        return import('@/bysc_system/views/visualOpsManagement/areaManagement');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/visualOpsManagement/areaManagement',\n        title: '区域绘制',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'areaBinding',\n      path: '/system/visualOpsManagement/areaBinding',\n      component: function component() {\n        return import('@/bysc_system/views/visualOpsManagement/areaBinding');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/visualOpsManagement/areaBinding',\n        title: '区域管理',\n        icon: 'logo-apple'\n      }\n    }, {\n      name: 'deviceBinding',\n      path: '/system/visualOpsManagement/deviceBinding',\n      component: function component() {\n        return import('@/bysc_system/views/visualOpsManagement/deviceBinding');\n      },\n      meta: {\n        showInMenu: true,\n        path: '/system/visualOpsManagement/deviceBinding',\n        title: '区域绑定设备',\n        icon: 'logo-apple'\n      }\n    }]\n  }]\n}];\nexport default systemRoutes;", {"version": 3, "names": ["Main", "systemRoutes", "name", "path", "component", "redirect", "meta", "showInMenu", "showAlways", "title", "children", "icon"], "sources": ["D:/boweiWorkSpace/pc/bysc-vue-system/src/bysc_system/routes/system.js"], "sourcesContent": ["import Main from '@/components/main/Main';\r\n// 支付模块的路由\r\n\r\nconst systemRoutes = [\r\n  {\r\n    name: 'system/cfg',\r\n    path: '/system/cfg',\r\n    component: Main,\r\n    redirect: '/permission',\r\n    meta: {\r\n      showInMenu: true,\r\n      showAlways: true, // 表示一定要展示子菜单\r\n      title: '系统管理',\r\n    },\r\n    children: [\r\n      {\r\n        name: 'systemHome',\r\n        path: '/system/home',\r\n        component: () => import('@/bysc_system/views/home'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/home',\r\n          title: '首页',\r\n          icon: 'logo-apple'\r\n        }\r\n      },\r\n      {\r\n        name: 'permission',\r\n        path: '/permission',\r\n        redirect: '/system/user/table',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/permission',\r\n          title: '权限管理',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'userTable',\r\n            path: '/system/user/table',\r\n            component: () => import('@/bysc_system/views/user'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/user/table',\r\n              title: '用户管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'roleTable',\r\n            path: '/system/role/table',\r\n            component: () => import('@/bysc_system/views/role'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/role/table',\r\n              title: '角色管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'resourceTable',\r\n            path: '/system/resource/table',\r\n            component: () => import('@/bysc_system/views/menus'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/resource/table',\r\n              title: '资源管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'organizationTable',\r\n            path: '/system/organization/table',\r\n            component: () => import('@/bysc_system/views/organization'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/organization/table',\r\n              title: '组织管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'systemBase',\r\n        path: '/system/base',\r\n        redirect: '/system/dict/table',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/base',\r\n          title: '基础配置',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'dictTable',\r\n            path: '/system/dict/table',\r\n            component: () => import('@/bysc_system/views/sysDict'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/dict/table',\r\n              title: '系统字典',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          // {\r\n          //   name: 'dictBusinessTable',\r\n          //   path: '/system/dict/business/table',\r\n          //   component: () => import('@/bysc_system/views/businessDict'),\r\n          //   meta: {\r\n          //     showInMenu: true,\r\n          //     path: '/system/dict/business/table',\r\n          //     title: '业务字典',\r\n          //     icon: 'logo-apple'\r\n          //   }\r\n          // },\r\n          {\r\n            name: 'tenantTable',\r\n            path: '/base/tenant/table',\r\n            component: () => import('@/bysc_system/views/tenant'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/base/tenant/table',\r\n              title: '租户管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'codeRuleTable',\r\n            path: '/base/codeRule/table',\r\n            component: () => import('@/bysc_system/views/codeRule'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/base/codeRule/table',\r\n              title: '编码规则',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'codeRuleSectionTable',\r\n            path: '/base/codeRuleSection/table',\r\n            component: () => import('@/bysc_system/views/codeRule/codeRuleSection'),\r\n            meta: {\r\n              showInMenu: false,\r\n              path: '/base/codeRuleSection/table',\r\n              title: '编码规则组成',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'config',\r\n            path: '/system/base/config',\r\n            component: () => import('@/bysc_system/views/config'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/base/config',\r\n              title: '系统设置',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'systemCode',\r\n        path: '/system/code',\r\n        redirect: '/system/code/generation',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/code',\r\n          title: '代码生成',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'codeGeneration',\r\n            path: '/system/code/generation',\r\n            component: () => import('@/bysc_system/views/codeGeneration'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/code/generation',\r\n              title: '代码生成',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'thirdLogin',\r\n        path: '/system/thirdLogin',\r\n        redirect: '/system/thirdLogin/thirdList',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/thirdLogin',\r\n          title: '第三方登录',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'thirdList',\r\n            path: '/system/thirdLogin/thirdList',\r\n            component: () => import('@/bysc_system/views/thirdLogin'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/thirdLogin/thirdList',\r\n              title: '第三方列表',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'commonModule',\r\n        path: '/commonModule',\r\n        redirect: '/system/commonModule/supplier',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          showAlways: true, // 表示一定要展示子菜单\r\n          title: '公共模块',\r\n        },\r\n        children: [\r\n          {\r\n            name: 'supplier',\r\n            path: '/system/commonModule/supplier',\r\n            component: () => import('@/bysc_system/views/commonModule/supplier'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/supplier',\r\n              title: '供应商',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'organization',\r\n            path: '/system/commonModule/organization',\r\n            component: () => import('@/bysc_system/views/commonModule/organization'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/organization',\r\n              title: '组织管理',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'kingdeeUser',\r\n            path: '/system/commonModule/kingdeeUser',\r\n            component: () => import('@/bysc_system/views/commonModule/kingdeeUser'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/kingdeeUser',\r\n              title: '金蝶账号',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'jdUser',\r\n            path: '/system/commonModule/jdUser',\r\n            component: () => import('@/bysc_system/views/commonModule/jdUser'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/jdUser',\r\n              title: '京东慧采账号',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'costCenterManage',\r\n            path: '/system/commonModule/costCenterManage',\r\n            component: () => import('@/bysc_system/views/commonModule/costCenterManage'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/costCenterManage',\r\n              title: '成本中心管理',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'jobNumberManage',\r\n            path: '/system/commonModule/jobNumberManage',\r\n            component: () => import('@/bysc_system/views/commonModule/jobNumberManage'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/jobNumberManage',\r\n              title: '工号管理',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'staffCostCenterManage',\r\n            path: '/system/commonModule/staffCostCenterManage',\r\n            component: () => import('@/bysc_system/views/commonModule/staffCostCenterManage'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/staffCostCenterManage',\r\n              title: '员工成本中心管理',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'finance',\r\n            path: '/system/commonModule/finance',\r\n            component: () => import('@/bysc_system/views/commonModule/finance'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/finance',\r\n              title: '财务科目',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'staffJobNumberManage',\r\n            path: '/system/commonModule/staffJobNumberManage',\r\n            component: () => import('@/bysc_system/views/commonModule/staffJobNumberManage'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/staffJobNumberManage',\r\n              title: '员工工时管理',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'salaryReport',\r\n            path: '/system/commonModule/salaryReport',\r\n            component: () => import('@/bysc_system/views/commonModule/salaryReport'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/salaryReport',\r\n              title: '工资报表生成',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'expense',\r\n            path: '/system/commonModule/expense',\r\n            component: () => import('@/bysc_system/views/commonModule/expense'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/commonModule/expense',\r\n              title: '费用项目',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        name: 'log',\r\n        path: '/log',\r\n        redirect: '/system/log/auditLog',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          showAlways: true, // 表示一定要展示子菜单\r\n          title: '日志管理',\r\n        },\r\n        children: [\r\n          {\r\n            name: 'auditLog',\r\n            path: '/system/log/auditLog',\r\n            component: () => import('@/bysc_system/views/log/auditLog'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/log/auditLog',\r\n              title: '审计日志',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'loginLog',\r\n            path: '/system/log/loginLog',\r\n            component: () => import('@/bysc_system/views/log/loginLog'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/log/loginLog',\r\n              title: '登录审计',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n          {\r\n            name: 'changePwdLog',\r\n            path: '/system/log/changePwdLog',\r\n            component: () => import('@/bysc_system/views/log/changePwdLog'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/log/changePwdLog',\r\n              title: '修改密码日志',\r\n              icon: 'logo-apple'\r\n            },\r\n          },\r\n        ]\r\n      },\r\n      // 看板管理\r\n      {\r\n        name: 'boardData',\r\n        path: '/system/boardData',\r\n        redirect: '/system/boardData/capital',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/boardData',\r\n          title: '看板数据管理',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'capitalAirport',\r\n            path: '/system/boardData/capital',\r\n            redirect: '/system/boardData/capital/forecast',\r\n            component: () => import('@/components/main/components/empty-router-view/empty'),\r\n            meta: {\r\n              showInMenu: true,\r\n              title: '首都机场',\r\n              icon: 'logo-apple'\r\n            },\r\n            children: [\r\n              {\r\n                name: 'forecastManage',\r\n                path: '/system/boardData/capital/forecast',\r\n                component: () => import('@/bysc_system/views/boardData/capital/forecast'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '值班领导管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'yesterdayFlights',\r\n                path: '/system/boardData/capital/yesterdayFlights',\r\n                component: () => import('@/bysc_system/views/boardData/capital/yesterdayFlights'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '昨日行李量管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'flightThreeTable',\r\n                path: '/system/boardData/capital/flightThree',\r\n                component: () => import('@/bysc_system/views/boardData/capital/flightThree'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '首都行李数据管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'yesterdayFlightData',\r\n                path: '/system/boardData/capital/yesterdayFlightData',\r\n                component: () => import('@/bysc_system/views/boardData/capital/yesterdayFlightData'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  // title: '昨日航班/航班/保障航班架次数据管理',\r\n                  title: '航班及旅客管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'yesterdayRouteDistance',\r\n                path: '/system/boardData/capital/yesterdayRouteDistance',\r\n                component: () => import('@/bysc_system/views/boardData/capital/yesterdayRouteDistance'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '昨日行驶里程管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'operationPassengerVolume',\r\n                path: '/system/boardData/capital/operationPassengerVolume',\r\n                component: () => import('@/bysc_system/views/boardData/capital/operationPassengerVolume'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '首都捷运数据管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'capitalAPU',\r\n                path: '/system/boardData/capital/APU',\r\n                component: () => import('@/bysc_system/views/boardData/capital/APU'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '首都客桥数据管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              }\r\n            ]\r\n          },\r\n\r\n\r\n          // 大兴机场\r\n          {\r\n            name: 'daxingAirport',\r\n            path: '/system/boardData/daxing',\r\n            redirect: '/system/boardData/daxing/forecast',\r\n            component: () => import('@/components/main/components/empty-router-view/empty'),\r\n            meta: {\r\n              showInMenu: true,\r\n              title: '大兴机场',\r\n              icon: 'logo-apple'\r\n            },\r\n            children: [\r\n              {\r\n                name: 'daxingForecastManage',\r\n                path: '/system/boardData/daxing/forecast',\r\n                component: () => import('@/bysc_system/views/boardData/daxing/forecast'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '值班领导管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'daxingYesterdayFlights',\r\n                path: '/system/boardData/daxing/yesterdayFlights',\r\n                component: () => import('@/bysc_system/views/boardData/daxing/yesterdayFlights'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '昨日行李量管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'daxingFlightThreeTable',\r\n                path: '/system/boardData/daxing/flightThree',\r\n                component: () => import('@/bysc_system/views/boardData/daxing/flightThree'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '大兴行李数据管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'daxingYesterdayFlightData',\r\n                path: '/system/boardData/daxing/yesterdayFlightData',\r\n                component: () => import('@/bysc_system/views/boardData/daxing/yesterdayFlightData'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  // title: '昨日航班/航班/保障航班架次数据管理',\r\n                  title: '航班及旅客管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n\r\n              {\r\n                name: 'daxingAPU',\r\n                path: '/system/boardData/daxing/APU',\r\n                component: () => import('@/bysc_system/views/boardData/daxing/APU'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  title: '大兴客桥数据管理',\r\n                  icon: 'logo-apple'\r\n                }\r\n              }\r\n\r\n            ]\r\n          }\r\n\r\n\r\n        ]\r\n      },\r\n      // 新增技术部看板\r\n      {\r\n        name: 'techDashboard',\r\n        path: '/system/techDashboard',\r\n        redirect: '/system/techDashboard/techData',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          path: '/system/techDashboard',\r\n          title: '技术部看板',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'techData',\r\n            path: '/system/techDashboard/techData',\r\n            component: () => import('@/bysc_system/views/techDashboard/techData'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/techDashboard/techData',\r\n              title: '技术管理数据',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'safetyManagement',\r\n            path: '/system/techDashboard/safety',\r\n            redirect: '/system/techDashboard/safety/capital',\r\n            component: () => import('@/components/main/components/empty-router-view/empty'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/techDashboard/safety',\r\n              title: '安全管理数据',\r\n              icon: 'logo-apple'\r\n            },\r\n            children: [\r\n              {\r\n                name: 'capitalSafetyData',\r\n                path: '/system/techDashboard/safety/capital',\r\n                component: () => import('@/bysc_system/views/techDashboard/safety/capital'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  path: '/system/techDashboard/safety/capital',\r\n                  title: '首都安全管理数据',\r\n                  icon: 'logo-apple'\r\n                }\r\n              },\r\n              {\r\n                name: 'daxingSafetyData',\r\n                path: '/system/techDashboard/safety/daxing',\r\n                component: () => import('@/bysc_system/views/techDashboard/safety/daxing'),\r\n                meta: {\r\n                  showInMenu: true,\r\n                  path: '/system/techDashboard/safety/daxing',\r\n                  title: '大兴安全管理数据',\r\n                  icon: 'logo-apple'\r\n                }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      // 新增人力看板菜单\r\n      {\r\n        name: 'hrDashboard',\r\n        path: '/system/hrDashboard',\r\n        redirect: '/system/hrDashboard/overview',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          showAlways: true,\r\n          title: '人力看板',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'hrOverview',\r\n            path: '/system/hrDashboard/overview',\r\n            component: () => import('@/bysc_system/views/hrDashboard/overview'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/hrDashboard/overview',\r\n              title: '人力概览',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'enterData',\r\n            path: '/system/hrDashboard/enterData',\r\n            component: () => import('@/bysc_system/views/hrDashboard/enterData'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/hrDashboard/enterData',\r\n              title: '数据录入',\r\n              icon: 'logo-apple'\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      // 可视化运维管理\r\n\r\n      {\r\n        name: 'visualOpsManagement',\r\n        path: '/system/visualOpsManagement',\r\n        redirect: '/system/visualOpsManagement/drawingManagement',\r\n        component: () => import('@/components/main/components/empty-router-view/empty'),\r\n        meta: {\r\n          showInMenu: true,\r\n          showAlways: true,\r\n          title: '可视化运维管理',\r\n          icon: 'logo-apple'\r\n        },\r\n        children: [\r\n          {\r\n            name: 'drawingManagement',\r\n            path: '/system/visualOpsManagement/drawingManagement',\r\n            component: () => import('@/bysc_system/views/visualOpsManagement/drawingManagement'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/visualOpsManagement/drawingManagement',\r\n              title: '图纸管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'areaManagement',\r\n            path: '/system/visualOpsManagement/areaManagement',\r\n            component: () => import('@/bysc_system/views/visualOpsManagement/areaManagement'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/visualOpsManagement/areaManagement',\r\n              title: '区域绘制',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'areaBinding',\r\n            path: '/system/visualOpsManagement/areaBinding',\r\n            component: () => import('@/bysc_system/views/visualOpsManagement/areaBinding'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/visualOpsManagement/areaBinding',\r\n              title: '区域管理',\r\n              icon: 'logo-apple'\r\n            }\r\n          },\r\n          {\r\n            name: 'deviceBinding',\r\n            path: '/system/visualOpsManagement/deviceBinding',\r\n            component: () => import('@/bysc_system/views/visualOpsManagement/deviceBinding'),\r\n            meta: {\r\n              showInMenu: true,\r\n              path: '/system/visualOpsManagement/deviceBinding',\r\n              title: '区域绑定设备',\r\n              icon: 'logo-apple'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n\r\n    ]\r\n  }\r\n];\r\n\r\nexport default systemRoutes;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,wBAAwB;AACzC;;AAEA,IAAMC,YAAY,GAAG,CACnB;EACEC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ,IAAI;EACfK,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE;IACJC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAAE;IAClBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,0BAA0B,CAAC;IAAA;IACnDE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,IAAI;MACXE,IAAI,EAAE;IACR;EACF,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,aAAa;IACnBE,QAAQ,EAAE,oBAAoB;IAC9BD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,aAAa;MACnBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,0BAA0B,CAAC;MAAA;MACnDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,0BAA0B,CAAC;MAAA;MACnDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,2BAA2B,CAAC;MAAA;MACpDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,wBAAwB;QAC9BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,kCAAkC,CAAC;MAAA;MAC3DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,4BAA4B;QAClCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBE,QAAQ,EAAE,oBAAoB;IAC9BD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,6BAA6B,CAAC;MAAA;MACtDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACET,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,oBAAoB;MAC1BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,4BAA4B,CAAC;MAAA;MACrDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,oBAAoB;QAC1BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,sBAAsB;MAC5BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,8BAA8B,CAAC;MAAA;MACvDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,sBAAsB;QAC5BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,6BAA6B;MACnCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,8CAA8C,CAAC;MAAA;MACvEE,IAAI,EAAE;QACJC,UAAU,EAAE,KAAK;QACjBJ,IAAI,EAAE,6BAA6B;QACnCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,qBAAqB;MAC3BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,4BAA4B,CAAC;MAAA;MACrDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,qBAAqB;QAC3BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBE,QAAQ,EAAE,yBAAyB;IACnCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,cAAc;MACpBM,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,oCAAoC,CAAC;MAAA;MAC7DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,yBAAyB;QAC/BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,oBAAoB;IAC1BE,QAAQ,EAAE,8BAA8B;IACxCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,oBAAoB;MAC1BM,KAAK,EAAE,OAAO;MACdE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,8BAA8B;MACpCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,gCAAgC,CAAC;MAAA;MACzDE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,OAAO;QACdE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,eAAe;IACrBE,QAAQ,EAAE,+BAA+B;IACzCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAAE;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,+BAA+B;MACrCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;MAAA;MACpEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,+BAA+B;QACrCM,KAAK,EAAE,KAAK;QACZE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,mCAAmC;MACzCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,+CAA+C,CAAC;MAAA;MACxEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,mCAAmC;QACzCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,kCAAkC;MACxCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,8CAA8C,CAAC;MAAA;MACvEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,kCAAkC;QACxCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,6BAA6B;MACnCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,yCAAyC,CAAC;MAAA;MAClEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,6BAA6B;QACnCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,uCAAuC;MAC7CC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,mDAAmD,CAAC;MAAA;MAC5EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,uCAAuC;QAC7CM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,sCAAsC;MAC5CC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;MAAA;MAC3EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,sCAAsC;QAC5CM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,4CAA4C;MAClDC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,wDAAwD,CAAC;MAAA;MACjFE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,4CAA4C;QAClDM,KAAK,EAAE,UAAU;QACjBE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,8BAA8B;MACpCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;MAAA;MACnEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;MAAA;MAChFE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,2CAA2C;QACjDM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,mCAAmC;MACzCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,+CAA+C,CAAC;MAAA;MACxEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,mCAAmC;QACzCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,8BAA8B;MACpCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;MAAA;MACnEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC,EACD;IACET,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,MAAM;IACZE,QAAQ,EAAE,sBAAsB;IAChCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAAE;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,sBAAsB;MAC5BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,kCAAkC,CAAC;MAAA;MAC3DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,sBAAsB;QAC5BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,sBAAsB;MAC5BC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,kCAAkC,CAAC;MAAA;MAC3DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,sBAAsB;QAC5BM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,0BAA0B;MAChCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,sCAAsC,CAAC;MAAA;MAC/DE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,0BAA0B;QAChCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC;EACD;EACA;IACET,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,mBAAmB;IACzBE,QAAQ,EAAE,2BAA2B;IACrCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,mBAAmB;MACzBM,KAAK,EAAE,QAAQ;MACfE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,2BAA2B;MACjCE,QAAQ,EAAE,oCAAoC;MAC9CD,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;MAAA;MAC/EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR,CAAC;MACDD,QAAQ,EAAE,CACR;QACER,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,oCAAoC;QAC1CC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;QAAA;QACzEE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,QAAQ;UACfE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,kBAAkB;QACxBC,IAAI,EAAE,4CAA4C;QAClDC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,wDAAwD,CAAC;QAAA;QACjFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,SAAS;UAChBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,kBAAkB;QACxBC,IAAI,EAAE,uCAAuC;QAC7CC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,mDAAmD,CAAC;QAAA;QAC5EE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,qBAAqB;QAC3BC,IAAI,EAAE,+CAA+C;QACrDC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,2DAA2D,CAAC;QAAA;QACpFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChB;UACAE,KAAK,EAAE,SAAS;UAChBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,wBAAwB;QAC9BC,IAAI,EAAE,kDAAkD;QACxDC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,8DAA8D,CAAC;QAAA;QACvFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,0BAA0B;QAChCC,IAAI,EAAE,oDAAoD;QAC1DC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,gEAAgE,CAAC;QAAA;QACzFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,+BAA+B;QACrCC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;QAAA;QACpEE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC;IAEL,CAAC;IAGD;IACA;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,0BAA0B;MAChCE,QAAQ,EAAE,mCAAmC;MAC7CD,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;MAAA;MAC/EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR,CAAC;MACDD,QAAQ,EAAE,CACR;QACER,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,mCAAmC;QACzCC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,+CAA+C,CAAC;QAAA;QACxEE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,QAAQ;UACfE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,wBAAwB;QAC9BC,IAAI,EAAE,2CAA2C;QACjDC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;QAAA;QAChFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,SAAS;UAChBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,wBAAwB;QAC9BC,IAAI,EAAE,sCAAsC;QAC5CC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;QAAA;QAC3EE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,2BAA2B;QACjCC,IAAI,EAAE,8CAA8C;QACpDC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,0DAA0D,CAAC;QAAA;QACnFE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChB;UACAE,KAAK,EAAE,SAAS;UAChBE,IAAI,EAAE;QACR;MACF,CAAC,EAED;QACET,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,8BAA8B;QACpCC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;QAAA;QACnEE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC;IAGL,CAAC;EAIL,CAAC;EACD;EACA;IACET,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,uBAAuB;IAC7BE,QAAQ,EAAE,gCAAgC;IAC1CD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBJ,IAAI,EAAE,uBAAuB;MAC7BM,KAAK,EAAE,OAAO;MACdE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,gCAAgC;MACtCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;MAAA;MACrEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,gCAAgC;QACtCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,8BAA8B;MACpCE,QAAQ,EAAE,sCAAsC;MAChDD,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;MAAA;MAC/EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR,CAAC;MACDD,QAAQ,EAAE,CACR;QACER,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE,sCAAsC;QAC5CC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;QAAA;QAC3EE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBJ,IAAI,EAAE,sCAAsC;UAC5CM,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC,EACD;QACET,IAAI,EAAE,kBAAkB;QACxBC,IAAI,EAAE,qCAAqC;QAC3CC,SAAS,EAAE,SAAAA,UAAA;UAAA,OAAM,MAAM,CAAC,iDAAiD,CAAC;QAAA;QAC1EE,IAAI,EAAE;UACJC,UAAU,EAAE,IAAI;UAChBJ,IAAI,EAAE,qCAAqC;UAC3CM,KAAK,EAAE,UAAU;UACjBE,IAAI,EAAE;QACR;MACF,CAAC;IAEL,CAAC;EAEL,CAAC;EACD;EACA;IACET,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3BE,QAAQ,EAAE,8BAA8B;IACxCD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,MAAM;MACbE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,8BAA8B;MACpCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;MAAA;MACnEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,8BAA8B;QACpCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,+BAA+B;MACrCC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;MAAA;MACpEE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,+BAA+B;QACrCM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC;EACD;;EAEA;IACET,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,6BAA6B;IACnCE,QAAQ,EAAE,+CAA+C;IACzDD,SAAS,EAAE,SAAAA,UAAA;MAAA,OAAM,MAAM,CAAC,sDAAsD,CAAC;IAAA;IAC/EE,IAAI,EAAE;MACJC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,SAAS;MAChBE,IAAI,EAAE;IACR,CAAC;IACDD,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,+CAA+C;MACrDC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,2DAA2D,CAAC;MAAA;MACpFE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,+CAA+C;QACrDM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,4CAA4C;MAClDC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,wDAAwD,CAAC;MAAA;MACjFE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,4CAA4C;QAClDM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,yCAAyC;MAC/CC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,qDAAqD,CAAC;MAAA;MAC9EE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,yCAAyC;QAC/CM,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE;MACR;IACF,CAAC,EACD;MACET,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE,SAAAA,UAAA;QAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;MAAA;MAChFE,IAAI,EAAE;QACJC,UAAU,EAAE,IAAI;QAChBJ,IAAI,EAAE,2CAA2C;QACjDM,KAAK,EAAE,QAAQ;QACfE,IAAI,EAAE;MACR;IACF,CAAC;EAEL,CAAC;AAGL,CAAC,CACF;AAED,eAAeV,YAAY"}]}