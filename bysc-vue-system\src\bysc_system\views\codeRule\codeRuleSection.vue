<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="codeRule/baseCodeRuleSection-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          :auto-load="false"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="组成编码">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.sectionCode"
                  size="small"
                  placeholder="请输入组成编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="组成名称">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.sectionName"
                  size="small"
                  placeholder="请输入组成名称"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'codeRuleSection_add'" size="small" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" ref="table" stripe style="width: 100%">
            <el-table-column fixed="left" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot == 'sectionType'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <div v-for="(cell,i) in sectionTypeList" :key="i">
                    <div v-if="cell.value == scope.row.sectionType">{{cell.label}}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="170"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'codeRuleSection_edit'"
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  v-permission="'codeRuleSection_del'"
                  @click="handleDelete(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px 40px 10px;">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="100px"
          class="demo-form"
        >
          <el-form-item label="分段编码" prop="sectionCode">
            <el-input size="small" v-model.trim="form.sectionCode" placeholder="请输入分段编码" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="分段名称" prop="sectionName">
            <el-input size="small" v-model.trim="form.sectionName" placeholder="请输入分段名称" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="分段序号" prop="sectionSort">
            <el-input-number size="small" v-model.trim="form.sectionSort" placeholder="请输入分段序号" maxlength="32" :controls="false" style="width:100%;"></el-input-number>
          </el-form-item>
          <el-form-item label="分段类型" prop="sectionType">
            <el-select
              v-model="form.sectionType"
              placeholder="请选择分段类型"
              clearable
              style="width: 100%; margin: 0 10px 0 0"
              size="small"
            >
              <el-option
                v-for="(item, index) in sectionTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="固定字符" prop="fixeChar" v-if="form.sectionType=='1'">
            <el-input size="small" v-model.trim="form.fixeChar" placeholder="请输入固定字符" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="日期时间格式" prop="timestampFormat" v-if="form.sectionType=='2'">
            <el-select
              v-model="form.timestampFormat"
              placeholder="请选择分段类型"
              clearable
              style="width: 100%; margin: 0 10px 0 0"
              size="small"
            >
              <el-option
                v-for="(item, index) in timestampFormatList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="分段长度" prop="sectionLength" v-if="form.sectionType=='3'">
            <el-input-number size="small" v-model.trim="form.sectionLength" placeholder="请输入分段长度" maxlength="32" :controls="false" style="width:100%;"></el-input-number>
          </el-form-item>
          <el-form-item label="起始流水号" prop="serialNum" v-if="form.sectionType=='3'">
            <el-input-number size="small" v-model.trim="form.serialNum" placeholder="请输入起始流水号" maxlength="32" :controls="false" style="width:100%;"></el-input-number>
          </el-form-item>
          <el-form-item label="流水号步长" prop="serialNumStep" v-if="form.sectionType=='3'">
            <el-input-number size="small" v-model.trim="form.serialNumStep" placeholder="请输入流水号步长" maxlength="32" :controls="false" style="width:100%;"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              size="small"
              placeholder="请输入备注"
              maxlength="200"
              type="textarea"
              v-model.trim="form.remark"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            @click="submitForm"
            :loading="okLoading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultForm = {};
export default {
  components: {
    Grid
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      timestampFormatList: [
        {
          label: '年-月-日时:分:秒',
          value: '1'
        },
        {
          label: '年/月/日时:分:秒',
          value: '2'
        },
        {
          label: '年月日时分秒',
          value: '3'
        },
        {
          label: '年-月-日',
          value: '4'
        },
        {
          label: '年/月/日',
          value: '5'
        }
      ],
      sectionTypeList: [
        {
          label: '固定字符',
          value: '1'
        },
        {
          label: '当前日期时间',
          value: '2'
        },
        {
          label: '流水号',
          value: '3'
        }
      ],
      form: _.cloneDeep(defaultForm),
      okLoading: false,
      rules: {
        sectionCode: [
          {required: true, message: '请输入分段编码', trigger: 'blur'},
          // {
          //   min: 1,
          //   max: 15,
          //   message: '长度在 1 到 15 个字符',
          //   trigger: 'blur',
          // },
        ],
        sectionName: [
          {required: true, message: '请输入分段名称', trigger: 'blur'},
        ],
        sectionSort: [
          {required: true, message: '请输入分段序号', trigger: 'blur'},
        ],
        sectionType: [
          {required: true, message: '请选择分段类型', trigger: 'blur'},
        ],
      },
      detailDrawer: false,
      drawerTitle: '新增编码规则组成',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '组成编码',
          key: 'sectionCode',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '组成名称',
          key: 'sectionName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '分段序号',
          key: 'sectionSort',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '分段类型',
          slot: 'sectionType',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '备注',
          key: 'remark',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    if (this.$route.query.id) {
      sessionStorage.setItem('ruleId', this.$route.query.id);
    }
    this.searchForm = _.cloneDeep(defaultSearchForm);
    this.searchForm.ruleId = sessionStorage.getItem('ruleId');
    this.$nextTick(() => {
      this.$refs.grid.query();
    });
  },
  methods: {
    handleAdd() {
      this.form = _.cloneDeep(defaultForm);
      this.drawerTitle = '新增编码规则组成';
      this.drawer = true;
    },
    handleEdit(row) {
      this.$api['codeRule/baseCodeRuleSection-get']({id: row.id}).then(data => {
        this.drawerTitle = '编辑编码规则组成';
        this.drawer = true;
        this.form = data;
      });
    },
    submitForm() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.form.ruleId = sessionStorage.getItem('ruleId');
          if (this.form.sectionType == '1') {
            this.form.timestampFormat = null;
            this.form.sectionLength = null;
            this.form.serialNum = null;
            this.form.serialNumStep = null;
          } else if (this.form.sectionType == '2') {
            this.form.fixeChar = null;
            this.form.sectionLength = null;
            this.form.serialNum = null;
            this.form.serialNumStep = null;
          } else if (this.form.sectionType == '3') {
            this.form.fixeChar = null;
            this.form.timestampFormat = null;
          }
          this.$api['codeRule/baseCodeRuleSection-save'](this.form).then(data => {
            this.drawer = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.form = _.cloneDeep(defaultForm);
      this.$refs.form.resetFields();
      this.drawer = false;
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['codeRule/baseCodeRuleSection-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.searchForm.ruleId = sessionStorage.getItem('ruleId');
      this.valueDate = '';
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
    getTimes(date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? ('0' + m) : m;
      var d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      var h = date.getHours();
      h = h < 10 ? ('0' + h) : h;
      var min = date.getMinutes();
      min = min < 10 ? ('0' + min) : min;
      var s = date.getSeconds();
      s = s < 10 ? ('0' + s) : s;
      return y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + s;
    },
    getDate(e) {
      this.searchForm.contractStartTime = this.getTimes(e[0]).substring(0, 10);
      this.searchForm.contractEndTime = this.getTimes(e[1]).substring(0, 10);
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
