<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :width="width"
    @close="handleClose"
  >
    <div>
      <el-form
        :model="form"
        :rules="formRules"
        ref="bindPositionForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="资产位置" prop="deviceLocationId">
              <ltree
                v-if="dialogVisible"
                ref="searchLtree"
                :cnvalue="form.deviceLocationName"
                :multiple="multiple"
                :isLazy="isLazy"
                :apiUrl="apiUrl"
                :bound-location-ids="boundLocationIds"
                @getSelectCnData="getSelectCnData"
                @getSelectData="getSelectData"
                :value="deviceLocationIds"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleCancel">
        {{ cancelText }}
      </el-button>
      <el-button
        size="small"
        type="primary"
        :loading="confirmLoading"
        @click="handleConfirm"
      >
        {{ confirmText }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import ltree from "@/components/treeComp/localtionTree.vue";
import _ from 'lodash';

export default {
  name: 'BindPositionDialog',
  components: {
    ltree
  },
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '绑定资产位置'
    },
    // 弹窗宽度
    width: {
      type: String,
      default: '40%'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否懒加载
    isLazy: {
      type: Boolean,
      default: false
    },
    // API接口地址
    apiUrl: {
      type: String,
      default: 'firstLineDept/get-loc-tree-true'
    },
    // 保存API接口地址
    saveApiUrl: {
      type: String,
      default: 'visualOpsManagement/assetLocation-save'
    },
    // 获取详情API接口地址
    getDetailApiUrl: {
      type: String,
      default: 'firstLineDept/get-dts'
    },
    // 当前行数据
    rowData: {
      type: Object,
      default: () => ({})
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取 消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确 定'
    },
    // 自定义表单验证规则
    customRules: {
      type: Object,
      default: () => ({})
    },
    // 区域ID
    reginId: {
      type: String,
      default: ''
    },
    // 已绑定的位置ID数组
    boundLocationIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const validatePosition = (rule, value, callback) => {
      // 检查表单中的实际值，而不是传入的value参数
      const actualValue = this.form.deviceLocationId;
      console.log('validatePosition called - actualValue:', actualValue, 'type:', typeof actualValue);
      console.log('validatePosition called - value param:', value, 'type:', typeof value);

      if (!actualValue || actualValue === '' || (Array.isArray(actualValue) && actualValue.length === 0)) {
        console.log('Validation failed: no value selected');
        callback(new Error('请选择绑定资产位置'));
      } else {
        console.log('Validation passed');
        callback();
      }
    };

    return {
      dialogVisible: false,
      confirmLoading: false,
      form: {
        deviceLocationId: '',
        deviceLocationName: ''
      },
      deviceLocationIds: [],
      defaultFormRules: {
        deviceLocationId: [
          {required: true, validator: validatePosition, trigger: 'blur,change'},
        ],
      }
    };
  },
  computed: {
    formRules() {
      return Object.assign({}, this.defaultFormRules, this.customRules);
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val && this.rowData && this.rowData.id) {
          this.loadData();
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 加载数据
    loadData() {
      if (!this.rowData || !this.rowData.id) {
        return;
      }

      this.$api[this.getDetailApiUrl]({
        id: this.rowData.id
      }).then(res => {
        this.form = _.cloneDeep(res);
        if (this.form.deviceLocationId) {
          this.deviceLocationIds = this.form.deviceLocationId.split(',');
          this.form.deviceLocationName = this.form.deviceLocationName;
        } else {
          this.deviceLocationIds = [];
        }
      }).catch(error => {
        this.$message.error('加载数据失败');
        console.error('Load data error:', error);
      });
    },

    // 获取选择的中文名称
    getSelectCnData(cnName, data) {
      this.form.deviceLocationName = cnName;
    },

    // 获取选择的数据
    getSelectData(ids, node) {
      console.log('getSelectData called with ids:', ids, 'type:', typeof ids);
      this.form.deviceLocationId = ids;
      this.deviceLocationIds = Array.isArray(ids) ? ids : [ids];
      console.log('Updated form.deviceLocationId:', this.form.deviceLocationId);
      console.log('Updated deviceLocationIds:', this.deviceLocationIds);

      // 数据更新后，手动触发验证
      this.$nextTick(() => {
        if (this.$refs.bindPositionForm) {
          this.$refs.bindPositionForm.validateField('deviceLocationId');
        }
      });
    },

    // 处理取消
    handleCancel() {
      this.dialogVisible = false;
      this.$emit('cancel');
    },

    // 处理关闭
    handleClose() {
      this.resetForm();
      this.$emit('close');
    },

    // 处理确认
    handleConfirm() {
      // 确保数据格式正确
      if (this.deviceLocationIds && this.deviceLocationIds.length > 0) {
        this.form.deviceLocationId = Array.isArray(this.deviceLocationIds)
          ? this.deviceLocationIds.join(',')
          : this.deviceLocationIds;
      } else {
        this.form.deviceLocationId = [];
      }

      // 使用 nextTick 确保数据更新后再验证
      this.$nextTick(() => {
        this.$refs.bindPositionForm.validate(valid => {
          if (valid) {
            this.confirmLoading = true;
            // 构建保存参数，包含 reginId
            const saveParams = {
              locationIds: this.form.deviceLocationId.split(",")
            };
            // 如果有 reginId，添加到参数中
            if (this.reginId) {
              saveParams.reginId = this.reginId;
            }

            this.$api[this.saveApiUrl](saveParams).then(data => {
              this.dialogVisible = false;
              this.confirmLoading = false;
              this.resetForm();
              this.$message({
                type: 'success',
                message: '操作成功',
              });
              this.$emit('success', data);
            }).catch(error => {
              this.confirmLoading = false;
              this.$emit('error', error);
            });
          } else {
            console.log('表单验证失败');
            return false;
          }
        });
      });
    },

    // 重置表单
    resetForm() {
      this.form = {
        deviceLocationId: '',
        deviceLocationName: ''
      };
      this.deviceLocationIds = [];
      this.$refs.bindPositionForm && this.$refs.bindPositionForm.resetFields();
    }
  }
};
</script>

<style scoped>
.demo-ruleForm {
  padding: 0 20px;
}
</style>
