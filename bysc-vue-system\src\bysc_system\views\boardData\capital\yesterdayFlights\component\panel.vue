<template>
  <el-drawer
    :title="'编辑'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <el-form :model="editForm" ref="editForm" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="自动生成前部日行李量" disabled />
        </el-form-item>

        <el-form-item label="行李量(件)" prop="luggageQuantity">
          <el-input-number
            v-model="editForm.luggageQuantity"
            :min="0"
             style="width: 100%"
            placeholder="请输入行李量"
            controls-position="right">

            <template slot="append">件</template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="日期" prop="luggageDate">
          <el-date-picker
            v-model="editForm.luggageDate"
            type="date"
            placeholder="自动生成前一天"
            disabled
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EditPanel',
  data() {
    return {
      visible: false,
      editForm: {
        name: '',
        luggageQuantity: '',
        luggageDate: null
      },
      rules: {
        luggageQuantity: [
          {required: true, message: '请输入行李量', trigger: 'blur'},
          {type: 'number', message: '行李量必须为数字'}
        ]
      }
    };
  },
  methods: {
    open(data) {
      this.visible = true;
      this.editForm = _.cloneDeep(data);
    },
    handleCancel() {
      this.visible = false;
      this.$refs.editForm.resetFields();
    },
    handleSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          // TODO: 调用保存接口
          this.$api['boardData/dashboardYesterdayLuggage-save'](this.editForm).then(() => {
            this.$message.success('保存成功');
            this.$emit('submit', this.editForm);
            this.visible = false;
          });

        }
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}
</style>
