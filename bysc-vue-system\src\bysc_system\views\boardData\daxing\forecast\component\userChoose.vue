<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :size="'70%'"
    :destroy-on-close="true"
    :modal-append-to-body="false"
    :wrapper-closable="false"
    :modal="false"
  >
    <div class="drawer-container">
      <Grid
        api="systems/userPage"
        :event-bus="searchEventBus"
        :search-params="searchForm"
        :newcolumn="columns"
        :show-selection="false"
        @datas="getDatas"
        @columnChange="getcolumn"
        ref="grid"
      >
        <!-- Search Form -->
        <div slot="search">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="账户名">
              <el-input
                v-model.trim="searchForm.account"
                size="small"
                placeholder="请输入账户名"
              ></el-input>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input
                v-model.trim="searchForm.realName"
                size="small"
                placeholder="请输入姓名"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="handleSearch">搜索</el-button>
              <el-button size="small" @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- Table -->
        <el-table
          slot="table"
          slot-scope="{ loading }"
          v-loading="loading"
          :data="tableData"
          ref="table"
          :row-key="getRowKeys"
          @selection-change="changeSelect"
          @row-click="handleRowClick"
          highlight-current-row
          stripe
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            :selectable="isSelectable"
            width="55"
          >
          </el-table-column>
          <!-- ... other table columns remain the same ... -->
          <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot=='gender'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">{{
                  scope.row[item.slot] == "1" ? "男" : "女"
                }}</template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot=='status'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row[item.slot] == '0'">禁用</span>
                  <span v-if="scope.row[item.slot] == '1'">正常</span>
                  <span v-if="scope.row[item.slot] == '99'">已锁定</span>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
        </el-table>
      </Grid>

      <!-- Drawer Footer -->
      <div class="drawer-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="confirmSelection">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import Vue from "vue";
import Grid from '@/components/Grid';

export default {
  name: 'UserChoose',
  components: {
    Grid
  },
  props: {
    // 预选的用户数据
    selectedUser: {
      type: Object,
      default: () => ({})
    }
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      visible: false,
      title: '选择用户',
      searchForm: {
        account: '',
        realName: '',
        status: ''
      },
      tableData: [],
      selectedRow: null,
      columns: [
        {
          title: "账户名",
          key: "account",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "姓名",
          key: "realName",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "员工状态",
          key: "state",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "企业标识",
          key: "corporateIdentity",
          tooltip: true,
          minWidth: 130,
        },
        {
          title: "手机号",
          key: "mobile",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '所属部门',
          key: 'preOrganizationName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "现所在部门",
          key: "organizationName",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '所属模块',
          key: 'moduleNames',
          tooltip: true,
          minWidth: 150,
        },

        {
          title: '现所在模块',
          key: 'nowModuleNames',
          tooltip: true,
          minWidth: 150,
        },

        {
          title: "状态",
          slot: "status",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "邮箱",
          key: "email",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "性别",
          slot: "gender",
          tooltip: true,
          minWidth: 170,
        },
        {
          title: "最后登录时间",
          key: "lastLoginTime",
          tooltip: true,
          minWidth: 170,
        },
      ],
      // ... other data properties remain the same ...
    };
  },
  methods: {
    changeSelect(selection) {
      if (selection.length > 1) {
        const lastSelected = selection[selection.length - 1];
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(lastSelected, true);
        this.selectedRow = lastSelected;
      } else {
        this.selectedRow = selection[0] || null;
      }
    },
    isSelectable(row) {
      // 移除选择限制，所有行都可以选择
      return true;
    },
    getRowKeys(row) {
      return row.id;
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      let ids = e.map(item => item.id);
      let p1 = this.$api["systems/find-user-module"]({ids});
      let p2 = this.$api["systems/find-user-now-module"]({ids});
      let p3 = this.$api["systems/find-user-dept"]({ids});
      let p4 = this.$api["systems/find-zhi-yuan-person"]({ids});
      Promise.all([p1, p2, p3, p4]).then(res => {
        this.tableData = e.map(item => {
          // console.log('res[3][item.state] ', res[3][item]);
          let _e = res[3].filter(initem => {
            if (initem.sysUserId == item.id) {
              return initem.state;
            }
          });
          let state = _e[0] ? _e[0].state : '';
          let corporateIdentity = _e[0] ? _e[0].corporateIdentity : '';
          return {
            ...item,
            moduleNames: res[0][item.id] || '',
            nowModuleNames: res[1][item.id] || '',
            preOrganizationName: res[2][item.id] || '',
            state,
            corporateIdentity
          };
        });
      });

    },
    // 打开抽屉
    open() {
      this.visible = true;
      if (this.selectedUser.id) {
        this.selectedRow = this.selectedUser;
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow(this.selectedRow);
        });
      }
    },
    // 行点击事件
    handleRowClick(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 确认选择
    confirmSelection() {
      if (!this.selectedRow) {
        this.$message.warning('请选择一个用户');
        return;
      }
      this.$emit('select', this.selectedRow);
      this.visible = false;
    },

    // 处理搜索
    handleSearch() {
      // 触发 Grid 组件的搜索
      this.$nextTick(() => {
        this.searchEventBus.$emit('search');
      });
    },

    // 处理重置
    handleReset() {
      // 重置搜索表单
      this.searchForm = {
        account: '',
        realName: '',
        status: ''
      };
      // 重置后自动触发搜索
      this.handleSearch();
    },
    // ... other existing methods remain the same ...
  }
};
</script>

<style lang="scss" scoped>
.drawer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 20px;  // 添加整体内边距

  // 抽屉头部样式
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    span {
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 搜索区域样式
  :deep(.demo-form-inline) {
    padding: 20px 0 4px;  // 上下间距调整

    .el-form-item {
      margin-bottom: 16px;
      margin-right: 32px;

      // 表单标签样式
      .el-form-item__label {
        padding-right: 12px;
      }

      // 输入框样式
      .el-input {
        width: 220px;  // 统一输入框宽度
      }
    }
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    text-align: right;
    background: #fff;
    border-top: 1px solid #e8e8e8;
  }

  // 添加表格选中行的样式
  :deep(.el-table) {
    .el-table__row.current-row > td {
      background-color: #f5f7fa !important;
    }

    .el-table__row:hover > td {
      background-color: #f5f7fa !important;
    }
  }
}

:deep(.el-drawer__wrapper) {
  position: absolute !important;
  z-index: 3100 !important;
}

:deep(.el-drawer__container) {
  position: relative;
}

:deep(.v-modal) {
  position: absolute;
  z-index: 3099 !important;
}
</style>