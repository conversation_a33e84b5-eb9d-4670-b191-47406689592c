export default [
  // 金蝶
  {
    name: 'kingdeeSsoUser-page', // 查询金蝶授权用户分页
    method: 'POST',
    path: '/kingdee/kingdeeSsoUser/page'
  },

  {
    name: 'kingdeeSsoUser-save', // 保存金蝶授权用户
    method: 'POST',
    path: '/kingdee/kingdeeSsoUser/save'
  },
  {
    name: 'kingdeeSsoUser-get', // 查询金蝶授权用户详情
    method: 'OTHERGET',
    path: '/kingdee/kingdeeSsoUser/get'
  },
  {
    name: 'kingdeeSsoUser-delete', // 删除金蝶授权用户
    method: 'DELETE',
    path: '/kingdee/kingdeeSsoUser/delete'
  },
  {
    name: 'kingdeeSsoUserBind-not-bind-user-page', // 查询未绑定金蝶授权登录账号的用户分页
    method: 'POST',
    path: '/kingdee/kingdeeSsoUserBind/not-bind-user-page'
  },
  {
    name: 'kingdeeSsoUserBind-bind-user-page', // 查询已绑定金蝶授权登录账号的用户列表
    method: 'GET',
    path: '/kingdee/kingdeeSsoUserBind/bind-user-page'
  },
  {
    name: 'kingdeeSsoUserBind-bind', // 绑定金蝶授权登录用户
    method: 'POST',
    path: '/kingdee/kingdeeSsoUserBind/bind'
  },
  // 京东慧采授
  {
    name: 'jdUser-page', // 查询京东慧采授权用户分页
    method: 'POST',
    path: '/syn/jdhcSooUser/page'
  },
  {
    name: 'jdUser-save', // 保存京东慧采授授权用户
    method: 'POST',
    path: '/syn/jdhcSooUser/save'
  },
  {
    name: 'jdUserBind-bind-user-page', // 查询已绑定京东慧采授授权登录账号的用户列表
    method: 'GET',
    path: '/syn/jdhcSooUserBind/bind-user-page'
  },
  {
    name: 'jdUserBind-not-bind-user-page', // 查询未绑定京东慧采授授权登录账号的用户分页
    method: 'POST',
    path: '/syn/jdhcSooUserBind/not-bind-user-page'
  },
  {
    name: 'jdUserBind-bind', // 绑定京东慧采授权登录用户
    method: 'POST',
    path: '/syn/jdhcSooUserBind/bind'
  },
  {
    name: 'jdUser-get', // 查询京东慧采授权用户详情
    method: 'OTHERGET',
    path: '/syn/jdhcSooUser/get'
  },
  {
    name: 'jdUser-delete', // 删除京东慧采授用户
    method: 'DELETE',
    path: '/syn/jdhcSooUser/delete'
  },


  {
    name: 'materialSupplier-pageinfo', // 查询供应商绑定分页
    method: 'POST',
    path: '/material/materialSupplier/pageinfo'
  },
  {
    name: 'materialSupplier-get', // 查询供应商详情
    method: 'OTHERGET',
    path: '/material/materialSupplier/get'
  },
  {
    name: 'materialSupplier-suppliersave', // 添加供应商
    method: 'POST',
    path: '/material/materialSupplier/suppliersave'
  },
  {
    name: 'materialSupplier-delete', // 删除供应商
    method: 'DELETE',
    path: '/material/materialSupplier/delete'
  },
  {
    name: 'contractCounterPartner-list', // 查询合同相对方列表
    method: 'GET',
    path: '/contract/contractCounterPartner/list'
  },
  {
    name: 'materialSupplierTaijiRel-save', // 保存供应商太极关联表
    method: 'POST',
    path: '/material/materialSupplierTaijiRel/save'
  },
  {
    name: 'KingdeeSupplier-save', // 获取金蝶供应商并保存
    method: 'GET',
    path: '/kingdee/KingdeeSupplier/save'
  },

  {
    name: 'sysOrganzizationKingdeeRel-selectPage', // 组织机构分页
    method: 'POST',
    path: '/kingdee/sysOrganzizationKingdeeRel/selectPage'
  },
  {
    name: 'KingdeeDepartment-all', // 获取金蝶部门
    method: 'GET',
    path: '/kingdee/KingdeeDepartment/all'
  },
  {
    name: 'sysOrganzizationKingdeeRel-save', // 保存部门与金蝶关联表
    method: 'POST',
    path: '/kingdee/sysOrganzizationKingdeeRel/save'
  },

  {
    name: 'materialFinancialAccounts-page', // 查询财务科目分页
    method: 'POST',
    path: '/kingdee/materialFinancialAccounts/page'
  },
  {
    name: 'materialFinancialAccounts-save', // 保存财务科目
    method: 'POST',
    path: '/kingdee/materialFinancialAccounts/save'
  },
  {
    name: 'materialFinancialAccounts-get', // 查询财务科目详情
    method: 'OTHERGET',
    path: '/kingdee/materialFinancialAccounts/get'
  },
  {
    name: 'materialFinancialAccounts-delete', // 删除财务科目
    method: 'DELETE',
    path: '/kingdee/materialFinancialAccounts/delete'
  },
  {
    name: 'financeSalaryPushRecord-page', // 查询工资费用推送记录分页
    method: 'POST',
    path: '/syn/financeSalaryPushRecord/page'
  },
  {
    name: 'generate-sap-zip', // 生成SAP报表压缩包
    method: 'POST',
    path: '/syn/financeSalaryPushRecord/generate-sap-zip'
  },
  {
    name: 'get-sap-zip-name', // 获取SAP凭证压缩包文件名
    method: 'GET',
    path: '/syn/financeSalaryPushRecord/get-sap-zip-name'
  },

  {
    name: 'expenseItem-page', // 查询金蝶费用项目分页
    method: 'POST',
    path: '/kingdee/kingdeeExpenseItem/page'
  },
  {
    name: 'expenseItem-save', // 保存金蝶费用项目
    method: 'POST',
    path: '/kingdee/kingdeeExpenseItem/save'
  },
];
