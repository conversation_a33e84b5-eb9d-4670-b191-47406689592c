{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue?vue&type=style&index=0&id=5e906b0b&lang=css", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue", "mtime": 1754276220642}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1752725539056}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752725560473}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752725548209}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\n.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n", {"version": 3, "sources": ["localtionTree.vue"], "names": [], "mappings": ";AAgdA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "localtionTree.vue", "sourceRoot": "src/components/treeComp", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-popover\r\n      v-model=\"showSearchDeptTree\"\r\n      placement=\"right\"\r\n      width=\"800\"\r\n      trigger=\"click\"\r\n      @show=\"showSearchTreeEvent\"\r\n      @hide=\"hideSearchTreeEvent\"\r\n    >\r\n      <div>\r\n        <el-input\r\n          size=\"small\"\r\n          style=\"width: 100%; margin-bottom: 8px\"\r\n          placeholder=\"请输入关键字搜索\"\r\n          v-model=\"locationDesc\"\r\n          @input=\"onSearch\"\r\n          @clear=\"onSearch\"\r\n          clearable\r\n        >\r\n          default-expand-all\r\n        </el-input>\r\n      </div>\r\n      <div style=\"height: 460px; overflow-y: scroll\">\r\n        <el-tree\r\n          :props=\"props\"\r\n          :data=\"deptAndUserData\"\r\n          :show-checkbox=\"showCheckbox\"\r\n          :load=\"loadNode\"\r\n          v-bind=\"$attrs\"\r\n          v-on=\"$listeners\"\r\n          :lazy=\"isLazy\"\r\n          @node-click=\"handleNodeClick\"\r\n          @check=\"getTreeDatas\"\r\n          :highlight-current=\"highlightCurrent\"\r\n          node-key=\"primaryId\"\r\n          :key=\"setTimer\"\r\n          check-strictly\r\n          ref=\"tree\"\r\n          :default-checked-keys=\"Array.isArray(value) ? value : [value]\"\r\n          v-if=\"showSearchDeptTree\"\r\n        >\r\n          <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n            <span>\r\n              <span style=\"margin-left: 2px\"\r\n                >{{ data.label }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{\r\n                  node.label\r\n                }}</span\r\n              >\r\n            </span>\r\n          </span>\r\n        </el-tree>\r\n      </div>\r\n\r\n      <el-input\r\n        size=\"small\"\r\n        style=\"width: 100%; margin-left: 8px\"\r\n        v-model=\"selectValue\"\r\n        type=\"textarea\"\r\n        :placeholder=\"treeplaceholder\"\r\n        readonly\r\n        clearable\r\n        @clear=\"clearSelect\"\r\n        slot=\"reference\"\r\n      ></el-input>\r\n\r\n      <span class=\"dialog-footer\" v-if=\"$attrs.multiple\">\r\n        <el-button size=\"small\" @click=\"showSearchDeptTree = false\"\r\n          >取 消</el-button\r\n        >\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBindPositionForm\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {keys} from \"lodash\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      locationDesc: \"\",\r\n      treeData: [],\r\n      selectedNode: null,\r\n      deptAndUserData: [],\r\n      nodes: null,\r\n      resolves: null,\r\n      setTimer: null,\r\n      timer: null,\r\n      selectedNodes: [],\r\n      selectedUserList: [],\r\n      selectValue: \"\",\r\n      showSearchDeptTree: false,\r\n      checkedTreeList: [], // 选中的值\r\n      checkedListKey: [],\r\n    };\r\n  },\r\n  props: {\r\n    props: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          label: \"primaryCode\",\r\n          children: \"children\",\r\n          isLeaf: \"leaf\",\r\n        };\r\n      },\r\n    },\r\n    value: {\r\n      type: String | Array,\r\n      default: \"\",\r\n    },\r\n    cnvalue: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    treeplaceholder: {\r\n      type: String,\r\n      default: \"请选择数据\",\r\n    },\r\n    highlightCurrent: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    isLazy: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    showCheckbox: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      },\r\n    },\r\n    apiUrl: {\r\n      type: String,\r\n      default: \"firstLineDept/get-loc-info-lazy-tree\",\r\n    },\r\n    // 已绑定的位置ID数组，用于设置节点的disabled状态\r\n    boundLocationIds: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n  },\r\n  watch: {\r\n    value(val) {\r\n      if (!val) {\r\n        if (!this.$attrs.multiple) {\r\n          this.clearSelect();\r\n        } else {\r\n          this.$refs.tree && this.$refs.tree.setCheckedKeys(val);\r\n        }\r\n      } else {\r\n        // this.selectValue = filterTree[0] ? filterTree[0][this.treeProps.label] : '';\r\n      }\r\n    },\r\n    cnvalue(val) {\r\n      this.selectValue = val;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.setTimer = new Date().getTime();\r\n    this.selectValue = this.cnvalue;\r\n  },\r\n  methods: {\r\n    // 递归设置节点的disabled属性\r\n    setNodeDisabled(nodes) {\r\n      if (!nodes || !Array.isArray(nodes)) {\r\n        return nodes;\r\n      }\r\n\r\n      return nodes.map(node => {\r\n        // 创建节点的副本以避免直接修改原数据\r\n        const newNode = {...node};\r\n\r\n        // 检查当前节点的primaryId是否在已绑定的位置ID数组中\r\n        if (this.boundLocationIds && this.boundLocationIds.includes(newNode.primaryId)) {\r\n          newNode.disabled = true;\r\n        } else {\r\n          newNode.disabled = false;\r\n        }\r\n\r\n        // 如果有子节点，递归处理\r\n        if (newNode.children && Array.isArray(newNode.children)) {\r\n          newNode.children = this.setNodeDisabled(newNode.children);\r\n        }\r\n\r\n        return newNode;\r\n      });\r\n    },\r\n\r\n    hideSearchTreeEvent() {\r\n      this.locationDesc = \"\";\r\n    },\r\n    // 展示树时\r\n    showSearchTreeEvent() {\r\n      this.$api[this.apiUrl]({\r\n        parentId: 0,\r\n      }).then(data => {\r\n        // console.log(\"dataTree\", data);\r\n        // 设置节点的disabled属性\r\n        this.deptAndUserData = this.setNodeDisabled(data);\r\n      });\r\n\r\n      // this.$refs.tree && this.$refs.tree.setCheckedKeys([this.value]);\r\n    },\r\n    clearSelect() {\r\n      this.selectValue = \"\";\r\n      this.$refs.tree && this.$refs.tree.setCheckedKeys([]);\r\n    },\r\n    setCheckedKeys(ids) {\r\n      this.$refs.tree && this.$refs.tree.setCheckedKeys(ids);\r\n    },\r\n    // 搜索树节点\r\n    onSearch() {\r\n      if (this.timer) {\r\n        clearTimeout(this.timer);\r\n      }\r\n      this.timer = setTimeout(() => {\r\n        if (!this.isLazy) {\r\n          // 非懒加载\r\n\r\n          if (this.locationDesc.length > 0) {\r\n            this.$api[this.apiUrl]({\r\n              desc: this.locationDesc,\r\n            }).then(res => {\r\n              // 设置节点的disabled属性\r\n              this.deptAndUserData = this.setNodeDisabled(res);\r\n              console.log(\"this.showCheckbox1\", this.showCheckbox);\r\n              if (this.showCheckbox) {\r\n                console.log(\"this.showCheckbox2\", this.showCheckbox);\r\n                setTimeout(() => {\r\n                  this.selectedNodes.length\r\n                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n                }, 500);\r\n              }\r\n            });\r\n          } else {\r\n            this.$api[this.apiUrl]({\r\n              parentId: 0,\r\n            }).then(res => {\r\n              // 设置节点的disabled属性\r\n              this.deptAndUserData = this.setNodeDisabled(res);\r\n              console.log(\"this.showCheckbox1\", this.showCheckbox);\r\n              if (this.showCheckbox) {\r\n                console.log(\"this.showCheckbox2\", this.showCheckbox);\r\n                setTimeout(() => {\r\n                  this.selectedNodes.length\r\n                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n                }, 500);\r\n              }\r\n            });\r\n          }\r\n\r\n          return;\r\n        }\r\n        if (this.locationDesc.length) {\r\n          this.$api[this.apiUrl]({\r\n            desc: this.locationDesc,\r\n          }).then(res => {\r\n            // 设置节点的disabled属性\r\n            this.deptAndUserData = this.setNodeDisabled(res);\r\n            if (this.showCheckbox) {\r\n              setTimeout(() => {\r\n                this.selectedNodes.length\r\n                  && this.$refs.tree.setCheckedNodes(this.selectedNodes);\r\n              }, 500);\r\n            }\r\n          });\r\n        } else {\r\n          this.setTimer = new Date().getTime();\r\n          this.deptAndUserData = [];\r\n          this.nodes.data.primaryId = 0;\r\n          // console.log(\"====\", this.nodes);\r\n          this.loadNode(this.nodes, this.resolves);\r\n        }\r\n      }, 500);\r\n    },\r\n    getTreeDatas(data, list) {\r\n      if (!this.$attrs.multiple) {\r\n        let thisNode = this.$refs.tree.getNode(data.primaryId),\r\n          keys = []; // 获取已勾选节点的key值\r\n        if (thisNode && thisNode.checked) {\r\n          keys = [data];\r\n          // 当前节点若被选中\r\n          for (let i = thisNode.level; i > 1; i--) {\r\n            // 当前子节点选中，取消勾选父节点\r\n            this.$refs.tree.setChecked(thisNode.parent, false);\r\n            // 判断是否有父级节点\r\n            if (!thisNode.parent.checked) {\r\n              // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n              thisNode = thisNode.parent;\r\n              keys.unshift(thisNode.data);\r\n            }\r\n          }\r\n        }\r\n        let arr = [];\r\n        keys.forEach(row => {\r\n          console.log(\"row\", row, row.primaryCode);\r\n\r\n          arr.push(`${row.label} ${row.primaryCode}`);\r\n        });\r\n        //   console.log(keys,arr.join('/'), 'keys++++++++++++++',data.primaryId)\r\n        this.selectedNodes = list.checkedNodes;\r\n        this.selectValue = arr.join(\"→\");\r\n        const checkedNodes = this.$refs.tree.getCheckedNodes();\r\n\r\n        if (checkedNodes.length > 0) {\r\n          this.$emit(\"getSelectCnData\", arr.join(\"→\"), data);\r\n          this.$emit(\"getSelectData\", data.primaryId);\r\n          this.$emit(\"getSelectTreeData\", data);\r\n          this.$emit(\"getCnValue\", this.selectValue);\r\n        } else {\r\n          this.$emit(\"getSelectCnData\", \"\", \"\");\r\n          this.$emit(\"getSelectData\", \"\");\r\n          this.$emit(\"getSelectTreeData\", \"\");\r\n          this.$emit(\"getCnValue\", \"\");\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.showSearchDeptTree = false;\r\n      } else {\r\n        let thisNode = this.$refs.tree.getNode(data.primaryId),\r\n          keys = [data]; // 获取已勾选节点的key值\r\n        // 有取消子节点\r\n        if (thisNode && thisNode.checked && thisNode.childNodes.length > 0) {\r\n          this.clearCheckedChildren(thisNode.childNodes);\r\n        }\r\n\r\n        if (thisNode && thisNode.checked) {\r\n          // 当前节点若被选中\r\n          for (let i = thisNode.level; i > 1; i--) {\r\n            // 当前子节点选中，取消勾选父节点\r\n            this.$refs.tree.setChecked(thisNode.parent, false);\r\n          }\r\n        }\r\n\r\n        // 更新选中的节点列表\r\n        this.checkedTreeList = this.$refs.tree.getCheckedNodes() || [];\r\n\r\n        // 立即触发事件，传递选中的数据\r\n        let checkedListValue = [];\r\n        this.checkedListKey = [];\r\n\r\n        this.checkedTreeList.forEach(item => {\r\n          this.checkedListKey.push(item.primaryId);\r\n          let thisNode = this.$refs.tree.getNode(item.primaryId);\r\n          let keys = [thisNode.data];\r\n          if (thisNode && thisNode.checked) {\r\n            // 当前节点若被选中\r\n            for (let i = thisNode.level; i > 1; i--) {\r\n              // 判断是否有父级节点\r\n              if (!thisNode.parent.checked) {\r\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n                thisNode = thisNode.parent;\r\n                keys.unshift(thisNode.data);\r\n              }\r\n            }\r\n          }\r\n          let arr = [];\r\n          keys.forEach(row => {\r\n            arr.push(`${row.label} ${row.primaryCode}`);\r\n          });\r\n          checkedListValue.push(arr.join(\"→\"));\r\n        });\r\n\r\n        this.selectValue = checkedListValue.join(\"||\");\r\n        this.$emit(\"getSelectData\", this.checkedListKey);\r\n        this.$emit(\"getSelectCnData\", this.selectValue);\r\n      }\r\n      //\r\n    },\r\n    clearCheckedChildren(children) {\r\n      children.length > 0\r\n        && children.forEach(child => {\r\n          this.$refs.tree.setChecked(child, false);\r\n          if (child.childNodes) {\r\n            this.clearCheckedChildren(child.childNodes);\r\n          }\r\n        });\r\n    },\r\n    submitBindPositionForm() {\r\n      if (this.$attrs.multiple) {\r\n        let checkedListValue = [];\r\n        this.checkedListKey = [];\r\n        // console.log(\"this.checkedTreeList\", this.checkedTreeList);\r\n        this.checkedTreeList.forEach(item => {\r\n          this.checkedListKey.push(item.primaryId);\r\n          let thisNode = this.$refs.tree.getNode(item.primaryId);\r\n          let keys = [thisNode.data];\r\n          if (thisNode && thisNode.checked) {\r\n            // 当前节点若被选中\r\n            for (let i = thisNode.level; i > 1; i--) {\r\n              // 判断是否有父级节点\r\n              if (!thisNode.parent.checked) {\r\n                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组\r\n                thisNode = thisNode.parent;\r\n                keys.unshift(thisNode.data);\r\n              }\r\n            }\r\n          }\r\n          let arr = [];\r\n          keys.forEach(row => {\r\n            arr.push(`${row.label} ${row.primaryCode}`);\r\n          });\r\n          checkedListValue.push(arr.join(\"→\"));\r\n          this.selectValue = checkedListValue.join(\"||\");\r\n          this.$emit(\"getSelectData\", this.checkedListKey);\r\n          this.$emit(\"getSelectCnData\", this.selectValue);\r\n          this.showSearchDeptTree = false;\r\n        });\r\n      }\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.showCheckbox) {\r\n        return;\r\n      }\r\n      if (data.primaryId.indexOf(\"org\") !== -1) {\r\n        setTimeout(() => {\r\n          this.$refs.tree.setCurrentKey(this.selectedNode);\r\n        }, 0);\r\n      } else {\r\n        this.selectedNodes = [data];\r\n        this.selectedNode = data.primaryId;\r\n        this.$emit(\"treeNode\", data);\r\n      }\r\n    },\r\n    // 点击懒加载\r\n    loadNode(node, resolve) {\r\n      if (node.level === 0) {\r\n        this.showSearchTreeEvent();\r\n        return;\r\n      }\r\n      this.nodes = node;\r\n      this.resolves = resolve;\r\n      this.$api[this.apiUrl]({\r\n        parentId: node.data.primaryId,\r\n      }).then(res => {\r\n        // 设置节点的disabled属性\r\n        const processedData = this.setNodeDisabled(res);\r\n        resolve(processedData);\r\n        if (this.showCheckbox) {\r\n          setTimeout(() => {\r\n            this.$refs.tree.setCheckedKeys([this.value]);\r\n          }, 100);\r\n        } else {\r\n          setTimeout(() => {\r\n            this.$refs.tree.setCurrentKey(this.selectedNode);\r\n          }, 0);\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n</style>\r\n"]}]}