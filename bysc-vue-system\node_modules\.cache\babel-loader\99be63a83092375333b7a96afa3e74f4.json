{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue", "mtime": 1754276220640}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.array.find\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.string.includes\";\nexport default {\n  name: \"DrawingFormDialog\",\n  props: {\n    visible: {\n      type: <PERSON><PERSON>an,\n      default: false\n    },\n    mode: {\n      type: String,\n      default: \"add\",\n      // add: 新增, edit: 编辑\n      validator: function validator(value) {\n        return [\"add\", \"edit\"].includes(value);\n      }\n    },\n    editData: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  created: function created() {\n    this.getInit();\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      deptList: [],\n      formData: {\n        drawingName: \"\",\n        belongDeptKey: \"\",\n        drawingInfo: \"\" // 存储base64编码的图片\n      },\n      fileList: [],\n      saveLoading: false,\n      rules: {\n        drawingName: [{\n          required: true,\n          message: \"请输入图纸名称\",\n          trigger: \"blur\"\n        }, {\n          min: 1,\n          max: 50,\n          message: \"长度在 1 到 50 个字符\",\n          trigger: \"blur\"\n        }],\n        belongDeptKey: [{\n          required: true,\n          message: \"请选择所属部门\",\n          trigger: \"change\"\n        }],\n        drawingInfo: [{\n          validator: function validator(_rule, _value, callback) {\n            // 检查是否有图片数据（新上传的或编辑时已有的）\n            if (_this.formData.drawingInfo && _this.formData.drawingInfo.trim() !== \"\") {\n              callback();\n            } else {\n              callback(new Error(\"请上传图片\"));\n            }\n          },\n          trigger: [\"change\", \"blur\"]\n        }]\n      }\n    };\n  },\n  computed: {\n    dialogTitle: function dialogTitle() {\n      return this.mode === \"add\" ? \"新增图纸\" : \"编辑图纸\";\n    }\n  },\n  watch: {\n    visible: function visible(val) {\n      if (val) {\n        this.initFormData();\n      }\n    },\n    editData: {\n      handler: function handler(val) {\n        if (val && Object.keys(val).length > 0) {\n          this.initFormData();\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    getInit: function getInit() {\n      var _this2 = this;\n      this.$api[\"visualOpsManagement/workorderFirstLineDept-findAll\"]({}).then(function (res) {\n        _this2.deptList = res;\n      });\n    },\n    // 处理部门变化\n    handleDeptChange: function handleDeptChange(value) {\n      this.formData.belongDeptKey = value;\n    },\n    // 初始化表单数据\n    initFormData: function initFormData() {\n      var _this3 = this;\n      if (this.mode === \"edit\" && this.editData) {\n        this.formData = {\n          drawingName: this.editData.drawingName || \"\",\n          belongDeptKey: this.editData.belongDeptKey || \"\",\n          drawingInfo: this.editData.drawingInfo || \"\"\n        };\n        // 如果有已上传的图片，可以在这里处理\n        if (this.editData.drawingInfo) {\n          this.fileList = [{\n            name: this.editData.imageName || \"已上传图片\",\n            url: this.editData.drawingInfo,\n            uid: Date.now()\n          }];\n        } else {\n          this.fileList = [];\n        }\n      } else {\n        this.formData = {\n          drawingName: \"\",\n          belongDeptKey: \"\",\n          drawingInfo: \"\"\n        };\n        this.fileList = [];\n      }\n\n      // 清除表单验证，在编辑模式下如果有图片则重新验证\n      this.$nextTick(function () {\n        if (_this3.$refs.form) {\n          _this3.$refs.form.clearValidate();\n          // 在编辑模式下，如果有图片数据，触发一次验证以清除可能的错误提示\n          if (_this3.mode === \"edit\" && _this3.formData.drawingInfo) {\n            _this3.$refs.form.validateField(\"drawingInfo\");\n          }\n        }\n      });\n    },\n    // 上传前验证\n    beforeUpload: function beforeUpload(file) {\n      console.log(\"beforeUpload 被调用:\", file);\n\n      // 检查是否已经有文件\n      if (this.fileList.length >= 1) {\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\n        return false;\n      }\n      var isImage = /^image\\/(png|jpe?g)$/i.test(file.type);\n      var isLt10M = file.size / 1024 / 1024 < 10;\n      console.log(\"文件类型检查:\", isImage, file.type);\n      console.log(\"文件大小检查:\", isLt10M, file.size);\n      if (!isImage) {\n        this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\n        return false;\n      }\n      if (!isLt10M) {\n        this.$message.error(\"上传图片大小不能超过 10MB!\");\n        return false;\n      }\n      console.log(\"开始转换为base64\");\n      // 将文件转换为base64\n      this.convertToBase64(file);\n\n      // 阻止默认上传行为，因为我们使用base64方式\n      return false;\n    },\n    // 将文件转换为base64\n    convertToBase64: function convertToBase64(file) {\n      var _this4 = this;\n      var reader = new FileReader();\n      reader.onload = function (e) {\n        _this4.formData.drawingInfo = e.target.result;\n\n        // 手动添加到文件列表用于显示\n        _this4.fileList = [{\n          name: file.name,\n          url: e.target.result,\n          uid: file.uid || Date.now()\n        }];\n\n        // 触发表单验证，清除drawingInfo字段的错误提示\n        _this4.$nextTick(function () {\n          if (_this4.$refs.form) {\n            _this4.$refs.form.validateField(\"drawingInfo\");\n          }\n        });\n        _this4.$message.success(\"图片上传成功\");\n      };\n      reader.onerror = function (error) {\n        console.error(\"FileReader 错误:\", error);\n        _this4.$message.error(\"图片读取失败\");\n      };\n      reader.readAsDataURL(file);\n    },\n    // 预览图片（Element UI 上传组件的预览回调）\n    handlePreview: function handlePreview(file) {\n      // 使用文件的 url 或者 formData 中的 drawingInfo进行预览\n      var imageUrl = file.url || this.formData.drawingInfo;\n      if (imageUrl) {\n        // 创建一个新窗口来显示图片\n        var newWindow = window.open(\"\", \"_blank\");\n        newWindow.document.write(\"\\n          <html>\\n            <head><title>\\u56FE\\u7247\\u9884\\u89C8 - \".concat(file.name || \"图片\", \"</title></head>\\n            <body style=\\\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\\\">\\n              <div style=\\\"margin-bottom:10px;font-size:16px;color:#333;\\\">\\n                \").concat(file.name || \"图片预览\", \"\\n              </div>\\n              <img src=\\\"\").concat(imageUrl, \"\\\" style=\\\"max-width:100%;max-height:90vh;object-fit:contain;border:1px solid #ddd;border-radius:4px;\\\" alt=\\\"\\u9884\\u89C8\\u56FE\\u7247\\\" />\\n            </body>\\n          </html>\\n        \"));\n        newWindow.document.close();\n      } else {\n        this.$message.warning(\"无法预览图片\");\n      }\n    },\n    // 预览图片（通用方法，保留兼容性）\n    previewImage: function previewImage() {\n      if (this.formData.drawingInfo) {\n        // 创建一个新窗口来显示图片\n        var newWindow = window.open(\"\", \"_blank\");\n        newWindow.document.write(\"\\n          <html>\\n            <head><title>\\u56FE\\u7247\\u9884\\u89C8</title></head>\\n            <body style=\\\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\\\">\\n              <img src=\\\"\".concat(this.formData.drawingInfo, \"\\\" style=\\\"max-width:100%;max-height:100vh;object-fit:contain;\\\" alt=\\\"\\u9884\\u89C8\\u56FE\\u7247\\\" />\\n            </body>\\n          </html>\\n        \"));\n        newWindow.document.close();\n      }\n    },\n    // 替换图片\n    replaceImage: function replaceImage() {\n      this.$refs.hiddenFileInput.click();\n    },\n    // 删除图片\n    removeImage: function removeImage() {\n      var _this5 = this;\n      this.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function () {\n        _this5.formData.drawingInfo = \"\";\n        _this5.currentImageName = \"\";\n        _this5.currentImageSize = 0;\n        _this5.fileList = [];\n        _this5.$message.success(\"图片已删除\");\n      }).catch(function () {\n        // 用户取消删除\n      });\n    },\n    // 处理文件选择变化（Element UI 的 on-change 事件）\n    handleFileChange: function handleFileChange(file, fileList) {\n      // 检查是否已经有文件\n      if (fileList.length > 1) {\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\n        // 移除新添加的文件，保留第一个\n        this.fileList = [fileList[0]];\n        return;\n      }\n      if (file && file.raw) {\n        var rawFile = file.raw;\n\n        // 验证文件\n        var isImage = /^image\\/(png|jpe?g)$/i.test(rawFile.type);\n        var isLt10M = rawFile.size / 1024 / 1024 < 10;\n        if (!isImage) {\n          this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\n          this.fileList = [];\n          return;\n        }\n        if (!isLt10M) {\n          this.$message.error(\"上传图片大小不能超过 10MB!\");\n          this.fileList = [];\n          return;\n        }\n\n        // 转换为base64\n        this.convertToBase64(rawFile);\n      }\n    },\n    // 格式化文件大小\n    formatFileSize: function formatFileSize(bytes) {\n      if (bytes === 0) {\n        return \"0 B\";\n      }\n      var k = 1024;\n      var sizes = [\"B\", \"KB\", \"MB\", \"GB\"];\n      var i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    },\n    // 删除前确认（Element UI 的 before-remove 钩子）\n    handleBeforeRemove: function handleBeforeRemove() {\n      var _this6 = this;\n      return new Promise(function (resolve, reject) {\n        _this6.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(function () {\n          // 用户确认删除，允许删除操作\n          resolve(); // 允许删除\n        }).catch(function () {\n          // 用户取消删除\n          reject(); // 阻止删除\n        });\n      });\n    },\n    // 移除文件（Element UI 删除完成后的回调）\n    handleRemove: function handleRemove() {\n      var _this7 = this;\n      // 文件删除完成后，清空相关数据\n      this.formData.drawingInfo = \"\";\n      this.currentImageName = \"\";\n      this.currentImageSize = 0;\n\n      // 触发表单验证，显示drawingInfo字段的错误提示\n      this.$nextTick(function () {\n        if (_this7.$refs.form) {\n          _this7.$refs.form.validateField(\"drawingInfo\");\n        }\n      });\n      this.$message.success(\"图片已删除\");\n      console.log(\"文件删除完成\");\n    },\n    // 超出文件数量限制（保留方法以兼容旧代码）\n    handleExceed: function handleExceed() {\n      this.$message.warning(\"只能上传一张图片，请先删除已有图片后再上传新图片!\");\n    },\n    // 保存\n    handleSave: function handleSave() {\n      var _this8 = this;\n      this.$refs.form.validate(function (valid) {\n        if (valid) {\n          _this8.saveLoading = true;\n\n          // 根据选中的部门ID找到对应的部门名称\n          var selectedDept = _this8.deptList.find(function (dept) {\n            return dept.id === _this8.formData.belongDeptKey;\n          });\n          var belongDept = selectedDept ? selectedDept.name : '';\n\n          // 准备保存的数据\n          var saveData = {\n            drawingName: _this8.formData.drawingName,\n            belongDeptKey: _this8.formData.belongDeptKey,\n            belongDept: belongDept,\n            // 添加部门名称（中文）\n            drawingInfo: _this8.formData.drawingInfo,\n            id: _this8.editData && _this8.editData.id\n            // ...this.formData,\n            // mode: this.mode,\n            // editId: this.editData && this.editData.id,\n          };\n          console.log(\"保存数据:\", saveData);\n          console.log(\"选中的部门:\", selectedDept);\n          console.log(\"部门名称:\", belongDept);\n          console.log(\"图片Base64长度:\", _this8.formData.drawingInfo ? _this8.formData.drawingInfo.length : 0);\n          _this8.$api[\"visualOpsManagement/visualOpsManagement-save\"](saveData).then(function () {\n            _this8.saveLoading = false;\n            _this8.$message.success(_this8.mode === \"add\" ? \"新增成功\" : \"编辑成功\");\n\n            // 触发保存事件，传递表单数据\n            _this8.$emit(\"save\", saveData);\n            _this8.handleClose();\n          });\n        }\n      });\n    },\n    // 取消\n    handleCancel: function handleCancel() {\n      this.handleClose();\n    },\n    // 关闭弹窗\n    handleClose: function handleClose() {\n      this.$emit(\"update:visible\", false);\n      this.$emit(\"close\");\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "mode", "String", "validator", "value", "includes", "editData", "Object", "_default", "created", "getInit", "data", "_this", "deptList", "formData", "drawing<PERSON>ame", "belongDeptKey", "drawingInfo", "fileList", "saveLoading", "rules", "required", "message", "trigger", "min", "max", "_rule", "_value", "callback", "trim", "Error", "computed", "dialogTitle", "watch", "val", "initFormData", "handler", "keys", "length", "deep", "immediate", "methods", "_this2", "$api", "then", "res", "handleDeptChange", "_this3", "imageName", "url", "uid", "Date", "now", "$nextTick", "$refs", "form", "clearValidate", "validateField", "beforeUpload", "file", "console", "log", "$message", "error", "isImage", "test", "isLt10M", "size", "convertToBase64", "_this4", "reader", "FileReader", "onload", "e", "target", "result", "success", "onerror", "readAsDataURL", "handlePreview", "imageUrl", "newWindow", "window", "open", "document", "write", "concat", "close", "warning", "previewImage", "replaceImage", "hiddenFileInput", "click", "removeImage", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "currentImageName", "currentImageSize", "catch", "handleFileChange", "raw", "rawFile", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "handleBeforeRemove", "_this6", "Promise", "resolve", "reject", "handleRemove", "_this7", "handleExceed", "handleSave", "_this8", "validate", "valid", "selectedDept", "find", "dept", "id", "belongDept", "saveData", "$emit", "handleClose", "handleCancel"], "sources": ["src/bysc_system/views/visualOpsManagement/drawingManagement/components/DrawingFormDialog.vue"], "sourcesContent": ["<template>\r\n  <el-drawer\r\n    :title=\"dialogTitle\"\r\n    :visible.sync=\"visible\"\r\n    direction=\"rtl\"\r\n    size=\"600px\"\r\n    :close-on-press-escape=\"false\"\r\n    :wrapperClosable=\"false\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"formData\"\r\n      :rules=\"rules\"\r\n      label-width=\"100px\"\r\n      label-position=\"left\"\r\n    >\r\n      <el-form-item label=\"图纸名称\" prop=\"drawingName\">\r\n        <el-input\r\n          v-model.trim=\"formData.drawingName\"\r\n          placeholder=\"请输入图纸名称\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n           size=\"small\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属部门\" prop=\"belongDeptKey\">\r\n        <el-select\r\n          style=\"width: 100%\"\r\n          v-model=\"formData.belongDeptKey\"\r\n          placeholder=\"请选择\"\r\n          size=\"small\"\r\n          filterable\r\n          clearable\r\n        >\r\n          <el-option\r\n            :label=\"i.name\"\r\n            :value=\"i.id\"\r\n            v-for=\"i in deptList\"\r\n            :key=\"i.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"drawingInfo\" required>\r\n        <span slot=\"label\"> 上传图片 </span>\r\n        <el-upload\r\n          ref=\"upload\"\r\n          action=\"#\"\r\n           size=\"small\"\r\n          :file-list=\"fileList\"\r\n          :on-change=\"handleFileChange\"\r\n          :before-remove=\"handleBeforeRemove\"\r\n          :on-remove=\"handleRemove\"\r\n          :on-preview=\"handlePreview\"\r\n          :on-exceed=\"handleExceed\"\r\n          :auto-upload=\"false\"\r\n          accept=\".png,.jpg,.jpeg\"\r\n          list-type=\"picture-card\"\r\n          :limit=\"1\"\r\n        >\r\n          <i class=\"el-icon-plus\"></i>\r\n          <div slot=\"tip\" class=\"el-upload__tip\">\r\n            支持格式：PNG、JPG、JPEG格式<br />\r\n            文件大小：图片各尺寸不超过10MB<br />\r\n            尺寸要求：分辨率控制在2000×2000像素内<br />\r\n            上传数量：仅一张\r\n          </div>\r\n        </el-upload>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"drawer-footer\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSave\" :loading=\"saveLoading\"\r\n        >保存</el-button\r\n      >\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DrawingFormDialog\",\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    mode: {\r\n      type: String,\r\n      default: \"add\", // add: 新增, edit: 编辑\r\n      validator: value => [\"add\", \"edit\"].includes(value),\r\n    },\r\n    editData: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  created() {\r\n    this.getInit();\r\n  },\r\n  data() {\r\n    return {\r\n      deptList: [],\r\n      formData: {\r\n        drawingName: \"\",\r\n        belongDeptKey: \"\",\r\n        drawingInfo: \"\", // 存储base64编码的图片\r\n      },\r\n      fileList: [],\r\n      saveLoading: false,\r\n      rules: {\r\n        drawingName: [\r\n          {required: true, message: \"请输入图纸名称\", trigger: \"blur\"},\r\n          {\r\n            min: 1,\r\n            max: 50,\r\n            message: \"长度在 1 到 50 个字符\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        belongDeptKey: [\r\n          {required: true, message: \"请选择所属部门\", trigger: \"change\"},\r\n        ],\r\n\r\n        drawingInfo: [\r\n          {\r\n            validator: (_rule, _value, callback) => {\r\n              // 检查是否有图片数据（新上传的或编辑时已有的）\r\n              if (this.formData.drawingInfo && this.formData.drawingInfo.trim() !== \"\") {\r\n                callback();\r\n              } else {\r\n                callback(new Error(\"请上传图片\"));\r\n              }\r\n            },\r\n            trigger: [\"change\", \"blur\"],\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    dialogTitle() {\r\n      return this.mode === \"add\" ? \"新增图纸\" : \"编辑图纸\";\r\n    },\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      if (val) {\r\n        this.initFormData();\r\n      }\r\n    },\r\n    editData: {\r\n      handler(val) {\r\n        if (val && Object.keys(val).length > 0) {\r\n          this.initFormData();\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true,\r\n    },\r\n  },\r\n  methods: {\r\n    getInit() {\r\n      this.$api[\"visualOpsManagement/workorderFirstLineDept-findAll\"]({}).then(\r\n        res => {\r\n          this.deptList = res;\r\n        }\r\n      );\r\n    },\r\n    // 处理部门变化\r\n    handleDeptChange(value) {\r\n      this.formData.belongDeptKey = value;\r\n    },\r\n\r\n    // 初始化表单数据\r\n    initFormData() {\r\n      if (this.mode === \"edit\" && this.editData) {\r\n        this.formData = {\r\n          drawingName: this.editData.drawingName || \"\",\r\n          belongDeptKey: this.editData.belongDeptKey || \"\",\r\n          drawingInfo: this.editData.drawingInfo || \"\",\r\n        };\r\n        // 如果有已上传的图片，可以在这里处理\r\n        if (this.editData.drawingInfo) {\r\n          this.fileList = [\r\n            {\r\n              name: this.editData.imageName || \"已上传图片\",\r\n              url: this.editData.drawingInfo,\r\n              uid: Date.now(),\r\n            },\r\n          ];\r\n        } else {\r\n          this.fileList = [];\r\n        }\r\n      } else {\r\n        this.formData = {\r\n          drawingName: \"\",\r\n          belongDeptKey: \"\",\r\n          drawingInfo: \"\",\r\n        };\r\n        this.fileList = [];\r\n      }\r\n\r\n      // 清除表单验证，在编辑模式下如果有图片则重新验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.clearValidate();\r\n          // 在编辑模式下，如果有图片数据，触发一次验证以清除可能的错误提示\r\n          if (this.mode === \"edit\" && this.formData.drawingInfo) {\r\n            this.$refs.form.validateField(\"drawingInfo\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 上传前验证\r\n    beforeUpload(file) {\r\n      console.log(\"beforeUpload 被调用:\", file);\r\n\r\n      // 检查是否已经有文件\r\n      if (this.fileList.length >= 1) {\r\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\r\n        return false;\r\n      }\r\n\r\n      const isImage = /^image\\/(png|jpe?g)$/i.test(file.type);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      console.log(\"文件类型检查:\", isImage, file.type);\r\n      console.log(\"文件大小检查:\", isLt10M, file.size);\r\n\r\n      if (!isImage) {\r\n        this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error(\"上传图片大小不能超过 10MB!\");\r\n        return false;\r\n      }\r\n\r\n      console.log(\"开始转换为base64\");\r\n      // 将文件转换为base64\r\n      this.convertToBase64(file);\r\n\r\n      // 阻止默认上传行为，因为我们使用base64方式\r\n      return false;\r\n    },\r\n\r\n    // 将文件转换为base64\r\n    convertToBase64(file) {\r\n      const reader = new FileReader();\r\n      reader.onload = e => {\r\n        this.formData.drawingInfo = e.target.result;\r\n\r\n        // 手动添加到文件列表用于显示\r\n        this.fileList = [\r\n          {\r\n            name: file.name,\r\n            url: e.target.result,\r\n            uid: file.uid || Date.now(),\r\n          },\r\n        ];\r\n\r\n        // 触发表单验证，清除drawingInfo字段的错误提示\r\n        this.$nextTick(() => {\r\n          if (this.$refs.form) {\r\n            this.$refs.form.validateField(\"drawingInfo\");\r\n          }\r\n        });\r\n\r\n        this.$message.success(\"图片上传成功\");\r\n      };\r\n      reader.onerror = error => {\r\n        console.error(\"FileReader 错误:\", error);\r\n        this.$message.error(\"图片读取失败\");\r\n      };\r\n      reader.readAsDataURL(file);\r\n    },\r\n\r\n    // 预览图片（Element UI 上传组件的预览回调）\r\n    handlePreview(file) {\r\n      // 使用文件的 url 或者 formData 中的 drawingInfo进行预览\r\n      const imageUrl = file.url || this.formData.drawingInfo;\r\n      if (imageUrl) {\r\n        // 创建一个新窗口来显示图片\r\n        const newWindow = window.open(\"\", \"_blank\");\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head><title>图片预览 - ${file.name || \"图片\"}</title></head>\r\n            <body style=\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\">\r\n              <div style=\"margin-bottom:10px;font-size:16px;color:#333;\">\r\n                ${file.name || \"图片预览\"}\r\n              </div>\r\n              <img src=\"${imageUrl}\" style=\"max-width:100%;max-height:90vh;object-fit:contain;border:1px solid #ddd;border-radius:4px;\" alt=\"预览图片\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        this.$message.warning(\"无法预览图片\");\r\n      }\r\n    },\r\n\r\n    // 预览图片（通用方法，保留兼容性）\r\n    previewImage() {\r\n      if (this.formData.drawingInfo) {\r\n        // 创建一个新窗口来显示图片\r\n        const newWindow = window.open(\"\", \"_blank\");\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head><title>图片预览</title></head>\r\n            <body style=\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\">\r\n              <img src=\"${this.formData.drawingInfo}\" style=\"max-width:100%;max-height:100vh;object-fit:contain;\" alt=\"预览图片\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      }\r\n    },\r\n\r\n    // 替换图片\r\n    replaceImage() {\r\n      this.$refs.hiddenFileInput.click();\r\n    },\r\n\r\n    // 删除图片\r\n    removeImage() {\r\n      this.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.formData.drawingInfo = \"\";\r\n          this.currentImageName = \"\";\r\n          this.currentImageSize = 0;\r\n          this.fileList = [];\r\n          this.$message.success(\"图片已删除\");\r\n        })\r\n        .catch(() => {\r\n          // 用户取消删除\r\n        });\r\n    },\r\n\r\n    // 处理文件选择变化（Element UI 的 on-change 事件）\r\n    handleFileChange(file, fileList) {\r\n      // 检查是否已经有文件\r\n      if (fileList.length > 1) {\r\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\r\n        // 移除新添加的文件，保留第一个\r\n        this.fileList = [fileList[0]];\r\n        return;\r\n      }\r\n\r\n      if (file && file.raw) {\r\n        const rawFile = file.raw;\r\n\r\n        // 验证文件\r\n        const isImage = /^image\\/(png|jpe?g)$/i.test(rawFile.type);\r\n        const isLt10M = rawFile.size / 1024 / 1024 < 10;\r\n\r\n        if (!isImage) {\r\n          this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\r\n          this.fileList = [];\r\n          return;\r\n        }\r\n        if (!isLt10M) {\r\n          this.$message.error(\"上传图片大小不能超过 10MB!\");\r\n          this.fileList = [];\r\n          return;\r\n        }\r\n\r\n        // 转换为base64\r\n        this.convertToBase64(rawFile);\r\n      }\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) {\r\n        return \"0 B\";\r\n      }\r\n      const k = 1024;\r\n      const sizes = [\"B\", \"KB\", \"MB\", \"GB\"];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\r\n    },\r\n\r\n    // 删除前确认（Element UI 的 before-remove 钩子）\r\n    handleBeforeRemove() {\r\n      return new Promise((resolve, reject) => {\r\n        this.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            // 用户确认删除，允许删除操作\r\n            resolve(); // 允许删除\r\n          })\r\n          .catch(() => {\r\n            // 用户取消删除\r\n            reject(); // 阻止删除\r\n          });\r\n      });\r\n    },\r\n\r\n    // 移除文件（Element UI 删除完成后的回调）\r\n    handleRemove() {\r\n      // 文件删除完成后，清空相关数据\r\n      this.formData.drawingInfo = \"\";\r\n      this.currentImageName = \"\";\r\n      this.currentImageSize = 0;\r\n\r\n      // 触发表单验证，显示drawingInfo字段的错误提示\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.validateField(\"drawingInfo\");\r\n        }\r\n      });\r\n\r\n      this.$message.success(\"图片已删除\");\r\n      console.log(\"文件删除完成\");\r\n    },\r\n\r\n    // 超出文件数量限制（保留方法以兼容旧代码）\r\n    handleExceed() {\r\n      this.$message.warning(\r\n        \"只能上传一张图片，请先删除已有图片后再上传新图片!\"\r\n      );\r\n    },\r\n\r\n    // 保存\r\n    handleSave() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n\r\n          // 根据选中的部门ID找到对应的部门名称\r\n          const selectedDept = this.deptList.find(dept => dept.id === this.formData.belongDeptKey);\r\n          const belongDept = selectedDept ? selectedDept.name : '';\r\n\r\n          // 准备保存的数据\r\n          const saveData = {\r\n            drawingName: this.formData.drawingName,\r\n            belongDeptKey: this.formData.belongDeptKey,\r\n            belongDept: belongDept, // 添加部门名称（中文）\r\n            drawingInfo: this.formData.drawingInfo,\r\n            id: this.editData && this.editData.id,\r\n            // ...this.formData,\r\n            // mode: this.mode,\r\n            // editId: this.editData && this.editData.id,\r\n          };\r\n\r\n          console.log(\"保存数据:\", saveData);\r\n          console.log(\"选中的部门:\", selectedDept);\r\n          console.log(\"部门名称:\", belongDept);\r\n          console.log(\"图片Base64长度:\", this.formData.drawingInfo ? this.formData.drawingInfo.length : 0);\r\n\r\n          this.$api[\"visualOpsManagement/visualOpsManagement-save\"](saveData).then(\r\n            () => {\r\n              this.saveLoading = false;\r\n              this.$message.success(\r\n                this.mode === \"add\" ? \"新增成功\" : \"编辑成功\"\r\n              );\r\n\r\n              // 触发保存事件，传递表单数据\r\n              this.$emit(\"save\", saveData);\r\n\r\n              this.handleClose();\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n\r\n    // 取消\r\n    handleCancel() {\r\n      this.handleClose();\r\n    },\r\n\r\n    // 关闭弹窗\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false);\r\n      this.$emit(\"close\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n  text-align: right;\r\n  z-index: 1;\r\n}\r\n\r\n.el-form {\r\n  padding-bottom: 80px; // 为底部按钮留出空间\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/deep/ .el-upload--picture-card {\r\n  width: 100px;\r\n  height: 100px;\r\n  line-height: 100px;\r\n}\r\n\r\n/deep/ .el-upload-list--picture-card .el-upload-list__item {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n\r\n/deep/ .el-drawer__body {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAqFA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;MAAA;MACAG,SAAA,WAAAA,UAAAC,KAAA;QAAA,uBAAAC,QAAA,CAAAD,KAAA;MAAA;IACA;IACAE,QAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA,WAAAQ,SAAA;QAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,KAAA;QACAL,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,GAAA;UACAC,GAAA;UACAH,OAAA;UACAC,OAAA;QACA,EACA;QACAP,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QAEAN,WAAA,GACA;UACAd,SAAA,WAAAA,UAAAuB,KAAA,EAAAC,MAAA,EAAAC,QAAA;YACA;YACA,IAAAhB,KAAA,CAAAE,QAAA,CAAAG,WAAA,IAAAL,KAAA,CAAAE,QAAA,CAAAG,WAAA,CAAAY,IAAA;cACAD,QAAA;YACA;cACAA,QAAA,KAAAE,KAAA;YACA;UACA;UACAP,OAAA;QACA;MAEA;IACA;EACA;EACAQ,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA/B,IAAA;IACA;EACA;EACAgC,KAAA;IACApC,OAAA,WAAAA,QAAAqC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,YAAA;MACA;IACA;IACA7B,QAAA;MACA8B,OAAA,WAAAA,QAAAF,GAAA;QACA,IAAAA,GAAA,IAAA3B,MAAA,CAAA8B,IAAA,CAAAH,GAAA,EAAAI,MAAA;UACA,KAAAH,YAAA;QACA;MACA;MACAI,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACA/B,OAAA,WAAAA,QAAA;MAAA,IAAAgC,MAAA;MACA,KAAAC,IAAA,2DAAAC,IAAA,CACA,UAAAC,GAAA;QACAH,MAAA,CAAA7B,QAAA,GAAAgC,GAAA;MACA,CACA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAA1C,KAAA;MACA,KAAAU,QAAA,CAAAE,aAAA,GAAAZ,KAAA;IACA;IAEA;IACA+B,YAAA,WAAAA,aAAA;MAAA,IAAAY,MAAA;MACA,SAAA9C,IAAA,oBAAAK,QAAA;QACA,KAAAQ,QAAA;UACAC,WAAA,OAAAT,QAAA,CAAAS,WAAA;UACAC,aAAA,OAAAV,QAAA,CAAAU,aAAA;UACAC,WAAA,OAAAX,QAAA,CAAAW,WAAA;QACA;QACA;QACA,SAAAX,QAAA,CAAAW,WAAA;UACA,KAAAC,QAAA,IACA;YACAvB,IAAA,OAAAW,QAAA,CAAA0C,SAAA;YACAC,GAAA,OAAA3C,QAAA,CAAAW,WAAA;YACAiC,GAAA,EAAAC,IAAA,CAAAC,GAAA;UACA,EACA;QACA;UACA,KAAAlC,QAAA;QACA;MACA;QACA,KAAAJ,QAAA;UACAC,WAAA;UACAC,aAAA;UACAC,WAAA;QACA;QACA,KAAAC,QAAA;MACA;;MAEA;MACA,KAAAmC,SAAA;QACA,IAAAN,MAAA,CAAAO,KAAA,CAAAC,IAAA;UACAR,MAAA,CAAAO,KAAA,CAAAC,IAAA,CAAAC,aAAA;UACA;UACA,IAAAT,MAAA,CAAA9C,IAAA,eAAA8C,MAAA,CAAAjC,QAAA,CAAAG,WAAA;YACA8B,MAAA,CAAAO,KAAA,CAAAC,IAAA,CAAAE,aAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACAC,OAAA,CAAAC,GAAA,sBAAAF,IAAA;;MAEA;MACA,SAAAzC,QAAA,CAAAoB,MAAA;QACA,KAAAwB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAAC,OAAA,2BAAAC,IAAA,CAAAN,IAAA,CAAA7D,IAAA;MACA,IAAAoE,OAAA,GAAAP,IAAA,CAAAQ,IAAA;MAEAP,OAAA,CAAAC,GAAA,YAAAG,OAAA,EAAAL,IAAA,CAAA7D,IAAA;MACA8D,OAAA,CAAAC,GAAA,YAAAK,OAAA,EAAAP,IAAA,CAAAQ,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAG,OAAA;QACA,KAAAJ,QAAA,CAAAC,KAAA;QACA;MACA;MAEAH,OAAA,CAAAC,GAAA;MACA;MACA,KAAAO,eAAA,CAAAT,IAAA;;MAEA;MACA;IACA;IAEA;IACAS,eAAA,WAAAA,gBAAAT,IAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAC,CAAA;QACAJ,MAAA,CAAAvD,QAAA,CAAAG,WAAA,GAAAwD,CAAA,CAAAC,MAAA,CAAAC,MAAA;;QAEA;QACAN,MAAA,CAAAnD,QAAA,IACA;UACAvB,IAAA,EAAAgE,IAAA,CAAAhE,IAAA;UACAsD,GAAA,EAAAwB,CAAA,CAAAC,MAAA,CAAAC,MAAA;UACAzB,GAAA,EAAAS,IAAA,CAAAT,GAAA,IAAAC,IAAA,CAAAC,GAAA;QACA,EACA;;QAEA;QACAiB,MAAA,CAAAhB,SAAA;UACA,IAAAgB,MAAA,CAAAf,KAAA,CAAAC,IAAA;YACAc,MAAA,CAAAf,KAAA,CAAAC,IAAA,CAAAE,aAAA;UACA;QACA;QAEAY,MAAA,CAAAP,QAAA,CAAAc,OAAA;MACA;MACAN,MAAA,CAAAO,OAAA,aAAAd,KAAA;QACAH,OAAA,CAAAG,KAAA,mBAAAA,KAAA;QACAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;MACAO,MAAA,CAAAQ,aAAA,CAAAnB,IAAA;IACA;IAEA;IACAoB,aAAA,WAAAA,cAAApB,IAAA;MACA;MACA,IAAAqB,QAAA,GAAArB,IAAA,CAAAV,GAAA,SAAAnC,QAAA,CAAAG,WAAA;MACA,IAAA+D,QAAA;QACA;QACA,IAAAC,SAAA,GAAAC,MAAA,CAAAC,IAAA;QACAF,SAAA,CAAAG,QAAA,CAAAC,KAAA,4EAAAC,MAAA,CAEA3B,IAAA,CAAAhE,IAAA,sNAAA2F,MAAA,CAGA3B,IAAA,CAAAhE,IAAA,iEAAA2F,MAAA,CAEAN,QAAA,kMAGA;QACAC,SAAA,CAAAG,QAAA,CAAAG,KAAA;MACA;QACA,KAAAzB,QAAA,CAAA0B,OAAA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA,SAAA3E,QAAA,CAAAG,WAAA;QACA;QACA,IAAAgE,SAAA,GAAAC,MAAA,CAAAC,IAAA;QACAF,SAAA,CAAAG,QAAA,CAAAC,KAAA,6MAAAC,MAAA,CAIA,KAAAxE,QAAA,CAAAG,WAAA,2JAGA;QACAgE,SAAA,CAAAG,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAG,YAAA,WAAAA,aAAA;MACA,KAAApC,KAAA,CAAAqC,eAAA,CAAAC,KAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnG,IAAA;MACA,GACA8C,IAAA;QACAkD,MAAA,CAAAhF,QAAA,CAAAG,WAAA;QACA6E,MAAA,CAAAI,gBAAA;QACAJ,MAAA,CAAAK,gBAAA;QACAL,MAAA,CAAA5E,QAAA;QACA4E,MAAA,CAAAhC,QAAA,CAAAc,OAAA;MACA,GACAwB,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA1C,IAAA,EAAAzC,QAAA;MACA;MACA,IAAAA,QAAA,CAAAoB,MAAA;QACA,KAAAwB,QAAA,CAAAC,KAAA;QACA;QACA,KAAA7C,QAAA,IAAAA,QAAA;QACA;MACA;MAEA,IAAAyC,IAAA,IAAAA,IAAA,CAAA2C,GAAA;QACA,IAAAC,OAAA,GAAA5C,IAAA,CAAA2C,GAAA;;QAEA;QACA,IAAAtC,OAAA,2BAAAC,IAAA,CAAAsC,OAAA,CAAAzG,IAAA;QACA,IAAAoE,OAAA,GAAAqC,OAAA,CAAApC,IAAA;QAEA,KAAAH,OAAA;UACA,KAAAF,QAAA,CAAAC,KAAA;UACA,KAAA7C,QAAA;UACA;QACA;QACA,KAAAgD,OAAA;UACA,KAAAJ,QAAA,CAAAC,KAAA;UACA,KAAA7C,QAAA;UACA;QACA;;QAEA;QACA,KAAAkD,eAAA,CAAAmC,OAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAA,KAAA;QACA;MACA;MACA,IAAAC,CAAA;MACA,IAAAC,KAAA;MACA,IAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAhD,GAAA,CAAA4C,KAAA,IAAAI,IAAA,CAAAhD,GAAA,CAAA6C,CAAA;MACA,OAAAK,UAAA,EAAAN,KAAA,GAAAI,IAAA,CAAAG,GAAA,CAAAN,CAAA,EAAAE,CAAA,GAAAK,OAAA,aAAAN,KAAA,CAAAC,CAAA;IACA;IAEA;IACAM,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAApB,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAnG,IAAA;QACA,GACA8C,IAAA;UACA;UACAyE,OAAA;QACA,GACAjB,KAAA;UACA;UACAkB,MAAA;QACA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA1G,QAAA,CAAAG,WAAA;MACA,KAAAiF,gBAAA;MACA,KAAAC,gBAAA;;MAEA;MACA,KAAA9C,SAAA;QACA,IAAAmE,MAAA,CAAAlE,KAAA,CAAAC,IAAA;UACAiE,MAAA,CAAAlE,KAAA,CAAAC,IAAA,CAAAE,aAAA;QACA;MACA;MAEA,KAAAK,QAAA,CAAAc,OAAA;MACAhB,OAAA,CAAAC,GAAA;IACA;IAEA;IACA4D,YAAA,WAAAA,aAAA;MACA,KAAA3D,QAAA,CAAA0B,OAAA,CACA,2BACA;IACA;IAEA;IACAkC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAArE,KAAA,CAAAC,IAAA,CAAAqE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAxG,WAAA;;UAEA;UACA,IAAA2G,YAAA,GAAAH,MAAA,CAAA9G,QAAA,CAAAkH,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,EAAA,KAAAN,MAAA,CAAA7G,QAAA,CAAAE,aAAA;UAAA;UACA,IAAAkH,UAAA,GAAAJ,YAAA,GAAAA,YAAA,CAAAnI,IAAA;;UAEA;UACA,IAAAwI,QAAA;YACApH,WAAA,EAAA4G,MAAA,CAAA7G,QAAA,CAAAC,WAAA;YACAC,aAAA,EAAA2G,MAAA,CAAA7G,QAAA,CAAAE,aAAA;YACAkH,UAAA,EAAAA,UAAA;YAAA;YACAjH,WAAA,EAAA0G,MAAA,CAAA7G,QAAA,CAAAG,WAAA;YACAgH,EAAA,EAAAN,MAAA,CAAArH,QAAA,IAAAqH,MAAA,CAAArH,QAAA,CAAA2H;YACA;YACA;YACA;UACA;UAEArE,OAAA,CAAAC,GAAA,UAAAsE,QAAA;UACAvE,OAAA,CAAAC,GAAA,WAAAiE,YAAA;UACAlE,OAAA,CAAAC,GAAA,UAAAqE,UAAA;UACAtE,OAAA,CAAAC,GAAA,gBAAA8D,MAAA,CAAA7G,QAAA,CAAAG,WAAA,GAAA0G,MAAA,CAAA7G,QAAA,CAAAG,WAAA,CAAAqB,MAAA;UAEAqF,MAAA,CAAAhF,IAAA,iDAAAwF,QAAA,EAAAvF,IAAA,CACA;YACA+E,MAAA,CAAAxG,WAAA;YACAwG,MAAA,CAAA7D,QAAA,CAAAc,OAAA,CACA+C,MAAA,CAAA1H,IAAA,4BACA;;YAEA;YACA0H,MAAA,CAAAS,KAAA,SAAAD,QAAA;YAEAR,MAAA,CAAAU,WAAA;UACA,CACA;QACA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAD,WAAA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAD,KAAA;MACA,KAAAA,KAAA;IACA;EACA;AACA"}]}