<template>
  <div v-loading="spinShow" class="draw">
    <!-- <el-loading :fullscreen="false" v-if="spinShow">
      <div>正在加载图片</div>
    </el-loading> -->
    <div class="drawTop" ref="drawTop" v-if="lineStep == lineNum">
      <!-- <div>
        <el-upload
          class="upload-demo"
          action=""
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange">
          <el-button type="primary" size="small">选择文件</el-button>
        </el-upload>
      </div> -->
      <div>
        <el-button @click="goBack" size="small">返回</el-button>
      </div>
      <!-- <div>
        是否开启模糊查询（标准层开启，其它关闭）：<el-switch v-model="isOpenLike"/>
      </div> -->
      <!-- <div>
        <el-button @click="resetAll" size="small">重新绘制</el-button>
      </div> -->
      <div>
        <el-button @click="deleteAllAreas" size="small" type="danger">删除全部区域</el-button>
      </div>
      <div>
        <el-button @click="toggleAreaNames" size="small" :type="showAreaNames ? 'success' : 'info'">
          {{ showAreaNames ? '隐藏区域名称' : '显示区域名称' }}
        </el-button>
      </div>

      <div>
        <el-button-group>
          <el-button @click="zoomIn" icon="el-icon-zoom-in" size="small">放大</el-button>
          <el-button @click="zoomOut" icon="el-icon-zoom-out" size="small">缩小</el-button>
          <!-- <el-button @click="resetZoom" size="small">重置缩放</el-button>
          <el-button @click="autoFit" type="primary" size="small">自适应屏幕</el-button> -->
        </el-button-group>
      </div>
      <div>
        <el-button type="primary" @click="exportJson" size="small">保存区域</el-button>
      </div>
      <!-- <div>
        <el-dropdown @command="handleDefaultZoomChange">
          <el-button size="small">
            默认缩放: {{ defaultZoomText }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1.0">100%</el-dropdown-item>
            <el-dropdown-item command="1.3">130%</el-dropdown-item>
            <el-dropdown-item command="1.5">150%</el-dropdown-item>
            <el-dropdown-item command="1.8">180%</el-dropdown-item>
            <el-dropdown-item command="2.0">200%</el-dropdown-item>
            <el-dropdown-item command="2.2">220%</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div> -->
    </div>
    <div>
      （鼠标左键点击图片绘制区域，任意位置点击右键自动连接起点和终点完成区域绘制，可重复此步骤绘制多个区域）
    </div>
    <div>
      （鼠标悬停在已绘制区域上可高亮显示，点击区域内任意位置即可删除该区域）
    </div>
    <div style="display: flex;">
      <div class="canvas-container" ref="canvasContainer">
        <!-- 固定在容器右上角的滚动控制按钮，只在需要时显示 -->
        <div v-if="showScrollButtons" class="scroll-controls">
          <el-button-group>
            <el-button @click="scrollLeft" icon="el-icon-arrow-left" size="mini" title="向左滚动"></el-button>
            <el-button @click="scrollRight" icon="el-icon-arrow-right" size="mini" title="向右滚动"></el-button>
          </el-button-group>
        </div>
        <div class="content" ref="content"></div>
        <input
          v-show="isShow"
          type="text"
          @blur="txtBlue"
          ref="txt"
          id="txt"
          style="
            z-index: 9999;
            position: absolute;
            border: 0;
            background: none;
            outline: none;
          "
        />
      </div>
      <!-- 区域列表显示面板 -->
      <div style="width: 25%; padding-left: 10px; max-height: 600px; overflow-y: auto;">
        <h4>已绘制区域列表</h4>
        <el-empty v-if="roomNameList.length === 0" description="暂无区域"></el-empty>
        <el-table
          v-else
          :data="roomNameList"
          style="width: 100%"
          size="small"
          :max-height="550"
          @row-click="highlightArea"
          @row-mouseover="mouseoverArea"
          @row-mouseout="mouseoutArea">
          <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
          <el-table-column prop="roomName" label="名称" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <a
                style="margin-left: 10px;"
                @click.stop="deleteAreaByIndex(scope.$index)">
                删除
              </a>

            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      :visible.sync="areaFormVisible"
      title="区域信息"
      width="400px"
      :close-on-click-modal="false"
      @closed="handleAreaFormCancel">
      <el-form
        ref="areaForm"
        :model="areaForm"
        :rules="areaFormRules"
        label-width="100px"
        size="small">
        <el-form-item label="名称" prop="roomName">
          <el-input v-model="areaForm.roomName" placeholder="请输入名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAreaFormCancel">取消</el-button>
        <el-button type="primary" @click="handleAreaFormSubmit" :loading="areaFormLoading">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="imgModal"
      title="更换图片"
      width="400px"
      @closed="imgCancel">
      <el-form
        ref="imgForm"
        :model="imgForm"
        :rules="imgFormValidate"
        label-width="110px">
        <el-form-item label="楼层号" prop="floorNo">
          <el-input
            v-model.trim="imgForm.floorNo"
            maxlength="32"
            placeholder="楼层号">
          </el-input>
        </el-form-item>
        <el-form-item label="图片地址" prop="imgUrl">
          <el-input
            v-model.trim="imgForm.imgUrl"
            maxlength="256"
            placeholder="图片地址">
          </el-input>
          <!-- <multi-upload-pic-input :maxCount="1" :maxSize="10240" @on-change="handleMultiUpload2" width="235px" ref="multiUploadImage"></multi-upload-pic-input> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="imgCancel">取消</el-button>
        <el-button type="primary" :loading="imgOkLoading" @click="imgOk">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'callout',
  components: {
  },
  props: {
    areaArr: Array,
  },
  data() {
    return {
      imgForm: {},
      imgModal: false,
      imgOkLoading: false,
      imgFormValidate: {},

      isOpenLike: false,
      imgUrl: '',
      roomNameList: [],
      spinShow: false,
      imgPath: '',
      finalArr: [], // 绝对坐标数组，用于绘制
      finalArrRelative: [], // 相对坐标数组，用于保存
      dataArr: [],
      isShow: false,
      canvas: '',
      ctx: '',
      ctxX: 0,
      ctxY: 0,
      lineWidth: 3,
      type: 'L',
      typeOption: [
        {label: '线', value: 'L'},
      // { label: '矩形', value: 'R' }
      //   {label: "箭头", value: "A"},
      //   {label: "文字", value: "T"},
      ],
      canvasHistory: [],
      step: 0,
      loading: false,
      fillStyle: '#CB0707',
      strokeStyle: '#1E90FF',
      lineNum: 2,
      linePeak: [],
      lineStep: 2,
      ellipseR: 0.5,
      dialogVisible: false,
      isUnfold: true,
      fontSize: 18,
      fontColor: '#333333',
      fontFamily: 'Microsoft YaHei, Arial, sans-serif',
      img: new Image(),
      hoveredAreaIndex: -1, // 当前鼠标悬停的区域索引
      highlightColor: '#2E8AE6',
      deleteMode: false, // 是否处于删除模式
      lastHoveredAreaIndex: -1, // 用于表格行悬停时记录之前高亮的区域索引
      zoomLevel: 1,
      initialZoomLevel: 1,
      defaultZoomFactor: 1, // 增大默认缩放系数到1.5
      showAreaNames: true,
      // 区域表单相关
      areaFormVisible: false,
      areaFormLoading: false,
      areaForm: {
        roomName: '',
        roomType: '',
        remark: ''
      },
      areaFormRules: {
        roomName: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      tempAreaData: null, // 临时存储新绘制的区域数据
      debounceTimer: null, // 添加防抖定时器
      // 滚动控制相关
      scrollLeftBtn: null,
      scrollRightBtn: null,
      // 保存当前drawingId，用于刷新页面时恢复
      currentDrawingId: ''
    };
  },
  computed: {
    defaultZoomText() {
      return `${Math.round(this.defaultZoomFactor * 100)}%`;
    },
    // 判断是否需要显示滚动按钮
    showScrollButtons() {
      if (!this.$refs.canvasContainer || !this.canvas) {
        return false;
      }
      // 当画布宽度大于容器宽度时显示滚动按钮
      return this.canvas.width * this.zoomLevel > this.$refs.canvasContainer.clientWidth;
    }
  },
  created() {
    // 从URL参数或localStorage获取drawingId
    const drawingId = this.$route.query.drawingId || localStorage.getItem('currentDrawingId');

    // 如果有drawingId，保存到组件状态和localStorage
    if (drawingId) {
      this.currentDrawingId = drawingId;
      localStorage.setItem('currentDrawingId', drawingId);

      // 如果URL中没有drawingId但localStorage有，则更新URL
      if (!this.$route.query.drawingId) {
        this.$router.replace({
          query: {...this.$route.query, drawingId}
        });
      }
    }
  },
  beforeDestroy() {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.checkScrollNeeded);
  },
  mounted() {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.checkScrollNeeded);

    // 获取drawingId并加载数据
    const drawingId = this.currentDrawingId || this.$route.query.drawingId;
    if (drawingId) {
      this.getList();
    } else {
      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');
      setTimeout(() => {
        this.goBack();
      }, 2000);
    }
  },
  methods: {
    getList() {
      // 优先使用组件状态中的drawingId，其次是URL参数
      const drawingId = this.currentDrawingId || this.$route.query.drawingId;

      if (!drawingId) {
        this.$message.warning('缺少图纸ID参数');
        return;
      }

      this.$api['areaManagement/maintenanceDrawingManagement-get']({
        drawingId: drawingId
      }).then(data => {
        console.log(data, 'data');
        if (data) {
          // 获取base64图片数据
          const drawingData = data;
          if (drawingData.drawingInfo) {
            // 设置图片路径为base64数据
            this.imgPath = drawingData.drawingInfo;
            // 图纸基本信息
            this.imgUrl = drawingData.drawingInfo;

            // 初始化画布，渲染图片
            this.init();

            // 图片渲染完成后，获取已有区域数据
            this.getRegionList();
          } else {
            this.$message.error('图片数据不存在');
          }
        }
      }).catch(error => {
        console.error('获取图纸数据失败:', error);
        this.$message.error('获取图纸数据失败');
      });
    },

    // 获取已有区域数据
    getRegionList() {
      // 优先使用组件状态中的drawingId，其次是URL参数
      const drawingId = this.currentDrawingId || this.$route.query.drawingId;

      if (!drawingId) {
        return;
      }

      this.$api['areaManagement/maintenanceRegion-list']({
        drawingId: drawingId
      }).then(data => {
        if (data && data.length > 0) {
          // 将区域信息转换为需要的格式，并保存原始数据中的ID
          this.roomNameList = data.map(region => ({
            roomName: region.regionName,
            roomType: '',
            remark: region.remark || '',
            id: region.id || null // 保存原始区域ID，用于后续删除操作
          }));

          // 解析区域坐标数据
          const areaCoordinates = data.map(region => {
            try {
              return JSON.parse(region.pointJson || '[]');
            } catch (e) {
              console.error('Error parsing pointJson:', e);
              return [];
            }
          }).filter(coords => coords.length > 0);

          // 保存相对坐标数据
          this.finalArrRelative = [...areaCoordinates];

          // 处理区域数据并绘制到画布上
          if (areaCoordinates.length > 0) {
            // 转换成绝对坐标并保存
            this.finalArr = this.processAreaData(areaCoordinates);

            // 绘制所有区域
            this.redrawCanvas();
          }
        }
      }).catch(error => {
        console.error('获取区域数据失败:', error);
      });
    },
    goBack() {
      this.$router.push('/system/visualOpsManagement/drawingManagement');
    },
    editAreaByIndex(index) {

    },
    handleFileChange(file) {
      const rawFile = file.raw;
      if (!rawFile) {
        return;
      }
      const reader = new FileReader();
      reader.onload = e => {
        this.imgPath = e.target.result;
        this.spinShow = true;
        this.init();
      };
      reader.readAsDataURL(rawFile);
    },
    // 检查是否需要滚动按钮
    checkScrollNeeded() {
      if (this.$refs.canvasContainer && this.canvas) {
      // 通过Vue的响应式系统触发计算属性重新计算
        this.$forceUpdate();
      // 不再需要调整容器高度
      // this.adjustContainerHeight();
      }
    },
    // 不再需要调整容器高度的方法
    /*
  adjustContainerHeight() {
    if (this.$refs.canvasContainer && this.canvas) {
      const canvasHeight = this.canvas.height * this.zoomLevel;
      // 设置容器高度为画布高度加上一些边距
      this.$refs.canvasContainer.style.height = `${canvasHeight + 20}px`;
    }
  },
  */
    // 向左滚动画布
    scrollLeft() {
      if (this.$refs.canvasContainer) {
        const container = this.$refs.canvasContainer;
        const scrollAmount = Math.min(300, container.clientWidth / 2);
        container.scrollLeft -= scrollAmount;
      }
    },
    // 向右滚动画布
    scrollRight() {
      if (this.$refs.canvasContainer) {
        const container = this.$refs.canvasContainer;
        const scrollAmount = Math.min(300, container.clientWidth / 2);
        container.scrollLeft += scrollAmount;
      }
    },
    init() {
      let _this = this;
      let image = new Image();
      image.setAttribute('crossOrigin', 'anonymous');
      image.src = this.imgPath;
      image.onload = function () {
      // 图片加载完，再draw 和 toDataURL
        if (image.complete) {
          _this.spinShow = false;
          _this.img = image;
          let content = _this.$refs.content;
          _this.canvas = document.createElement('canvas');
          _this.canvas.height = _this.img.height;
          _this.canvas.width = _this.img.width;
          _this.canvas.setAttribute('style', 'border:2px solid red;');
          _this.canvas.setAttribute('id', 'myCanvas');
          _this.ctx = _this.canvas.getContext('2d');
          _this.ctx.globalAlpha = 1;
          _this.ctx.drawImage(_this.img, 0, 0);
          _this.canvasHistory.push(_this.canvas.toDataURL());
          _this.ctx.globalCompositeOperation = _this.type;

          // 清空之前的内容
          content.innerHTML = '';
          content.appendChild(_this.canvas);

          // 重置交互状态
          _this.deleteMode = false;
          _this.hoveredAreaIndex = -1;

          // 预设最小缩放比例，防止图片太小
          _this.defaultZoomFactor = Math.max(_this.defaultZoomFactor, 1.2);

          // 自动适应屏幕
          _this.$nextTick(() => {
            _this.autoFit();
            // 不再需要调整容器高度
            // _this.adjustContainerHeight();
            // 检查是否需要滚动按钮
            _this.checkScrollNeeded();
          });

          _this.bindEventLisner();

          if (_this.areaArr) {
            _this.finalArr = _this.processAreaData(_this.areaArr);
            _this.finalArr.forEach(i => {
              _this.createL2(i);
            });
          }
        }
      };
    },
    radioClick(item) {
      if (item != 'T') {
        this.txtBlue();
        this.resetTxt();
      }
    },
    // 下载画布
    downLoad() {
      let _this = this;
      let url = _this.canvas.toDataURL('image/png');
      let fileName = 'canvas.png';
      if ('download' in document.createElement('a')) {
      // 非IE下载
        const elink = document.createElement('a');
        elink.download = fileName;
        elink.style.display = 'none';
        elink.href = url;
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
      } else {
      // IE10+下载
        navigator.msSaveBlob(url, fileName);
      }
    },
    // 重置所有内容
    resetAll() {
      this.dataArr = [];
      this.finalArr = [];
      this.finalArrRelative = []; // 同时清空相对坐标数组
      this.roomNameList = [];
      this.$emit('getPointArr', this.finalArr);
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.canvasHistory = [];
      this.ctx.drawImage(this.img, 0, 0);
      this.canvasHistory.push(this.canvas.toDataURL());
      this.step = 0;
      this.resetTxt();
      this.hoveredAreaIndex = -1;
      this.deleteMode = false;
      // 应用缩放
      this.applyZoom();
    },
    // 保存区域规划
    save() {
    // this.finalArr
    },
    // 清空当前画布
    reset() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(this.img, 0, 0);
      this.resetTxt();
    },
    // 撤销方法
    repeal() {
      let _this = this;
      if (this.isShow) {
        _this.resetTxt();
        _this._repeal();
      } else {
        _this._repeal();
      }
    },
    _repeal() {
      if (this.step >= 1) {
        this.step = this.step - 1;
        let canvasPic = new Image();
        canvasPic.src = this.canvasHistory[this.step];
        canvasPic.addEventListener('load', () => {
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          this.ctx.drawImage(canvasPic, 0, 0);
          this.loading = true;
        });
      } else {
        this.$message.warning('不能再继续撤销了');
      }
    },
    // 恢复方法
    canvasRedo() {
      if (this.step < this.canvasHistory.length - 1) {
        if (this.step == 0) {
          this.step = 1;
        } else {
          this.step++;
        }
        let canvasPic = new Image();
        canvasPic.src = this.canvasHistory[this.step];
        canvasPic.addEventListener('load', () => {
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          this.ctx.drawImage(canvasPic, 0, 0);
        });
      } else {
        this.$message.warning('已经是最新的记录了');
      }
    },
    // 绘制历史数组中的最后一个
    rebroadcast() {
      let canvasPic = new Image();
      canvasPic.src = this.canvasHistory[this.step];
      canvasPic.addEventListener('load', () => {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(canvasPic, 0, 0);
        this.loading = true;
      });
    },
    // 绑定事件,判断分支
    bindEventLisner() {
      let _this = this;
      let r1, r2; // 绘制圆形，矩形需要
      this.canvas.addEventListener('click', function (e) {
        if (_this.type == 'L' && e.button == 0) {
          const coords = _this.getAdjustedCoordinates(e);
          // 如果处于删除模式，检查是否点击在区域内
          if (_this.deleteMode) {
            _this.checkAndDeleteArea(coords);
          } else {
            _this.createL(coords, 'begin');
          }
        }
      });
      this.canvas.oncontextmenu = function (e) {
        const coords = _this.getAdjustedCoordinates(e);
        _this.createL(coords, 'end');
        return false;
      };

      // 添加鼠标移动事件监听器，用于区域高亮，使用防抖处理
      this.canvas.addEventListener('mousemove', function (e) {
      // 清除之前的定时器
        if (_this.debounceTimer) {
          clearTimeout(_this.debounceTimer);
        }

        // 创建新的定时器，延迟执行高亮逻辑
        _this.debounceTimer = setTimeout(() => {
          if (_this.finalArr && _this.finalArr.length > 0) {
            const coords = _this.getAdjustedCoordinates(e);
            let isOverArea = false;
            let hoveredIndex = -1;

            // 检查鼠标是否在任何一个区域内
            for (let i = 0; i < _this.finalArr.length; i++) {
              if (_this.isPointInPolygon(coords, _this.finalArr[i])) {
                hoveredIndex = i;
                isOverArea = true;
                break;
              }
            }

            // 只有当悬停状态发生变化时才重绘
            if (_this.hoveredAreaIndex !== hoveredIndex) {
              _this.hoveredAreaIndex = hoveredIndex;
              _this.redrawWithHighlight();
              document.body.style.cursor = isOverArea ? 'pointer' : 'default';
            }
          }
        }, 10); // 10毫秒的防抖延迟，平衡响应性和性能
      });

      // 添加鼠标离开画布事件
      this.canvas.addEventListener('mouseleave', function () {
        if (_this.debounceTimer) {
          clearTimeout(_this.debounceTimer);
        }

        if (_this.hoveredAreaIndex !== -1) {
          _this.hoveredAreaIndex = -1;
          _this.redrawCanvas();
          document.body.style.cursor = 'default';
        }
      });
    },
    // 判断点是否在多边形内（原有方法，保留但不使用）
    judge(dot, coordinates) {
      return this.isPointInPolygon(dot, coordinates);
    },
    // 绘制线条
    createL(coords, status) {
      let _this = this;
      if (status == 'begin') {
      // 点击开始绘制前，检查点击位置是否在已有区域内
        const mousePoint = {x: coords.x, y: coords.y};
        for (let i = 0; i < this.finalArr.length; i++) {
          if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {
            this.$message.warning('您点击的位置已在其他区域内，请选择其他位置绘制');
            return; // 阻止继续执行
          }
        }

        if (_this.dataArr && _this.dataArr.length === 0) {
          _this.dataArr.push({x: coords.x, y: coords.y});
          _this.ctx.beginPath();
          _this.ctx.moveTo(coords.x, coords.y);
          _this.ctx.lineTo(coords.x + 1, coords.y + 1);
          _this.ctx.strokeStyle = _this.strokeStyle;
          _this.ctx.lineWidth = _this.lineWidth;
          _this.ctx.stroke();
        } else if (_this.dataArr && _this.dataArr.length !== 0) {
          _this.dataArr.push({x: coords.x, y: coords.y});
          _this.ctx.lineTo(coords.x, coords.y);
          _this.ctx.strokeStyle = _this.strokeStyle;
          _this.ctx.lineWidth = _this.lineWidth;
          _this.ctx.stroke();
        }
      } else if (status == 'end') {
        if (_this.dataArr && _this.dataArr.length !== 0) {
          _this.ctx.moveTo(
            _this.dataArr[_this.dataArr.length - 1].x,
            _this.dataArr[_this.dataArr.length - 1].y
          );
          _this.ctx.lineTo(_this.dataArr[0].x, _this.dataArr[0].y);
          _this.ctx.stroke();
          _this.ctx.closePath();
          _this.step = _this.step + 1;
          if (_this.step < _this.canvasHistory.length - 1) {
            _this.canvasHistory.length = _this.step;
          }
          _this.canvasHistory.push(_this.canvas.toDataURL());
          if (_this.dataArr && _this.dataArr.length < 3) {
            _this.$message.info('该区域点数少于三个，不保存');
            _this._repeal();
          } else {
          // 检查新绘制的区域是否与现有区域有重叠
            const newArea = _this.dataArr;
            let hasOverlap = false;

            for (let i = 0; i < _this.finalArr.length; i++) {
              if (_this.checkPolygonsOverlap(newArea, _this.finalArr[i])) {
                hasOverlap = true;
                break;
              }
            }

            if (hasOverlap) {
              _this.$message.warning('新绘制的区域与现有区域重叠，请重新绘制');
              _this._repeal();
            } else {
            // 存储新绘制的区域数据，等待表单提交后再保存
              _this.tempAreaData = {
                area: _this.dataArr,
                relativeArea: _this.dataArr.map(point => _this.toRelativeCoordinates(point))
              };

              // 打开区域信息表单
              _this.areaForm = {
                roomName: `区域${_this.roomNameList.length + 1}`,
                roomType: '',
                remark: ''
              };
              _this.areaFormVisible = true;
            }
          }
          this.$emit('getPointArr', _this.finalArr);
          _this.dataArr = [];
          _this.canvas.onmousemove = null;
        }
      }
    },
    // 重绘画布并高亮显示悬停区域
    redrawWithHighlight() {
    // 清空画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // 重绘背景图
      this.ctx.drawImage(this.img, 0, 0);

      // 重绘所有区域
      for (let i = 0; i < this.finalArr.length; i++) {
        this.drawArea(this.finalArr[i], i === this.hoveredAreaIndex);
      }
    },

    // 统一的区域绘制函数
    drawArea(area, isHighlighted = false) {
      if (!area || area.length < 3) {
        return;
      }

      this.ctx.beginPath();
      this.ctx.moveTo(area[0].x, area[0].y);

      // 绘制区域轮廓
      for (let i = 1; i < area.length; i++) {
        this.ctx.lineTo(area[i].x, area[i].y);
      }

      // 闭合路径
      this.ctx.lineTo(area[0].x, area[0].y);

      // 设置样式
      if (isHighlighted) {
      // 高亮显示 - 增加线宽，使用更深的蓝色
        this.ctx.strokeStyle = this.highlightColor;
        this.ctx.lineWidth = this.lineWidth + 2;

        // 填充半透明颜色
        this.ctx.fillStyle = this.highlightColor + '22'; // 添加22作为透明度 (13%)
        this.ctx.fill();
      } else {
        this.ctx.strokeStyle = this.strokeStyle;
        this.ctx.lineWidth = this.lineWidth;
      }

      this.ctx.stroke();
      this.ctx.closePath();

      // 如果启用了显示区域名称，并且能找到对应的名
      const areaIndex = this.finalArr.indexOf(area);
      if (this.showAreaNames && areaIndex >= 0 && this.roomNameList[areaIndex]) {
        this.drawAreaName(area, this.roomNameList[areaIndex].roomName);
      }
    },

    // 绘制区域名称
    drawAreaName(area, name) {
      if (!area || area.length < 3 || !name) {
        return;
      }

      // 计算区域的中心点
      let centerX = 0, centerY = 0;
      for (let i = 0; i < area.length; i++) {
        centerX += area[i].x;
        centerY += area[i].y;
      }
      centerX /= area.length;
      centerY /= area.length;

      // 保存当前上下文状态
      this.ctx.save();

      // 设置固定的字体大小，不受图片尺寸和缩放影响
      // 首先获取画布的尺寸
      const canvasWidth = this.canvas.width;
      const canvasHeight = this.canvas.height;

      // 计算固定的字体大小，基于画布尺寸的一个合适比例
      // 这样可以确保在不同大小的图片上，字体大小相对于图片尺寸的比例是一致的
      const baseFontSize = 36; // 固定基础字体大小

      // 应用字体设置
      this.ctx.font = `bold ${baseFontSize}px ${this.fontFamily}`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';

      // 测量文本宽度
      const textWidth = this.ctx.measureText(name).width;

      // 增强文字阴影以提高可读性
      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';
      this.ctx.shadowBlur = 4;
      this.ctx.shadowOffsetX = 1;
      this.ctx.shadowOffsetY = 1;

      // 使用更亮的蓝色，但与线条颜色(#1E90FF)有区别
      this.ctx.fillStyle = '#38B0DE'; // 天蓝色，比线条颜色略微偏青一些
      this.ctx.fillText(name, centerX, centerY);

      // 恢复上下文状态
      this.ctx.restore();
    },

    // 绘制圆角矩形
    roundRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {
      if (typeof radius === 'undefined') {
        radius = 5;
      }
      if (typeof radius === 'number') {
        radius = {tl: radius, tr: radius, br: radius, bl: radius};
      } else {
        const defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
        for (const side in defaultRadius) {
          radius[side] = radius[side] || defaultRadius[side];
        }
      }

      ctx.beginPath();
      ctx.moveTo(x + radius.tl, y);
      ctx.lineTo(x + width - radius.tr, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
      ctx.lineTo(x + width, y + height - radius.br);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
      ctx.lineTo(x + radius.bl, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
      ctx.lineTo(x, y + radius.tl);
      ctx.quadraticCurveTo(x, y, x + radius.tl, y);
      ctx.closePath();

      if (fillColor) {
        ctx.fillStyle = fillColor;
        ctx.fill();
      }

      if (strokeColor) {
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = 1;
        ctx.stroke();
      }
    },

    // 重绘画布
    redrawCanvas() {
      if (!this.canvas || !this.ctx) {
        return;
      }

      // 清空画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // 绘制背景图
      this.ctx.drawImage(this.img, 0, 0);

      // 重绘所有保留的区域
      for (let i = 0; i < this.finalArr.length; i++) {
        this.drawArea(this.finalArr[i]);
      }
    },

    // 创建区域2（读取已有区域）
    createL2(e) {
      let _this = this;

      // 使用统一的绘制函数
      this.drawArea(e);

      _this.step = _this.step + 1;
      if (_this.step < _this.canvasHistory.length - 1) {
        _this.canvasHistory.length = _this.step;
      }
      _this.canvasHistory.push(_this.canvas.toDataURL());
      this.$emit('getPointArr', _this.finalArr);
    },
    // 绘制矩形
    createR(e, status, r1, r2) {
      let _this = this;
      let r;
      if (status == 'begin') {
      // console.log('onmousemove')
        _this.canvas.onmousemove = function (e) {
          _this.reset();
          let rx = e.layerX - r1;
          let ry = e.layerY - r2;

          // 保留之前绘画的图形
          if (_this.step !== 0) {
            let canvasPic = new Image();
            canvasPic.src = _this.canvasHistory[_this.step];
            _this.ctx.drawImage(canvasPic, 0, 0);
          }

          _this.ctx.beginPath();
          _this.ctx.strokeRect(r1, r2, rx, ry);
          _this.ctx.strokeStyle = _this.strokeStyle;
          _this.ctx.lineWidth = _this.lineWidth;
          _this.ctx.closePath();
          _this.ctx.stroke();
        };
      } else if (status == 'end') {
        _this.rebroadcast();
        let interval = setInterval(() => {
          if (_this.loading) {
            clearInterval(interval);
            _this.loading = false;
          } else {
            return;
          }
          let rx = e.layerX - r1;
          let ry = e.layerY - r2;
          _this.ctx.beginPath();
          _this.ctx.rect(r1, r2, rx, ry);
          _this.ctx.strokeStyle = _this.strokeStyle;
          _this.ctx.lineWidth = _this.lineWidth;
          _this.ctx.closePath();
          _this.ctx.stroke();
          _this.step = _this.step + 1;
          if (_this.step < _this.canvasHistory.length - 1) {
            _this.canvasHistory.length = _this.step; // 截断数组
          }
          _this.canvasHistory.push(_this.canvas.toDataURL());
          _this.canvas.onmousemove = null;
        }, 1);
      }
    },

    // 绘制箭头
    drawArrow(e, status) {
      let _this = this;
      if (status == 'begin') {
      // 获取起始位置
        _this.arrowFromX = e.layerX;
        _this.arrowFromY = e.layerY;
        _this.ctx.beginPath();
        _this.ctx.moveTo(e.layerX, e.layerY);
      } else if (status == 'end') {
      // 计算箭头及画线
        let toX = e.layerX;
        let toY = e.layerY;
        let theta = 30;
        let headlen = 10;
        let _this = this;
        let fromX = this.arrowFromX;
        let fromY = this.arrowFromY;
        // 计算各角度和对应的P2,P3坐标
        let angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;
        let angle1 = ((angle + theta) * Math.PI) / 180;
        let angle2 = ((angle - theta) * Math.PI) / 180;
        let topX = headlen * Math.cos(angle1);
        let topY = headlen * Math.sin(angle1);
        let botX = headlen * Math.cos(angle2);
        let botY = headlen * Math.sin(angle2);
        let arrowX = fromX - topX;
        let arrowY = fromY - topY;
        _this.ctx.moveTo(arrowX, arrowY);
        _this.ctx.moveTo(fromX, fromY);
        _this.ctx.lineTo(toX, toY);
        arrowX = toX + topX;
        arrowY = toY + topY;
        _this.ctx.moveTo(arrowX, arrowY);
        _this.ctx.lineTo(toX, toY);
        arrowX = toX + botX;
        arrowY = toY + botY;
        _this.ctx.lineTo(arrowX, arrowY);
        _this.ctx.strokeStyle = _this.strokeStyle;
        _this.ctx.lineWidth = _this.lineWidth;
        _this.ctx.stroke();

        _this.ctx.closePath();
        _this.step = _this.step + 1;
        if (_this.step < _this.canvasHistory.length - 1) {
          _this.canvasHistory.length = _this.step; // 截断数组
        }
        _this.canvasHistory.push(_this.canvas.toDataURL());
        _this.canvas.onmousemove = null;
      }
    },

    // 文字输入
    createT(e, status) {
      let _this = this;
      if (status == 'begin') {
      // 初始化文字输入相关参数
        _this.isTextInputMode = true;
      } else if (status == 'end') {
        let offset = 0;
        if (_this.fontSize >= 28) {
          offset = _this.fontSize / 2 - 3;
        } else {
          offset = _this.fontSize / 2 - 2;
        }

        _this.ctxX = e.layerX + 2;
        _this.ctxY = e.layerY + offset;

        let index = this.getPointOnCanvas(e);
        _this.$refs.txt.style.left = index.x + 'px';
        _this.$refs.txt.style.top = index.y - _this.fontSize / 2 + 'px';
        _this.$refs.txt.value = '';
        _this.$refs.txt.style.height = _this.fontSize + 'px';
        (_this.$refs.txt.style.width
          = _this.canvas.width - e.layerX - 1 + 'px'),
        (_this.$refs.txt.style.fontSize = _this.fontSize + 'px');
        _this.$refs.txt.style.fontFamily = _this.fontFamily;
        _this.$refs.txt.style.color = _this.fontColor;
        _this.$refs.txt.style.maxlength = Math.floor(
          (_this.canvas.width - e.layerX) / _this.fontSize
        );
        _this.isShow = true;
        setTimeout(() => {
          _this.$refs.txt.focus();
        });
      }
    },
    // 文字输入框失去光标时在画布上生成文字
    txtBlue() {
      let _this = this;
      let txt = _this.$refs.txt.value;
      if (txt) {
        _this.ctx.font
          = _this.$refs.txt.style.fontSize
          + ' '
          + _this.$refs.txt.style.fontFamily;
        _this.ctx.fillStyle = _this.$refs.txt.style.color;
        _this.ctx.fillText(txt, _this.ctxX, _this.ctxY);
        _this.step = _this.step + 1;
        if (_this.step < _this.canvasHistory.length - 1) {
          _this.canvasHistory.length = _this.step; // 截断数组
        }
        _this.canvasHistory.push(_this.canvas.toDataURL());
        _this.canvas.onmousemove = null;
      }
    },
    // 计算文字框定位位置
    getPointOnCanvas(e) {
      let cs = this.canvas;
      let content = document.getElementsByClassName('content')[0];
      return {
        x: e.layerX + (content.clientWidth - cs.width) / 2,
        y: e.layerY,
      };
    },
    // 清空文字
    resetTxt() {
      let _this = this;
      _this.$refs.txt.value = '';
      _this.isShow = false;
    },
    exportJson() {
      // 直接使用已经准备好的相对坐标数组
      let exportArr = JSON.parse(JSON.stringify(this.finalArrRelative));

      // 获取URL中的drawingId参数
      const drawingId = this.currentDrawingId || this.$route.query.drawingId;

      // 准备导出数据，使用正确的字段名
      let maintenanceRegionList = this.roomNameList.map((room, index) => {
        return {
          id: room.id || null,
          regionName: room.roomName, // 使用regionName作为区域名称字段
          pointJson: JSON.stringify(exportArr[index]), // 使用pointJson作为区域坐标JSON字段
          remark: room.remark || '', // 备注字段
          drawingId: drawingId // 从URL参数获取的图纸ID
        };
      });

      // 如果没有区域数据，提示用户
      if (maintenanceRegionList.length === 0) {
        this.$message.warning('尚未绘制任何区域，无法保存');
        return;
      }

      // 二次确认保存操作
      this.$confirm('确认保存当前绘制的所有区域？', '保存确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        // 用户确认，调用保存接口
        console.log('保存的区域数据:', maintenanceRegionList);

        // 调用保存接口，传入整合后的区域列表
        this.$api['areaManagement/maintenanceRegion-save']({
          maintenanceRegionList: maintenanceRegionList
        }).then(data => {
          this.$message.success('保存成功');
        }).catch(error => {
          this.$message.error('保存失败，请重试');
        });
      });
    },
    ToBase64() {
      let that = this;
      var img = document.getElementById('imgfile');
      var imgFile = new FileReader();
      imgFile.readAsDataURL(img.files[0]);

      imgFile.onload = function () {
        var imgData = this.result; // base64数据
        that.imgPath = imgData;
        that.init();
      };
    },
    handleMultiUpload(v) {
      this.imgUrl = v[0];
    },
    handleMultiUpload2(v) {
      this.imgForm.imgUrl = v[0];
    },
    changeImgUrl() {
      this.imgForm = {
        floorNo: '',
        imgUrl: ''
      };
      this.imgModal = true;
    },
    imgOk() {
      this.$refs.imgForm.validate(valid => {
        if (valid) {
          this.imgOkLoading = true;
          this.$api['iotHome/changeFloorMap'](this.imgForm)
            .then(data => {
              this.$message.success('更换成功');
              this.imgModal = false;
            })
            .finally(() => {
              this.imgOkLoading = false;
            });
        }
      });
    },
    imgCancel() {
    // 取消关闭
      this.imgModal = false;
      this.$refs.imgForm.resetFields();
    },
    // 将绝对坐标转换为相对坐标（0-1之间的比例值）
    toRelativeCoordinates(point) {
      if (!this.canvas) {
        return point;
      }
      return {
        x: parseFloat((point.x / this.canvas.width).toFixed(4)),
        y: parseFloat((point.y / this.canvas.height).toFixed(4))
      };
    },

    // 将相对坐标转换为绝对坐标（实际像素值）
    toAbsoluteCoordinates(point) {
      if (!this.canvas) {
        return point;
      }
      return {
        x: Math.round(point.x * this.canvas.width),
        y: Math.round(point.y * this.canvas.height)
      };
    },

    // 判断一个点是否为相对坐标（值在0-1之间）
    isRelativeCoordinate(point) {
      return point.x >= 0 && point.x <= 1 && point.y >= 0 && point.y <= 1;
    },

    // 确保点使用绝对坐标
    ensureAbsoluteCoordinate(point) {
      if (this.isRelativeCoordinate(point)) {
        return this.toAbsoluteCoordinates(point);
      }
      return point;
    },

    // 确保点使用相对坐标
    ensureRelativeCoordinate(point) {
      if (!this.isRelativeCoordinate(point)) {
        return this.toRelativeCoordinates(point);
      }
      return point;
    },

    // 处理输入的区域数据，确保使用正确的坐标类型
    processAreaData(areaData) {
      if (!areaData || !areaData.length) {
        return [];
      }

      // 深拷贝避免修改原始数据
      const processedData = JSON.parse(JSON.stringify(areaData));

      // 检查第一个点的第一个坐标，判断是否为相对坐标
      const firstArea = processedData[0];
      if (firstArea && firstArea.length > 0) {
        const firstPoint = firstArea[0];
        const isRelative = this.isRelativeCoordinate(firstPoint);

        // 如果是相对坐标，转换为绝对坐标用于绘制
        if (isRelative) {
        // 同时保存相对坐标版本
          this.finalArrRelative = JSON.parse(JSON.stringify(processedData));

          processedData.forEach(area => {
            area.forEach((point, index) => {
              area[index] = this.toAbsoluteCoordinates(point);
            });
          });
        } else {
        // 如果是绝对坐标，生成相对坐标版本
          this.finalArrRelative = processedData.map(area =>
            area.map(point => this.toRelativeCoordinates(point))
          );
        }
      }

      return processedData;
    },
    deleteAllAreas() {
      if (this.finalArr.length > 0) {
        // 二次确认删除所有区域操作，强调可能影响已绑定设备
        this.$confirm('警告：删除所有区域可能会影响已绑定的设备！确定要删除吗？', '批量删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: `<div>
            <p><strong style="color: red;">严重警告：</strong>删除所有区域将会：</p>
            <ul>
              <li>永久移除所有区域的绘制信息</li>
              <li>导致所有与这些区域关联的设备失去位置信息</li>
              <li>需要重新绘制区域并重新绑定所有设备</li>
            </ul>
            <p>此操作不可恢复，确定要继续删除所有区域吗？</p>
          </div>`
        }).then(() => {
          // 收集所有区域的ID
          const regionIds = this.roomNameList
            .filter(room => room.id) // 只收集有ID的区域
            .map(room => room.id);

          if (regionIds.length > 0) {
            // 有保存过的区域，调用API删除
            this.$api['areaManagement/maintenanceRegion-batch-delete']({
              ids: regionIds
            }).then(data => {
              this.$message.success('已删除所有区域');

              // 清空前端数据
              this.finalArr = [];
              this.finalArrRelative = [];
              this.roomNameList = [];

              // 重绘画布
              this.redrawCanvas();

              // 通知父组件区域变化
              this.$emit('getPointArr', this.finalArr);
            }).catch(error => {
              this.$message.error('删除失败，请重试');
              console.error('批量删除区域失败:', error);
            });
          } else {
            // 没有保存过的区域，直接清空前端数据
            this.finalArr = [];
            this.finalArrRelative = [];
            this.roomNameList = [];

            // 重绘画布
            this.redrawCanvas();

            // 通知父组件区域变化
            this.$emit('getPointArr', this.finalArr);

            this.$message.success('已删除所有区域');
          }
        });
      } else {
        this.$message.info('没有可删除的区域');
      }
    },
    // 检查并删除点击的区域
    checkAndDeleteArea(coords) {
      const mousePoint = {x: coords.x, y: coords.y};

      // 检查点击是否在任何一个区域内
      for (let i = 0; i < this.finalArr.length; i++) {
        if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {
        // 弹出确认对话框
          this.$confirm(`确定要删除第${i + 1}个区域吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
          // 删除该区域
            this.finalArr.splice(i, 1);
            this.finalArrRelative.splice(i, 1);

            // 如果有对应的名称，也要删除
            if (this.roomNameList.length > i) {
              this.roomNameList.splice(i, 1);
            }

            // 重绘画布
            this.hoveredAreaIndex = -1;
            this.redrawCanvas();

            // 通知父组件区域变化
            this.$emit('getPointArr', this.finalArr);

            this.$message.success(`已删除第${i + 1}个区域`);
          });
          break;
        }
      }
    },

    // 判断点是否在多边形内（射线法）
    isPointInPolygon(point, polygon) {
      if (!polygon || polygon.length < 3) {
        return false;
      }

      let inside = false;
      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = polygon[i].x, yi = polygon[i].y;
        const xj = polygon[j].x, yj = polygon[j].y;

        const intersect = ((yi > point.y) !== (yj > point.y))
        && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);

        if (intersect) {
          inside = !inside;
        }
      }

      return inside;
    },


    // 通过表格行点击高亮并选择区域
    highlightArea(row, column, event) {
      const index = this.roomNameList.indexOf(row);
      if (index >= 0 && index < this.finalArr.length) {
        this.hoveredAreaIndex = index;
        this.redrawWithHighlight();
      }
    },

    // 通过表格行悬停高亮区域
    mouseoverArea(row, column, event) {
      const index = this.roomNameList.indexOf(row);
      if (index >= 0 && index < this.finalArr.length) {
        this.lastHoveredAreaIndex = this.hoveredAreaIndex;
        this.hoveredAreaIndex = index;
        this.redrawWithHighlight();
      }
    },

    // 表格行鼠标移出恢复之前的高亮状态
    mouseoutArea(row, column, event) {
      this.hoveredAreaIndex = this.lastHoveredAreaIndex;
      this.redrawCanvas();
    },

    // 通过索引删除区域
    deleteAreaByIndex(index) {
      if (index >= 0 && index < this.finalArr.length) {
        // 二次确认删除操作，强调可能影响已绑定设备
        this.$confirm(`警告：删除"${this.roomNameList[index].roomName}"区域可能会影响已绑定的设备！确定要删除吗？`, '删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: `<div>
            <p><strong style="color: red;">警告：</strong>删除"${this.roomNameList[index].roomName}"区域将会：</p>
            <ul>
              <li>永久移除该区域的绘制信息</li>
              <li>可能导致与该区域关联的设备失去位置信息</li>
              <li>需要重新绑定相关设备到其他区域</li>
            </ul>
            <p>确定要继续删除操作吗？</p>
          </div>`
        }).then(() => {
          // 获取要删除的区域ID
          const regionId = this.roomNameList[index].id;

          // 如果有ID，调用API删除
          if (regionId) {
            this.$api['areaManagement/maintenanceRegion-batch-delete']({
              ids: [regionId]
            }).then(data => {
              this.$message.success(`已删除区域`);

              // 删除前端数据
              this.finalArr.splice(index, 1);
              this.finalArrRelative.splice(index, 1);
              this.roomNameList.splice(index, 1);

              // 重绘画布
              this.hoveredAreaIndex = -1;
              this.redrawCanvas();

              // 通知父组件区域变化
              this.$emit('getPointArr', this.finalArr);
            });
          } else {
            // 新绘制的区域没有ID，直接从前端删除
            this.finalArr.splice(index, 1);
            this.finalArrRelative.splice(index, 1);
            this.roomNameList.splice(index, 1);

            // 重绘画布
            this.hoveredAreaIndex = -1;
            this.redrawCanvas();

            // 通知父组件区域变化
            this.$emit('getPointArr', this.finalArr);

            this.$message.success(`已删除区域`);
          }
        });
      }
    },

    // 检查两个多边形是否重叠
    checkPolygonsOverlap(poly1, poly2) {
    // 简单实现：检查每个多边形的点是否在另一个多边形内
    // 或者检查多边形的边是否相交

      // 检查poly1的点是否在poly2内
      for (let i = 0; i < poly1.length; i++) {
        if (this.isPointInPolygon(poly1[i], poly2)) {
          return true;
        }
      }

      // 检查poly2的点是否在poly1内
      for (let i = 0; i < poly2.length; i++) {
        if (this.isPointInPolygon(poly2[i], poly1)) {
          return true;
        }
      }

      // 检查边是否相交
      for (let i = 0; i < poly1.length; i++) {
        const a1 = poly1[i];
        const a2 = poly1[(i + 1) % poly1.length];

        for (let j = 0; j < poly2.length; j++) {
          const b1 = poly2[j];
          const b2 = poly2[(j + 1) % poly2.length];

          if (this.doLinesIntersect(a1, a2, b1, b2)) {
            return true;
          }
        }
      }

      return false;
    },

    // 检查两条线段是否相交
    doLinesIntersect(p1, p2, p3, p4) {
    // 线段1的方向
      const d1x = p2.x - p1.x;
      const d1y = p2.y - p1.y;

      // 线段2的方向
      const d2x = p4.x - p3.x;
      const d2y = p4.y - p3.y;

      // 行列式
      const denominator = d2y * d1x - d2x * d1y;

      // 如果行列式为0，则线段平行或共线
      if (denominator === 0) {
        return false;
      }

      // 参数t和u
      const u_a = (d2x * (p1.y - p3.y) - d2y * (p1.x - p3.x)) / denominator;
      const u_b = (d1x * (p1.y - p3.y) - d1y * (p1.x - p3.x)) / denominator;

      // 如果t和u都在[0,1]范围内，则线段相交
      return (u_a >= 0 && u_a <= 1 && u_b >= 0 && u_b <= 1);
    },

    // 放大画布
    zoomIn() {
      this.zoomLevel *= 1.1;
      this.applyZoom();
    },

    // 缩小画布
    zoomOut() {
      this.zoomLevel *= 0.9;
      this.applyZoom();
    },

    // 重置缩放
    resetZoom() {
      this.zoomLevel = this.initialZoomLevel || 1;
      this.applyZoom();
    },

    // 应用缩放
    applyZoom() {
      if (!this.canvas) {
        return;
      }

      // 应用缩放变换
      this.canvas.style.transform = `scale(${this.zoomLevel})`;
      this.canvas.style.transformOrigin = 'top left';

      // 重绘以更新区域名称大小
      this.redrawCanvas();

      // 检查是否需要滚动按钮
      this.checkScrollNeeded();

    // 不再需要调整容器高度
    // this.adjustContainerHeight();
    },

    // 自适应屏幕
    autoFit() {
      if (!this.canvas || !this.$refs.canvasContainer) {
        return;
      }

      const container = this.$refs.canvasContainer;
      const containerWidth = container.clientWidth;
      const containerHeight = window.innerHeight - 100; // 减去头部和边距

      const imgRatio = this.canvas.width / this.canvas.height;
      const containerRatio = containerWidth / containerHeight;

      let newZoomLevel;

      if (imgRatio > containerRatio) {
      // 宽度适配
        newZoomLevel = containerWidth / this.canvas.width;
      } else {
      // 高度适配
        newZoomLevel = containerHeight / this.canvas.height;
      }

      // 确保缩放系数不会过小
      newZoomLevel = Math.max(newZoomLevel, 0.1);

      // 应用默认缩放系数，使图片显示更大
      // 限制最大缩放为2倍，避免图片过大导致性能问题
      newZoomLevel = Math.min(newZoomLevel * this.defaultZoomFactor, 2.5);

      this.zoomLevel = newZoomLevel;
      this.initialZoomLevel = newZoomLevel;
      this.applyZoom();

      // 确保画布在容器中居中
      this.$nextTick(() => {
        const canvasContainer = this.$refs.canvasContainer;
        if (canvasContainer) {
        // 如果画布宽度小于容器宽度，添加水平居中样式
          if (this.canvas.offsetWidth * this.zoomLevel < canvasContainer.offsetWidth) {
            this.canvas.style.marginLeft = 'auto';
            this.canvas.style.marginRight = 'auto';
            this.canvas.style.display = 'block';
          }
        }
      });

      // this.$message.success('已自动调整图片大小');
    },

    // 更新事件坐标计算，考虑缩放因素
    getAdjustedCoordinates(e) {
      if (!this.canvas) {
        return {x: e.layerX, y: e.layerY};
      }

      // 考虑缩放因素
      const rect = this.canvas.getBoundingClientRect();
      const scaleX = this.canvas.width / rect.width;
      const scaleY = this.canvas.height / rect.height;

      // 相对于画布的坐标
      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      return {x, y};
    },

    // 切换显示区域名称
    toggleAreaNames() {
      this.showAreaNames = !this.showAreaNames;
      this.redrawCanvas();
      this.$message.success(this.showAreaNames ? '已显示区域名称' : '已隐藏区域名称');
    },

    // 处理默认缩放变化
    handleDefaultZoomChange(command) {
      const newFactor = parseFloat(command);
      if (!isNaN(newFactor)) {
        this.defaultZoomFactor = newFactor;
        this.$message.success(`默认缩放已设置为${Math.round(newFactor * 100)}%`);
        this.autoFit(); // 立即应用新的缩放设置
      }
    },

    // 处理区域表单提交
    handleAreaFormSubmit() {
      this.$refs.areaForm.validate(valid => {
        if (valid && this.tempAreaData) {
          this.areaFormLoading = true;

          // 将区域数据添加到相应的数组
          this.finalArr.push(this.tempAreaData.area);
          this.finalArrRelative.push(this.tempAreaData.relativeArea);

          // 添加信息
          this.roomNameList.push({
            roomName: this.areaForm.roomName,
            roomType: this.areaForm.roomType,
            remark: this.areaForm.remark
          });

          // 清除临时数据
          this.tempAreaData = null;

          // 关闭表单对话框
          setTimeout(() => {
            this.areaFormLoading = false;
            this.areaFormVisible = false;
            this.$message.success('区域信息已保存');
            // 重绘画布以显示新添加的区域名称
            this.redrawCanvas();
          }, 300);
        }
      });
    },

    // 处理区域表单取消
    handleAreaFormCancel() {
      if (this.tempAreaData) {
      // 如果取消表单，需要撤销绘制的区域
        this._repeal();
        this.tempAreaData = null;
      }
      this.areaFormVisible = false;
    },
  },
};
</script>

<style lang="scss" scope>
* {
  box-sizing: border-box;
}

.draw {
  min-width: 420px;
  overflow-y: hidden;
}

.content {
  flex-grow: 1;
  width: 100%;
  display: block; /* 修改为block，避免居中导致的偏移 */
}

.canvas-container {
  width: 75%;
  position: relative;
  overflow: auto; /* 保持滚动功能 */
  border: 1px solid #ebeef5;
  height: 600px; /* 固定高度 */
}

/* 滚动控制按钮样式 */
.scroll-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.drawTop {
  display: flex;
  flex-wrap: wrap;
  padding: 5px;
}

.drawTop > div {
  display: flex;
  padding: 5px 5px;
}

div.drawTopContrllor {
  // display: none;
}

@media screen and (max-width: 1200px) {
  .drawTop {
    position: absolute;
    background-color: white;
    width: 100%;
    overflow: hidden;
  }

  .drawTopContrllor {
    display: flex !important;
    width: 100%;
    padding: 0 !important;
  }
}
</style>
