export default [
  {
    name: 'dashboardLeaderDuty-page',
    method: 'POST',
    path: '/syn/dashboardLeaderDuty/sd-page',
    desc: '首都领导值班记录表分页'
  },


  {
    name: 'dashboardLeaderDuty-dx-page',
    method: 'POST',
    path: '/syn/dashboardLeaderDuty/dx-page',
    desc: '大兴领导值班记录表分页'
  },




  {
    name: 'dashboardLeaderDuty-get',
    method: 'OTHERGET',
    path: '/syn/dashboardLeaderDuty/get',
    desc: '查询领导值班记录表详情'
  },

  {
    name: 'dashboardLeaderDutyDetail-page',
    method: 'POST',
    path: '/syn/dashboardLeaderDutyDetail/page',
    desc: '查询首都机场领导值班详情分页'
  },
  {
    name: 'dashboardLeaderDutyDetail-save',
    method: 'POST',
    path: '/syn/dashboardLeaderDutyDetail/save',
    desc: '保存首都机场领导值班详情'
  },

  /** *
   * 昨日行李量管理
   */

  {
    name: 'dashboardYesterdayLuggage-page',
    method: 'POST',
    path: '/syn/dashboardYesterdayLuggage/sd-page',
    desc: '查询首都昨日行李量管理分页'
  },

  {
    name: 'dashboardYesterdayLuggage-dx-page',
    method: 'POST',
    path: '/syn/dashboardYesterdayLuggage/dx-page',
    desc: '查询大兴昨日行李量管理分页'
  },

  {
    name: 'dashboardYesterdayLuggage-get',
    method: 'OTHERGET',
    path: '/syn/dashboardYesterdayLuggage/get',
    desc: '查询昨日行李量管理详情'
  },

  {
    name: 'dashboardYesterdayLuggage-save',
    method: 'POST',
    path: '/syn/dashboardYesterdayLuggage/save',
    desc: '保存昨日行李量管理'
  },



  /** *
   * 首都行李数据管理
   */

  {
    name: 'dashboardLuggageThreeRate-sd-page',
    method: 'POST',
    path: '/syn/dashboardLuggageThreeRate/sd-page',
    desc: '查询首都行李三率分页'
  },

  {
    name: 'dashboardLuggageThreeRate-dx-page',
    method: 'POST',
    path: '/syn/dashboardLuggageThreeRate/dx-page',
    desc: '查询大兴行李三率分页'
  },

  {
    name: 'dashboardLuggageThreeRate-get',
    method: 'OTHERGET',
    path: '/syn/dashboardLuggageThreeRate/get',
    desc: '查询行李三率详情'
  },

  {
    name: 'dashboardLuggageThreeRate-save',
    method: 'POST',
    path: '/syn/dashboardLuggageThreeRate/save',
    desc: '保存行李三率'
  },


  /** *
   * 航班及旅客管理
   */

  {
    name: 'dashboardPassengerFlight-sd-page',
    method: 'POST',
    path: '/syn/dashboardPassengerFlight/sd-page',
    desc: '查询首都昨日旅客航班数分页'
  },

  {
    name: 'dashboardPassengerFlight-dx-page',
    method: 'POST',
    path: '/syn/dashboardPassengerFlight/dx-page',
    desc: '查询大兴昨日旅客航班数分页'
  },

  {
    name: 'dashboardPassengerFlight-get',
    method: 'OTHERGET',
    path: '/syn/dashboardPassengerFlight/get',
    desc: '查询昨日旅客航班数详情'
  },

  {
    name: 'dashboardPassengerFlight-save',
    method: 'POST',
    path: '/syn/dashboardPassengerFlight/save',
    desc: '保存昨日旅客航班数'
  },


  /** *
     * 行驶历程管理
     */

  {
    name: 'dashboardYesterdayMileage-page',
    method: 'POST',
    path: '/syn/dashboardYesterdayMileage/page',
    desc: '查询昨日行驶里程分页'
  },

  {
    name: 'dashboardYesterdayMileage-get',
    method: 'OTHERGET',
    path: '/syn/dashboardYesterdayMileage/get',
    desc: '查询昨日行驶里程详情'
  },

  {
    name: 'dashboardYesterdayMileage-save',
    method: 'POST',
    path: '/syn/dashboardYesterdayMileage/save',
    desc: '保存昨日行驶里程'
  },

  /**
     * 首都捷运数据管理
     */


  {
    name: 'dashboardTransportPassenger-page',
    method: 'POST',
    path: '/syn/dashboardTransportPassenger/page',
    desc: '查询运送旅客数分页'
  },

  {
    name: 'dashboardTransportPassenger-get',
    method: 'OTHERGET',
    path: '/syn/dashboardTransportPassenger/get',
    desc: '查询运送旅客数详情'
  },

  {
    name: 'dashboardTransportPassenger-save',
    method: 'POST',
    path: '/syn/dashboardTransportPassenger/save',
    desc: '保存运送旅客数'
  },

  /**
     * APU接驳管理
     */

  {
    name: 'dashboardApuConnection-page',
    method: 'POST',
    path: '/syn/dashboardApuConnection/page',
    desc: '查询首都机场APU接驳分页'
  },
  {
    name: 'dashboardApuConnection-save',
    method: 'POST',
    path: '/syn/dashboardApuConnection/save',
    desc: '保存首都机场APU接驳'
  },

  {
    name: 'dashboardApuConnection-dx-page',
    method: 'POST',
    path: '/syn/customerBridgeManagement/page',
    desc: '查询大兴机场APU接驳分页'
  },

  {
    name: 'dashboardApuConnection-dx-save',
    method: 'POST',
    path: '/syn/customerBridgeManagement/save',
    desc: '保存大兴机场APU接驳'
  },

];

