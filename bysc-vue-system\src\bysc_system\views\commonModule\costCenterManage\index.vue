<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="systems/costCenter-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="财务部门编码">
                <el-input
                  v-model.trim="searchForm.departNo"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="财务部门名称">
                <el-input
                  v-model.trim="searchForm.departName"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="人员结算方式">
                <el-select
                  v-model="searchForm.settlement"
                  placeholder="请选择"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in settlementMethod"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  v-model="searchForm.isEnable"
                  placeholder="请选择"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in statusList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="人员模块">
                <el-input
                  v-model.trim="searchForm.moduleName"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button
              v-permission="'costCenterManage_sync'"
              size="small"
              type="primary"
              @click="handleSync"
              :loading="okLoading"
              >同步</el-button
            >
          </div>
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="tableData"
            stripe
            ref="table"
            style="width: 100%"
          >
            <el-table-column
              v-if="isChoosePage"
              width="50"
              label="选择"
              align="center"
            >
              <template slot-scope="scope">
                <el-radio v-model="radio" :label="scope.row.id"> </el-radio>
              </template>
            </el-table-column>
            <el-table-column
              fixed="left"
              label="序号"
              type="index"
              width="50"
            ></el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.key == 'isEnable'"
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
                <template slot-scope="scope">
                  {{ scope.row.isEnable == 1 ? "启用" : "禁用" }}
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              v-if="!isChoosePage"
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'costCenterManage_edit'"
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <div
                  v-if="scope.row.isEnable == 0 && scope.row.organizationIds"
                  style="display: inline; margin: 0 6px"
                >
                  <el-button
                    v-permission="'costCenterManage_enable'"
                    @click="handleEnable(scope.row, 1)"
                    type="text"
                    size="small"
                    >启用</el-button
                  >
                </div>
                <div
                  v-if="scope.row.isEnable == 1"
                  style="display: inline; margin: 0 6px"
                >
                  <el-button
                    v-permission="'costCenterManage_enable'"
                    @click="handleEnable(scope.row, 0)"
                    type="text"
                    size="small"
                    >禁用</el-button
                  >
                </div>
                <el-button
                  v-if="scope.row.isEnable == 0"
                  v-permission="'costCenterManage_reset'"
                  @click="handleReset(scope.row)"
                  type="text"
                  size="small"
                  >重置</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <!-- 编辑 -->
    <el-dialog
      :title="'编辑'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      width="40%"
    >
      <el-form
        :model="dialogForm"
        :rules="rules"
        ref="form"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-form-item label="财务部门编码" prop="departNo">
          <el-input
            disabled
            v-model.trim="dialogForm.departNo"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="财务部门名称" prop="costCenterName">
          <el-input
            disabled
            v-model.trim="dialogForm.costCenterName"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="人员结算方式" prop="settlement">
          <el-select
            v-model="dialogForm.settlement"
            placeholder="请选择"
            clearable
            size="small"
          >
            <el-option
              v-for="(item, index) in settlementMethod"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="dialogForm.settlement == 'produce_manage'"
          label="是否生产成本中心"
          prop="produceCostCenter"
        >
          <el-radio-group
            v-model="dialogForm.produceCostCenter"
            @change="produceCostCenterChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            dialogForm.settlement == 'produce_manage' &&
            dialogForm.produceCostCenter
          "
          label="工资工号"
          prop="salaryProjectNo"
        >
          <el-input
            v-model.trim="dialogForm.salaryProjectNo"
            size="small"
            placeholder="请输入工资工号"
          ></el-input>
        </el-form-item>
        <el-form-item label="人力部门绑定" prop="organizationIds">
          <!-- <selectFilterTree :value="dialogForm.organizationId" :cnvalue="dialogForm.organizationName" ref="depttrees"
            :defaultData="deptTreeList" :treeProps="deptprops" @getSelectData="getSearchDeptData" treeplaceholder="请选择"
            :treeKey="'id'">
          </selectFilterTree> -->
          <el-cascader
            ref="cascader"
            v-model="organizationIdArrs"
            :options="deptTreeList"
            size="small"
            :props="props"
            clearable
            filterable
            @change="changeOrganization"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="人员模块" prop="moduleId">
          <!-- {{ modulezhiYuanOptions }} -->
          <el-select
            v-model="dialogForm.moduleId"
            placeholder="请选择"
            multiple
            clearable
            filterable
            size="small"
            @change="$forceUpdate()"
          >
            <el-option
              v-for="(item, index) in modulezhiYuanOptions"
              :key="index"
              :label="item.display"
              :value="item.enumValueId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model.trim="dialogForm.remark"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">取消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="okLoading"
          @click="submitForm"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import selectFilterTree from "@/components/treeComp/selectFilterTree.vue";
import {getFathersById} from "@/utils/tools.js";
const defaultSearchForm = {};
const defaultDialogForm = {
  settlement: null,
  organizationIds: null,
  organizationNames: null,
  costType: null,
  remark: null,
  produceCostCenter: true,
  salaryProjectNo: null,
  moduleIds: "",
  moduleId: []
};
export default {
  components: {
    Grid,
    selectFilterTree
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      statusList: [
        {
          label: '禁用',
          value: 0
        },
        {
          label: '启用',
          value: 1
        },
      ],
      settlementMethod: [
        {
          label: '工时',
          value: 'working_hours'
        },
        {
          label: '生产运行管理',
          value: 'produce_manage'
        }
      ],
      costType: [
        {
          label: '生产',
          value: 0
        },
        {
          label: '管理',
          value: 1
        }
      ],
      okLoading: false,
      rules: {
        settlement: [
          {required: true, message: '请选择人员结算方式', trigger: 'blur,change'},
        ],
        organizationIds: [
          {required: true, message: '请选择人力部门绑定', trigger: 'blur,change'},
        ],
        salaryProjectNo: [
          {required: true, message: '请输入工资工号', trigger: 'blur,change'},
        ],
        produceCostCenter: [
          {required: true, message: '请选择是否生产成本中心', trigger: 'blur,change'},
        ],
        moduleId: [
          {required: true, message: '请选择人员模块', trigger: 'blur,change'},
        ],
      },
      showDialog: false,
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '组织',
          key: 'orgcodeName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '财务部门编码',
          key: 'departNo',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '财务部门名称',
          key: 'departName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '人员结算方式',
          key: 'settlementName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '人力部门绑定',
          key: 'organizationNames',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '人员模块',
          key: 'moduleNames',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '状态',
          key: 'isEnable',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '备注',
          key: 'remark',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      dialogForm: _.cloneDeep(defaultDialogForm),
      deptTreeList: [],
      deptprops: {
        children: "children",
        label: "organizationName",
      },
      props: {
        multiple: true,
        checkStrictly: true,
        children: "children",
        label: "organizationName",
        value: 'id'
      },
      organizationIdArr: [], // 获取中文
      organizationIdArrs: [], // 保存结构需要的格式
      dataids: [],
      moduleOptions: [],
      modulezhiYuanOptions: [],
      bindmodulezhiYuanOptionList: [],
    };
  },
  props: {
    // 是否打开抽屉页
    isChoosePage: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.getDeptData();
  },
  methods: {
    getModuleOptions(id) {
      this.$api["systems/module-options"]({costCenterId: id}).then(data => {
        this.moduleOptions = data;
      });
    },




    produceCostCenterChange(e) {
      // console.log(e);
    },
    getDeptData() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.deptTreeList = data;
      });
    },
    getSearchDeptData(e) {
      this.dialogForm.organizationId = e;
    },
    handleSync() {
      this.okLoading = true;
      this.$api['systems/costCenter-sync']().then(data => {
        this.okLoading = false;
        this.$message({
          type: 'success',
          message: '同步成功',
        });
        this.$nextTick(() => {
          this.$refs.grid.query();
        });
      }).catch(() => {
        this.okLoading = false;
      });
    },
    async handleEdit(row) {
      // let allOptions = await this.$api["systems/module-zhiYuanModuleEnum"]({xx: 1});
      // this.bindmodulezhiYuanOptionList = await this.$api["systems/module-get-bound-list"]();
      // console.log("row===", row);
      // console.log("modulezhiYuanOptions", this.modulezhiYuanOptions);
      // console.log('bindmodulezhiYuanOptionList', this.bindmodulezhiYuanOptionList);
      // this.modulezhiYuanOptions = allOptions.map(item => {
      //   return {
      //     ...item,
      //     disabled: this.bindmodulezhiYuanOptionList.includes(item.enumValueId)
      //   };
      // });

      this.modulezhiYuanOptions = await this.$api["systems/module-zhiYuanModuleEnum"]({xx: 1});

      console.log('modulezhiYuanOptions', this.modulezhiYuanOptions);
      this.getModuleOptions(row.id);


      this.organizationIdArrs = [];
      this.$api['systems/costCenter-dts']({id: row.id}).then(async data => {
        this.dialogForm = _.cloneDeep(data);
        let _e = this.dialogForm.moduleIds ? this.dialogForm.moduleIds.split(",") : [];

        // _e.forEach(selectItem => { // 排除自身
        //   this.modulezhiYuanOptions = this.modulezhiYuanOptions.map(item => {
        //     if (selectItem === item.enumValueId) {
        //       return {
        //         ...item,
        //         disabled: false
        //       };
        //     }
        //     return item;
        //   });
        // });

        this.$set(this.dialogForm, 'moduleId', _e);
        console.log(this.dialogForm.moduleId, '====');
        this.showDialog = true;
        if (data.organizationIds && data.organizationIds != '') {
          this.organizationIdArrs = [];
          data.organizationIds.split(',').forEach(item => {
            let data = getFathersById(item, this.deptTreeList);
            this.organizationIdArrs.push(data);
          });
        }
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['commonModule/materialFinancialAccounts-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    changeOrganization(e) {
      this.organizationIdArr = [];
      e.forEach(item => {
        this.organizationIdArr.push(item[item.length - 1]);
      });
      this.dialogForm.organizationIds = this.organizationIdArr.join(',');
    },
    handleEnable(row, enable) {
      this.$confirm('确定' + (enable == 1 ? '启用' : '禁用') + '该数据？', '改变状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.isEnable = enable;
        this.$api['systems/costCenter-save'](row).then(data => {
          this.okLoading = false;
          this.$message({
            type: 'success',
            message: '状态切换成功',
          });
          this.showDialog = false;
          this.$nextTick(() => {
            this.$refs.grid.query();
          });
        }).catch(() => {
          this.okLoading = false;
        });
      });
    },
    handleReset(row) {
      this.$confirm('确定重置该数据？', '重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['systems/costCenter-reset']({id: row.id}).then(data => {
          this.okLoading = false;
          this.$message({
            type: 'success',
            message: '重置成功',
          });
          this.showDialog = false;
          this.$nextTick(() => {
            this.$refs.grid.query();
          });
        }).catch(() => {
          this.okLoading = false;
        });
      });
    },
    submitForm() {
      this.okLoading = true;
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!this.dialogForm.produceCostCenter) {
            this.dialogForm.salaryProjectNo = null;
          }
          let nodes = this.$refs.cascader.getCheckedNodes(), names = [];
          nodes.forEach(item => {
            if (this.organizationIdArr.indexOf(item.value) > -1) {
              names.push(item.label);
            }
          });
          this.dialogForm.moduleIds = this.dialogForm.moduleId.join(",");
          this.dialogForm.organizationNames = names.join(',');
          this.$api['systems/costCenter-save'](this.dialogForm).then(data => {
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.showDialog = false;
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            this.okLoading = false;
          });
        } else {
          this.okLoading = false;
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select,
.el-cascader {
  width: 100%;
}
</style>
