<template>
  <div class="table-container">
    <div class="table-wrapper">
      <el-table
        :data="tableData"
        stripe
        highlight-current-row
        v-loading="loading"
        class="custom-table">
        <el-table-column
          prop="extTemporaryAllocation"
          label="外单位临时调配"
          align="center"
          header-align="center"
          min-width="180" />
        <el-table-column
          prop="comTemporaryAllocation"
          label="公司内临时调配"
          align="center"
          header-align="center"
          min-width="180" />
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
          align="center"
          header-align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-permission="'enterData-edit'"
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加分页组件 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      title="编辑数据"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      custom-class="custom-dialog">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="150px"
        class="custom-form">
        <el-form-item label="外单位临时调配" prop="extTemporaryAllocation">
          <el-input-number
            v-model.number="form.extTemporaryAllocation"
            :controls="false"
            type="number"
            :min="0"
            class="custom-input"
            placeholder="请输入人数(必填)" />
        </el-form-item>
        <el-form-item label="公司内临时调配" prop="comTemporaryAllocation">
          <el-input-number
            v-model.number="form.comTemporaryAllocation"
            :controls="false"
            type="number"
            :min="0"
            class="custom-input"
            placeholder="请输入人数(必填)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'enterData',
  data() {
    return {
      tableData: [],
      loading: false,
      total: 0,
      queryParams: {
        current: 1,
        limit: 10
      },
      dialogVisible: false,
      form: {
        extTemporaryAllocation: '',
        comTemporaryAllocation: ''
      },
      rules: {
        extTemporaryAllocation: [
          {required: true, message: '请输入外单位临时调配人数', trigger: 'blur'},
          {type: 'number', min: 0, message: '人数不能小于0', trigger: 'blur'}
        ],
        comTemporaryAllocation: [
          {required: true, message: '请输入公司内临时调配人数', trigger: 'blur'},
          {type: 'number', min: 0, message: '人数不能小于0', trigger: 'blur'}
        ]
      }
    };
  },
  methods: {
    handleEdit(row) {
      this.form = {...row};
      this.dialogVisible = true;
    },
    handleSizeChange(val) {
      this.queryParams.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.queryParams.current = val;
      this.getTableData();
    },
    async getTableData() {
      try {

        this.$api['hrDashboard/financeErsonTemporaryAllocation-page']({param: this.queryParams}).then(res => {
          this.tableData = res.list;
          this.total = res.total;
        });
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
      } finally {
        this.loading = false;
      }
    },
    async handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.$api['hrDashboard/financeErsonTemporaryAllocation-save'](this.form).then(() => {
              this.$message.success('保存成功');
              this.dialogVisible = false;
              this.getTableData();
            });
          } catch (error) {
            console.error('保存失败:', error);
            this.$message.error('保存失败');
          }
        }
      });
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // background-color: #f5f7fa;

  // .table-wrapper {
  //   background-color: #fff;
  //   padding: 20px;
  //   border-radius: 4px;
  //   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  // }

  // .custom-table {
  //   margin-top: 10px;

  //   ::v-deep .el-table__header-wrapper {
  //     th {
  //       background-color: #f5f7fa;
  //       color: #606266;
  //       font-weight: bold;
  //       height: 50px;
  //     }
  //   }

  //   ::v-deep .el-table__row {
  //     height: 50px;
  //   }
  // }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
