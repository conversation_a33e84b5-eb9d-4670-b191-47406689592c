<template>
  <el-drawer
    :title="mode === 'edit' ? '编辑值班信息' : '查看值班信息'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <div v-if="mode === 'view'" class="section">
        <h3>基本信息</h3>
        <el-descriptions :column="3">
          <el-descriptions-item label="名称">{{ basicInfo.leaderDutyNo }}</el-descriptions-item>
          <el-descriptions-item label="年份">{{ basicInfo.year }}</el-descriptions-item>
          <el-descriptions-item label="月份">{{ basicInfo.month }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="section">
        <h3>列表详情</h3>
        <div style="margin-bottom: 10px">
          <el-button
            size="small"
            type="primary"
            @click="handleTemplateDownload"
            style="margin-right: 10px"
            >导入模板下载</el-button
          >

          <el-button
            size="small"
            type="primary"
            @click="handleBatchImport"
            style="margin-left: 10px"
            >批量导入</el-button
          >
        </div>
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          header-align="center"

        >
          <el-table-column prop="dutyDate" label="日期" min-width="180" align="center" />
          <el-table-column prop="sdDxLeaderName" label="大兴值班领导姓名" min-width="180" align="center" />
          <el-table-column prop="sdDxLeaderOrg" label="组织" min-width="180" align="center" />

          <el-table-column prop="sdDxLeaderMobile" label="手机号" min-width="sdDxLeaderMobile" align="center" />

          <el-table-column label="操作" width="180"  v-if="mode === 'edit'" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleEdit(scope.row)"

              >
                编辑
              </el-button>

            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="mt-20"
          style="text-align: right; margin: 20px 0;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="param.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="param.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />


        <bw-import
          :action="importUrl"
          :on-success="successInit"
          :on-error="errorInit"
          :before-upload="handleBeforeUpload"
          accept=".xlsx"
          :extraParams="extraParams"
          ref="importNode"
        ></bw-import>
      </div>

      <div class="drawer-footer" v-if="mode === 'edit'">
        <el-button @click="handleCancel">关闭</el-button>
        <!-- <el-button type="primary" @click="handleSubmit">提交</el-button> -->
      </div>
    </div>

    <!-- 编辑表单抽屉 -->
    <el-drawer
      title="编辑"
      :visible.sync="editDrawerVisible"
      :size="'40%'"
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <div class="drawer-content">
        <el-form :model="editForm" ref="editForm" label-width="140px" :rules="rules">
          <el-form-item label="大兴值班领导姓名" prop="sdDxLeaderName" required>
            <el-input
              v-model="editForm.sdDxLeaderName"
              placeholder="请选择用户"
              readonly
              @click="openUserChoose"
              style="width: 100%"
            >
              <el-button slot="append" icon="el-icon-user" @click="openUserChoose"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="组织">
            <el-input v-model="editForm.sdDxLeaderOrg" disabled placeholder="根据大兴值班领导姓名自动代入" />
          </el-form-item>

          <el-form-item label="手机号">
            <el-input
              v-model="editForm.sdDxLeaderMobile"
              disabled
              placeholder="请填写手机号"
            />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="editForm.dutyDate"
              type="date"
              placeholder="自动生成前一天"
              :disabled="true"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="editDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">提交</el-button>
        </div>
      </div>

      <!-- <userChoose :key="userTimer" @getData="getConditionData" ref="userchoose" /> -->
      <user-choose
        ref="userChoose"
        :selected-user="selectedUser"
        @select="handleUserSelect"
      />
    </el-drawer>


  </el-drawer>


</template>

<script>
import userChoose from "./userChoose.vue";
import _ from "lodash";
import {mapActions} from "vuex";
import BwImport from "@/components/BwImport";
export default {
  name: 'DutyTable',
  components: {
    userChoose,
    BwImport
  },
  data() {
    return {
      extraParams: {},
      importUrl: "/api/syn/dashboardLeaderDutyDetail/import-LeaderDuty",
      selectedUser: {},
      userTimer: 0,
      visible: false,
      basicInfo: {
        name: '',
        year: '',
        month: ''
      },
      tableData: [],
      mode: 'view',
      editDrawerVisible: false,
      editForm: {
        sdDxLeaderName: '',
        sdDxLeaderOrg: '',
        dutyDate: null
      },
      leaderOptions: [],
      param: {
        current: 1,
        limit: 10
      },
      total: 0,
      rules: {
        sdDxLeaderName: [
          {required: true, message: '请选择值班领导姓名', trigger: 'change'}
        ]
      }
    };
  },
  methods: {
    ...mapActions(["download"]),
    handleTemplateDownload() {

      let s = '';
      Object.keys(this.data).forEach((key, i) => {
        if (this.data[key]) {
          if (i != Object.keys(this.data).length - 1) {
            s = s + key + '=' + this.data[key] + '&';
          } else {
            s = s + key + '=' + this.data[key];
          }
        }
      });
      let api = "/api/syn/dashboardLeaderDutyDetail/templateDownload-dx";
      let url = `${api}?${s}`;
      let downData = {
        url: url,
        downLoad: "大兴值班信息.xlsx",
      };
      this.download(downData); // 下载
    },

    handleBatchImport() {
      this.$refs.importNode.$refs.fileUpload.$refs[
        "upload-inner"
      ].handleClick();
    },
    successInit(response, file, fileList) {
      console.log("response, file, fileList", response, file, fileList);
      this.$nextTick(() => {
        this.fetchTableData();
        this.$message.success("批量导入成功");
      });
    },
    errorInit(response, file, fileList) {
      this.$message.error("批量导入出错");
    },
    handleBeforeUpload() {},
    openUserChoose() {
      this.$refs.userChoose.open();
    },
    handleUserSelect(user) {

      console.log("user11", user);

      this.selectedUser = user;
      this.editForm.sdDxLeaderName = user.nickname;
      this.editForm.sdDxLeaderOrg = user.corporateIdentity;
      this.editForm.sdDxLeaderId = user.id;
      this.editForm.sdDxLeaderMobile = user.account;
      console.log('this.selectedUser', this.selectedUser);
      // 处理选中的用户数据
    },
    open(data, mode = 'view') {
      this.mode = mode;
      this.data = data;
      this.extraParams = {
        leaderDutyId: data.id,
      };
      // 重置分页参数
      this.param = {
        current: 1,
        limit: 10
      };
      this.total = 0;
      this.tableData = [];

      // 如果有ID，获取详情数据
      // 获取表格数据

      if (data.id) {
        this.fetchTableData();
        this.$api['boardData/dashboardLeaderDuty-get']({
          id: data.id
        }).then(res => {
          if (res) {

            this.basicInfo = {
              leaderDutyNo: res.leaderDutyNo || '',
              year: res.year || '',
              month: res.month || ''
            };
          }
        });
      }
      this.visible = true;
    },
    fetchZhiYuanPerson(ids) {
      return this.$api["systems/find-zhi-yuan-person"]({ids}).then(res => {
        return res;
      });
    },

    handleCancel() {
      this.visible = false;
      this.$emit('cancel');
    },

    // 处理编辑按钮点击
    handleEdit(row) {
      this.currentEditRow = row;


      this.editForm = {
        sdDxLeaderName: row.sdDxLeaderName,
        sdDxLeaderOrg: row.sdDxLeaderOrg,
        dutyDate: row.dutyDate,
        sdDxLeaderId: row.sdDxLeaderId,
        sdDxLeaderMobile: row.sdDxLeaderMobile
      };
      this.editDrawerVisible = true;
    },

    // 处理提交按钮点击
    handleSubmit() {
      this.$emit('submit', this.tableData);
    },

    submitEdit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          // 处理编辑表单提交
          const params = {
            ...this.currentEditRow,
            sdDxLeaderName: this.editForm.sdDxLeaderName,
            sdDxLeaderOrg: this.editForm.sdDxLeaderOrg,
            dutyDate: this.editForm.dutyDate,
            sdDxLeaderId: this.editForm.sdDxLeaderId,
            sdDxLeaderMobile: this.editForm.sdDxLeaderMobile
          };

          this.$api['boardData/dashboardLeaderDutyDetail-save'](params).then(res => {
            this.$message.success('保存成功');
            this.fetchTableData();
          }).catch(err => {
            this.$message.error('保存失败');
          });
          this.editDrawerVisible = false;
        }
      });
    },

    // Handle page size change
    handleSizeChange(val) {
      this.param.limit = val;
      this.param.current = 1;
      this.fetchTableData();
    },

    // Handle current page change
    handleCurrentChange(val) {
      this.param.current = val;
      this.fetchTableData();
    },

    // Fetch table data with pagination
    fetchTableData(data) {
      this.$api['boardData/dashboardLeaderDutyDetail-page']({
        current: this.param.current,
        limit: this.param.limit,
        param: {
          leaderDutyId: this.data.id
        }
      }).then(res => {
        this.tableData = res.list || [];
        this.total = res.total || 0;
        this.$forceUpdate();
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.section {
  margin-bottom: 24px;
}

.section h3 {
  margin-bottom: 16px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
}

.drawer-footer .el-button {
  margin-left: 10px;
}

/* 添加编辑抽屉的样式 */
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}

.el-drawer {
  z-index: 2001;
}
</style>
