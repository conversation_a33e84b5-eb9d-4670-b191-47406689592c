{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue?vue&type=template&id=392841f4&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue", "mtime": 1754276220638}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div>\n  <!-- 操作按钮区域 -->\n  <div style=\"margin-bottom: 16px;\">\n    <el-button size=\"small\" @click=\"goBack\">\n      <i class=\"el-icon-arrow-left\"></i> 返回\n    </el-button>\n    <el-button v-permission=\"'assetLocation_add'\" type=\"primary\" size=\"small\" @click=\"openDialog\" style=\"margin-left: 10px;\">\n      <i class=\"el-icon-plus\"></i> 绑定资产位置\n    </el-button>\n  </div>\n\n  <el-tabs v-model=\"activeTab\" type=\"card\">\n    <!-- 资产位置 Tab -->\n    <el-tab-pane label=\"资产位置\" name=\"assetLocation\">\n      <el-row>\n        <el-col :span=\"24\">\n          <Grid\n            api=\"visualOpsManagement/assetLocation-page\"\n            :event-bus=\"assetLocationSearchEventBus\"\n            :search-params=\"assetLocationSearchParams\"\n            :newcolumn=\"assetLocationColumns\"\n            @datas=\"getAssetLocationDatas\"\n            @columnChange=\"getAssetLocationColumn\"\n            :auto-load=\"true\"\n            ref=\"assetLocationGrid\">\n            <el-table\n              slot=\"table\"\n              slot-scope=\"{loading}\"\n              v-loading=\"loading\"\n              :data=\"assetLocationTableData\"\n              stripe\n              ref=\"assetLocationTable\"\n              style=\"width: 100%\">\n              <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\n              </el-table-column>\n              <template v-for=\"(item, index) in assetLocationColumns\">\n                <el-table-column\n                  v-if=\"item.key == 'belongDept'\"\n                  :show-overflow-tooltip=\"true\"\n                  :align=\"item.align ? item.align : 'center'\"\n                  :key=\"index\"\n                  :prop=\"item.key\"\n                  :label=\"item.title\"\n                  :min-width=\"item.minWidth ? item.minWidth : '150'\">\n                  <template slot-scope=\"scope\">\n                    <div>\n                      {{ scope.row.belongDept || '未分配' }}\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column\n                  v-else\n                  :show-overflow-tooltip=\"true\"\n                  :key=\"item.key\"\n                  :prop=\"item.key\"\n                  :label=\"item.title\"\n                  :min-width=\"item.minWidth ? item.minWidth : '150'\"\n                  :align=\"item.align ? item.align : 'center'\">\n                </el-table-column>\n              </template>\n              <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-button  type=\"text\" size=\"small\" @click=\"handleAssetLocationBind(scope.row)\">取消绑定</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </Grid>\n        </el-col>\n      </el-row>\n    </el-tab-pane>\n\n    <!-- 资产设备 Tab -->\n    <el-tab-pane label=\"资产设备\" name=\"assetDevice\">\n      <el-row>\n        <el-col :span=\"24\">\n          <Grid\n            api=\"visualOpsManagement/assetDevice-page\"\n            :event-bus=\"assetDeviceSearchEventBus\"\n            :search-params=\"assetDeviceSearchParams\"\n            :newcolumn=\"assetDeviceColumns\"\n            @datas=\"getAssetDeviceDatas\"\n            @columnChange=\"getAssetDeviceColumn\"\n            :auto-load=\"false\"\n            ref=\"assetDeviceGrid\">\n            <el-table\n              slot=\"table\"\n              slot-scope=\"{loading}\"\n              v-loading=\"loading\"\n              :data=\"assetDeviceTableData\"\n              stripe\n              ref=\"assetDeviceTable\"\n              style=\"width: 100%\">\n              <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\n              </el-table-column>\n              <template v-for=\"(item, index) in assetDeviceColumns\">\n                <el-table-column\n                  v-if=\"item.key == 'recordStatus'\"\n                  :show-overflow-tooltip=\"true\"\n                  :align=\"item.align ? item.align : 'center'\"\n                  :key=\"index\"\n                  :prop=\"item.key\"\n                  :label=\"item.title\"\n                  :min-width=\"item.minWidth ? item.minWidth : '150'\">\n                  <template slot-scope=\"scope\">\n                    <div>\n                      {{ getStatusText(scope.row.recordStatus) }}\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column\n                  v-else\n                  :show-overflow-tooltip=\"true\"\n                  :key=\"item.key\"\n                  :prop=\"item.key\"\n                  :label=\"item.title\"\n                  :min-width=\"item.minWidth ? item.minWidth : '150'\"\n                  :align=\"item.align ? item.align : 'center'\">\n                </el-table-column>\n              </template>\n            </el-table>\n          </Grid>\n        </el-col>\n      </el-row>\n    </el-tab-pane>\n  </el-tabs>\n\n\n    <!-- 绑定位置弹窗 -->\n  <BindPositionDialog\n    :visible.sync=\"dialogVisible\"\n    :row-data=\"currentRow\"\n    :regin-id=\"reginId\"\n    :bound-location-ids=\"assetDeviceSearchParams.locationIds\"\n    @success=\"handleSuccess\"\n    @cancel=\"handleCancel\"\n  />\n\n</div>\n", null]}