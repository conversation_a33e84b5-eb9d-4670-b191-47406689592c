<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid api="systems/costProject-page" :event-bus="searchEventBus" :search-params="searchForm" :newcolumn="columns"
          @datas="getDatas" @columnChange="getcolumn" :auto-load="true" ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="工号">
                <el-input v-model.trim="searchForm.jobNum" size="small" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="成本中心编号">
                <el-input v-model.trim="searchForm.departNo" size="small" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="成本中心">
                <el-input v-model.trim="searchForm.departName" size="small" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="项目名称">
                <el-input v-model.trim="searchForm.projectName" size="small" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="备注">
                <el-input v-model.trim="searchForm.remark" size="small" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'costCenterManage_add'" size="small" type="primary"
              @click="handleEdit(0)">新增</el-button>
              <el-button v-permission="'jobNumberManage_async'" size="small" type="primary"
              @click="handleSync">同步</el-button>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table"
            style="width: 100%">
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column :show-overflow-tooltip="true" :key="item.key" :prop="item.key" :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'" :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="180">
              <template slot-scope="scope">
                <el-button v-permission="'jobNumberManage_edit'" @click="handleEdit(1, scope.row)" type="text"
                  size="small">编辑</el-button>
                <el-button v-permission="'jobNumberManage_del'" type="text" size="small" slot="reference"
                  @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <!-- 编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="showDialog" :close-on-click-modal="false" width="40%">
      <el-form :model="dialogForm" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="工号" prop="jobNum">
          <el-input v-model.trim="dialogForm.jobNum" size="small" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenterId">
          <el-select v-model="dialogForm.costCenterId" placeholder="请选择" clearable filterable size="small">
            <el-option v-for="(item, index) in costCenterList" :key="index" :label="item.departName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model.trim="dialogForm.projectName" size="small" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="dialogForm.remark" size="small" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">取消</el-button>
        <el-button size="small" type="primary" :loading="okLoading" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultDialogForm = {};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      okLoading: false,
      rules: {
        jobNum: [
          {required: true, message: '请输入工号', trigger: 'blur,change'},
        ],
        costCenterId: [
          {required: true, message: '请选择成本中心', trigger: 'blur,change'},
        ],
      },
      showDialog: false,
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '工号',
          key: 'jobNum',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '成本中心编号',
          key: 'departNo',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '成本中心',
          key: 'departName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '项目名称',
          key: 'projectName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '备注',
          key: 'remark',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      dialogForm: _.cloneDeep(defaultDialogForm),
      dialogTitle: '新增',
      costCenterList: [], // 成本中心
    };
  },
  watch: {
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 成本中心列表
      this.$api['systems/costCenter-list']({
        params: {}
      }).then(data => {
        this.costCenterList = data;
      });
    },
    handleSync() {
      this.$confirm("确定同步数据？", "同步提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '同步中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$api["systems/costProject-async"]({}).then(data => {
            loading.close();
            this.loading = false,
            this.$refs.grid.query();
            this.$message({
              type: "success",
              message: "同步成功",
            });
          });
        })
        .catch(() => {
          loading.close();
          this.$message({
            type: "info",
            message: "已取消同步",
          });
        });
    },

    async handleEdit(type, row) {
      this.dialogTitle = type == 0 ? '新增' : '编辑';
      if (type == 0) {
        this.dialogForm = _.cloneDeep(defaultDialogForm);
        this.showDialog = true;
      } else {
        await this.$api['systems/costProject-dts']({id: row.id}).then(data => {
          this.dialogForm = _.cloneDeep(data);
          this.showDialog = true;
        });
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['systems/costProject-del']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    submitForm() {
      this.okLoading = true;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$api['systems/costProject-save'](this.dialogForm).then(data => {
            this.showDialog = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            this.okLoading = false;
          });
        } else {
          this.okLoading = false;
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
