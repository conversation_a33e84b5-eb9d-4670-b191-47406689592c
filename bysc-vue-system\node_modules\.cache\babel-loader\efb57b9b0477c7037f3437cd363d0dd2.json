{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\visualOpsManagement.js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\visualOpsManagement.js", "mtime": *************}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'visualOpsManagement-page',\n  // 图纸分页\n  method: 'POST',\n  path: '/syn/maintenanceDrawingManagement/page'\n}, {\n  name: 'visualOpsManagement-save',\n  // 编图纸保存\n  method: 'POST',\n  path: '/syn/maintenanceDrawingManagement/save'\n}, {\n  name: 'visualOpsManagement-get',\n  // 获取图纸\n  method: 'OTHERGET',\n  path: '/syn/maintenanceDrawingManagement/get'\n}, {\n  name: 'visualOpsManagement-delete',\n  // 图纸删除\n  method: 'POSt',\n  path: '/syn/maintenanceDrawingManagement/delete'\n}, {\n  name: 'workorderFirstLineDept-findAll',\n  // 查询一线部门列表\n  method: 'POST',\n  path: '/workorder/workorderFirstLineDept/findAll'\n},\n//  区域管理分页\n{\n  name: 'maintenanceRegion-page',\n  // 区域管理分页\n  method: 'POST',\n  path: '/syn/maintenanceRegion/page'\n}, {\n  name: 'maintenanceRegionLocationRel-get',\n  // 根据区域ID查询已绑定的位置ID\n  method: 'OTHERGET',\n  path: '/syn/maintenanceRegionLocationRel/getByreginId'\n}, {\n  name: 'assetLocation-page',\n  // 资产位置关系表分页\n  method: 'POST',\n  path: '/syn/maintenanceRegionLocationRel/page'\n}, {\n  name: 'assetLocation-save',\n  // 保存可视化区域\n  method: 'POST',\n  path: '/syn/maintenanceRegionLocationRel/save'\n}, {\n  name: 'assetLocation-delete',\n  // 取消绑定\n  method: 'OTHER',\n  path: '/syn/maintenanceRegionLocationRel/delete'\n}, {\n  name: 'assetDevice-page',\n  // 查询设备资产卡片分页\n  method: 'POST',\n  path: '/device/deviceAssetCard/pageByRegionId'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/boweiWorkSpace/pc/bysc-vue-system/src/bysc_system/service/api/account/visualOpsManagement.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'visualOpsManagement-page', // 图纸分页\r\n    method: 'POST',\r\n    path: '/syn/maintenanceDrawingManagement/page'\r\n  },\r\n  {\r\n    name: 'visualOpsManagement-save', // 编图纸保存\r\n    method: 'POST',\r\n    path: '/syn/maintenanceDrawingManagement/save'\r\n  },\r\n  {\r\n    name: 'visualOpsManagement-get', // 获取图纸\r\n    method: 'OTHERGET',\r\n    path: '/syn/maintenanceDrawingManagement/get'\r\n  },\r\n  {\r\n    name: 'visualOpsManagement-delete', // 图纸删除\r\n    method: 'POSt',\r\n    path: '/syn/maintenanceDrawingManagement/delete'\r\n  },\r\n\r\n  {\r\n    name: 'workorderFirstLineDept-findAll', // 查询一线部门列表\r\n    method: 'POST',\r\n    path: '/workorder/workorderFirstLineDept/findAll'\r\n  },\r\n  //  区域管理分页\r\n  {\r\n    name: 'maintenanceRegion-page', // 区域管理分页\r\n    method: 'POST',\r\n    path: '/syn/maintenanceRegion/page'\r\n  },\r\n\r\n  {\r\n    name: 'maintenanceRegionLocationRel-get', // 根据区域ID查询已绑定的位置ID\r\n    method: 'OTHERGET',\r\n    path: '/syn/maintenanceRegionLocationRel/getByreginId'\r\n  },\r\n  {\r\n    name: 'assetLocation-page', // 资产位置关系表分页\r\n    method: 'POST',\r\n    path: '/syn/maintenanceRegionLocationRel/page'\r\n  },\r\n  {\r\n    name: 'assetLocation-save', // 保存可视化区域\r\n    method: 'POST',\r\n    path: '/syn/maintenanceRegionLocationRel/save'\r\n  },\r\n  {\r\n    name: 'assetLocation-delete', // 取消绑定\r\n    method: 'OTHER',\r\n    path: '/syn/maintenanceRegionLocationRel/delete'\r\n  },\r\n  {\r\n    name: 'assetDevice-page', // 查询设备资产卡片分页\r\n    method: 'POST',\r\n    path: '/device/deviceAssetCard/pageByRegionId'\r\n  },\r\n\r\n\r\n\r\n];\r\n\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,0BAA0B;EAAE;EAClCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,0BAA0B;EAAE;EAClCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,yBAAyB;EAAE;EACjCC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,4BAA4B;EAAE;EACpCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,gCAAgC;EAAE;EACxCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC;AACD;AACA;EACEF,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EAED;EACEF,IAAI,EAAE,kCAAkC;EAAE;EAC1CC,MAAM,EAAE,UAAU;EAClBC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,oBAAoB;EAAE;EAC5BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,sBAAsB;EAAE;EAC9BC,MAAM,EAAE,OAAO;EACfC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CAIF"}]}