{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaBinding\\index.vue?vue&type=template&id=dc3faea2&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaBinding\\index.vue", "mtime": 1754276220632}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"visualOpsManagement/maintenanceRegion-page\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getColumn\"\n        :auto-load=\"true\"\n        ref=\"grid\">\n        <div slot=\"search\">\n          <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\n            <el-form-item label=\"区域编号\">\n              <el-input\n                v-model.trim=\"searchForm.regionCode\"\n                size=\"small\"\n                maxlength=\"32\"\n                placeholder=\"请输入\"\n                clearable\n                style=\"width: 200px\">\n              </el-input>\n            </el-form-item>\n            <el-form-item label=\"区域名称\">\n              <el-input\n                v-model.trim=\"searchForm.regionName\"\n                size=\"small\"\n                maxlength=\"32\"\n                placeholder=\"请输入\"\n                clearable\n                style=\"width: 200px\">\n              </el-input>\n            </el-form-item>\n            <el-form-item>\n              <el-button size=\"small\" type=\"primary\" style=\"margin: 0 0 0 10px\" @click=\"searchTable\">查询</el-button>\n              <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n         <div slot=\"action\">\n          <el-button  size=\"small\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i> 返回\n          </el-button>\n        </div>\n\n        <el-table\n          slot=\"table\"\n          slot-scope=\"{loading}\"\n          v-loading=\"loading\"\n          :data=\"tableData\"\n          stripe\n          ref=\"table\"\n          style=\"width: 100%\">\n          <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <template v-for=\"item in columns\">\n            <el-table-column\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.minWidth ? item.minWidth : '150'\"\n              :align=\"item.align ? item.align : 'center'\">\n            </el-table-column>\n          </template>\n          <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" size=\"small\" @click=\"handleBind(scope.row)\">绑定设备</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n\n\n\n</div>\n", null]}