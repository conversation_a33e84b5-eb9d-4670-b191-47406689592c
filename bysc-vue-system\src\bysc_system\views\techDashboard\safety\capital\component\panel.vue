<template>
  <el-drawer
    :title="mode === 'edit' ? '编辑' : '添加'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
    :modal="false"
    :with-header="true"
  >
    <div class="drawer-content">
      <div class="section">
        <div class="section-header">
          <h3>事件列表</h3>
          <el-button type="primary" size="small" @click="handleAddEvent">
            <i class="el-icon-plus"></i> 添加事件
          </el-button>
        </div>

        <Grid
          api="techDashboard/sysSecurityControlRatingDetail-page"
          :event-bus="searchEventBus"
          :search-params="searchParams"
          :newcolumn="columns"
          @datas="handleDataReceived"
          @columnChange="handleColumnChange"
          ref="grid"
        >
          <el-table
            slot="table"
            slot-scope="{ loading }"
            v-loading="loading"
            :data="eventList"
            stripe
            style="width: 100%"
            border
            empty-text="暂无事件数据，请点击添加事件按钮"
          >
            <el-table-column
              fixed="left"
              align="center"
              label="序号"
              type="index"
              width="50"
            >
            </el-table-column>
            <el-table-column
              prop="eventName"
              label="事件名称"
              min-width="150"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="eventType"
              label="事件类型"
              min-width="120"
              align="center"
            >
            <template slot-scope="scope">
              <span :style="{color: scope.row.eventType === 'PLUSES' ? '#67C23A' : ''}">{{scope.row.eventType === 'PLUSES' ? '加分项' : scope.row.eventType}}</span>
            </template>
            </el-table-column>
            <el-table-column
              prop="number"
              label="数量"
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="singleScore"
              label="单值"
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="detailTotalScore"
              label="总分值"
              min-width="80"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="150"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleEditEvent(scope.row)"
                >编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="handleDeleteEvent(scope.row, scope.$index)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>

      </div>

      <div class="drawer-footer">
        <el-button @click="handleCancel" size="small">关闭</el-button>
      </div>
    </div>

    <EventPanel
      ref="eventPanel"
      @refresh="handleEventAdded"
    />
  </el-drawer>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import EventPanel from './eventPanel.vue';

export default {
  name: 'CapitalSafetyPanel',
  components: {
    Grid,
    EventPanel
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      visible: false,
      mode: 'add', // 'add' 或 'edit'
      form: {
        id: null,
        date: '',
        totalScore: '0'
      },
      // 表格列配置
      columns: [
        {
          title: "事件名称",
          key: "eventName",
          width: "150",
        },
        {
          title: "事件类型",
          key: "eventType",
          width: "120",
        },
        {
          title: "数量",
          key: "number",
          width: "80",
        },
        {
          title: "单值",
          key: "singleScore",
          width: "80",
        },
        {
          title: "总分值",
          key: "detailTotalScore",
          width: "80",
        }
      ],
      displayColumns: [],
      searchParams: {
        securityId: null
      },
      eventList: [],
      submitLoading: false,
      hasBonusItems: false
    };
  },
  created() {
    // 初始化displayColumns
    this.displayColumns = [...this.columns];
  },
  methods: {
    // 打开抽屉
    open(row, mode) {
      this.mode = mode;
      this.visible = true;

      if (row) {
        // 编辑模式
        this.form.securityId = row.id;
        this.searchParams.securityId = row.id;
        // 加载事件列表
        this.$nextTick(() => {
          if (this.$refs.grid) {
            this.$refs.grid.query();
          }
        });
      }
    },

    // 列变更处理
    handleColumnChange(columns) {
      this.displayColumns = columns;
    },

    // 数据加载处理
    handleDataReceived(data) {
      this.eventList = data || [];
      this.calculateTotalScore();
      this.checkForBonusItems();
    },

    // 计算总分值
    calculateTotalScore() {
      let total = 0;
      this.eventList.forEach(event => {
        const score = parseFloat(event.detailTotalScore || 0);
        if (!isNaN(score)) {
          total += score;
        }
      });
      this.form.totalScore = total.toString();
    },

    // 检查是否有加分项
    checkForBonusItems() {
      this.hasBonusItems = this.eventList.some(item => {
        const value = parseFloat(item.singleScore);
        return !isNaN(value) && value > 0;
      });
    },

    // 添加事件
    handleAddEvent() {
      this.$refs.eventPanel.open({securityId: this.form.securityId}, 'add');
    },

    // 编辑事件
    handleEditEvent(row) {
      this.$refs.eventPanel.open(row, 'edit');
    },

    // 删除事件
    handleDeleteEvent(row, index) {
      this.$confirm('确认删除该事件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (row.id) {
          // 如果有ID，调用删除API
          this.$api['techDashboard/sysSecurityControlRatingDetail-delete']({ids: [row.id]})
            .then(() => {
              this.$message.success('删除成功');
              this.$refs.grid.query(); // 重新加载数据
            });
        }
      }).catch(() => {
        // 取消删除
      });
    },

    // 事件添加或更新后的回调
    handleEventAdded() {
      if (this.$refs.grid) {
        this.$refs.grid.query();
      }
    },

    // 取消操作
    handleCancel() {
      this.visible = false;
      // 触发刷新事件，刷新index页面的表格数据
      this.$emit('refresh');
    },

    // 提交表单
    handleSubmit() {
      this.submitLoading = true;
      const params = {
        id: this.form.id,
        totalScore: this.form.totalScore
      };

      this.$api['techDashboard/sysSecurityControlRatingDetail-save'](params)
        .then(() => {
          this.$message.success('操作成功');
          this.visible = false;
          this.submitLoading = false;
          // 通知父组件刷新数据
          this.$emit('refresh');
        })
        .catch(err => {
          this.submitLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100% - 60px);
  overflow-y: auto;

  .section {
    margin-bottom: 30px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h3 {
        margin: 0;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .note-text {
    color: #E6A23C;
    margin-top: 10px;
    font-size: 14px;
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    text-align: right;
    background-color: #fff;
    border-top: 1px solid #e4e7ed;
  }
}
</style>