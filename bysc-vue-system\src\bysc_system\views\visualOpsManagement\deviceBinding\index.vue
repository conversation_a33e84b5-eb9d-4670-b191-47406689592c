<template>
  <div>
    <!-- 操作按钮区域 -->
    <div style="margin-bottom: 16px;">
      <el-button size="small" @click="goBack">
        <i class="el-icon-arrow-left"></i> 返回
      </el-button>
      <el-button v-permission="'assetLocation_add'" type="primary" size="small" @click="openDialog" style="margin-left: 10px;">
        <i class="el-icon-plus"></i> 绑定资产位置
      </el-button>
    </div>

    <el-tabs v-model="activeTab" type="card">
      <!-- 资产位置 Tab -->
      <el-tab-pane label="资产位置" name="assetLocation">
        <el-row>
          <el-col :span="24">
            <Grid
              api="visualOpsManagement/assetLocation-page"
              :event-bus="assetLocationSearchEventBus"
              :search-params="assetLocationSearchParams"
              :newcolumn="assetLocationColumns"
              @datas="getAssetLocationDatas"
              @columnChange="getAssetLocationColumn"
              :auto-load="true"
              ref="assetLocationGrid">
              <el-table
                slot="table"
                slot-scope="{loading}"
                v-loading="loading"
                :data="assetLocationTableData"
                stripe
                ref="assetLocationTable"
                style="width: 100%">
                <el-table-column fixed="left" label="序号" type="index" width="50">
                </el-table-column>
                <template v-for="(item, index) in assetLocationColumns">
                  <el-table-column
                    v-if="item.key == 'belongDept'"
                    :show-overflow-tooltip="true"
                    :align="item.align ? item.align : 'center'"
                    :key="index"
                    :prop="item.key"
                    :label="item.title"
                    :min-width="item.minWidth ? item.minWidth : '150'">
                    <template slot-scope="scope">
                      <div>
                        {{ scope.row.belongDept || '未分配' }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else
                    :show-overflow-tooltip="true"
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :min-width="item.minWidth ? item.minWidth : '150'"
                    :align="item.align ? item.align : 'center'">
                  </el-table-column>
                </template>
                <el-table-column fixed="right" align="center" label="操作" type="action" width="120">
                  <template slot-scope="scope">
                    <el-button  type="text" size="small" @click="handleAssetLocationBind(scope.row)">取消绑定</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </Grid>
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 资产设备 Tab -->
      <el-tab-pane label="资产设备" name="assetDevice">
        <el-row>
          <el-col :span="24">
            <Grid
              api="visualOpsManagement/assetDevice-page"
              :event-bus="assetDeviceSearchEventBus"
              :search-params="assetDeviceSearchParams"
              :newcolumn="assetDeviceColumns"
              @datas="getAssetDeviceDatas"
              @columnChange="getAssetDeviceColumn"
              :auto-load="false"
              ref="assetDeviceGrid">
              <el-table
                slot="table"
                slot-scope="{loading}"
                v-loading="loading"
                :data="assetDeviceTableData"
                stripe
                ref="assetDeviceTable"
                style="width: 100%">
                <el-table-column fixed="left" label="序号" type="index" width="50">
                </el-table-column>
                <template v-for="(item, index) in assetDeviceColumns">
                  <el-table-column
                    v-if="item.key == 'recordStatus'"
                    :show-overflow-tooltip="true"
                    :align="item.align ? item.align : 'center'"
                    :key="index"
                    :prop="item.key"
                    :label="item.title"
                    :min-width="item.minWidth ? item.minWidth : '150'">
                    <template slot-scope="scope">
                      <div>
                        {{ getStatusText(scope.row.recordStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else
                    :show-overflow-tooltip="true"
                    :key="item.key"
                    :prop="item.key"
                    :label="item.title"
                    :min-width="item.minWidth ? item.minWidth : '150'"
                    :align="item.align ? item.align : 'center'">
                  </el-table-column>
                </template>
              </el-table>
            </Grid>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>


      <!-- 绑定位置弹窗 -->
    <BindPositionDialog
      :visible.sync="dialogVisible"
      :row-data="currentRow"
      :regin-id="reginId"
      :bound-location-ids="assetDeviceSearchParams.locationIds"
      @success="handleSuccess"
      @cancel="handleCancel"
    />

  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import BindPositionDialog from './component/BindPositionDialog';
import {getRouteParams, saveRouteParams, navigateWithParams} from '@/utils/routeParams';



export default {
  name: 'DeviceBinding',
  components: {
    Grid,
    BindPositionDialog
  },
  destroyed() {
    this.assetLocationSearchEventBus.$off();
    this.assetDeviceSearchEventBus.$off();
  },
  data() {
    this.assetLocationSearchEventBus = new Vue();
    this.assetDeviceSearchEventBus = new Vue();

    return {
      dialogVisible: false,
      currentRow: {},
      activeTab: 'assetLocation', // 当前激活的Tab
      reginId: '', // 区域ID
      drawingId: '', // 图纸ID

      // 资产位置搜索参数
      assetLocationSearchParams: {
        reginId: ''
      },
      // 资产设备搜索参数
      assetDeviceSearchParams: {
        locationIds: [] // 已绑定的位置ID数组，每次获取资产设备时都会传递最新的位置ID
      },

      // 资产位置相关数据
      assetLocationColumns: [
        {
          title: '资产编号',
          key: 'locationCode',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '资产位置',
          key: 'locationDesc',
          tooltip: true,
          minWidth: 150,
        }
      ],
      assetLocationTableData: [],

      // 资产设备相关数据
      assetDeviceColumns: [
        {
          title: '资产编号',
          key: 'assetCode',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '状态',
          key: 'recordStatus',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '所属一线部门',
          key: 'firstDeptName',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '资产描述',
          key: 'assetDesc',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '规格',
          key: 'specification',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '是否主设备',
          key: 'mainDeviceName',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '关联主设备',
          key: 'relatedMainDeviceCode',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '位置编码',
          key: 'locationCode',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '位置描述',
          key: 'locationDesc',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '安装位置',
          key: 'installationLocation',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 150,
        }
      ],
      assetDeviceTableData: []
    };
  },
  created() {
    // 使用工具函数获取参数
    const params = getRouteParams(this.$route, ['reginId', 'drawingId'], 'deviceBinding_params');

    if (params.reginId) {
      this.reginId = params.reginId;
      this.assetLocationSearchParams.reginId = params.reginId;
      console.log('获取到 reginId 参数:', params.reginId);

      // 获取已绑定的位置ID
      this.getBindedLocationIds();
    } else {
      console.log('未获取到 reginId 参数');
      this.$message.warning('缺少区域ID参数，请返回列表重新选择');
    }

    if (params.drawingId) {
      this.drawingId = params.drawingId;
      console.log('获取到 drawingId 参数:', params.drawingId);
    } else {
      console.log('未获取到 drawingId 参数');
    }

    // 保存参数，确保刷新后不丢失
    if (params.reginId || params.drawingId) {
      saveRouteParams(this.$router, this.$route, {
        reginId: params.reginId,
        drawingId: params.drawingId
      }, 'deviceBinding_params');
    }

    this.getInit();
  },
  watch: {
    // 监听 Tab 切换
    activeTab(newTab) {
      if (newTab === 'assetDevice' && this.reginId) {
        // 切换到资产设备 Tab 时，重新获取位置ID并加载数据
        this.getBindedLocationIds();
      }
    },

  },
  methods: {
    // 返回上一页
    goBack() {
      // 获取当前页面的参数
      const drawingId = this.drawingId || this.$route.query.drawingId;

      // 使用导航工具返回到区域绑定页面，并传递参数
      if (drawingId) {
        navigateWithParams(this.$router, 'areaBinding', {
          drawingId: drawingId
        });
      } else {
        // 如果没有参数，直接跳转
        this.$router.push('/system/visualOpsManagement/areaBinding');
      }
    },

    openDialog(row) {
      // Handle case where row might be a click event or undefined
      // Check if row is an event object (has type property) or not a valid data object
      if (!row || row instanceof Event || row.type === 'click' || row.constructor.name.includes('Event')) {
        this.currentRow = {};
      } else {
        this.currentRow = row;
      }
      this.dialogVisible = true;
    },
    handleSuccess() {
      this.$message.success('绑定成功');
      // 刷新资产位置列表
      this.$refs.assetLocationGrid.queryData();
      // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID
      this.getBindedLocationIds();
    },
    handleCancel() {
      console.log('用户取消操作');
    },
    refreshList() {
      // 刷新数据列表
    },

    // 初始化数据
    getInit() {
      // 初始化相关数据
    },

    // 获取已绑定的位置ID
    getBindedLocationIds() {
      if (!this.reginId) {
        return;
      }

      this.$api['visualOpsManagement/maintenanceRegionLocationRel-get']({
        reginId: this.reginId
      }).then(res => {
        console.log('获取已绑定位置ID结果:', res);
        if (res && res.length > 0) {
          // 提取位置ID数组
          this.assetDeviceSearchParams.locationIds = res;
        } else {
          this.assetDeviceSearchParams.locationIds = [0];
          console.log('该区域暂无绑定的位置');
        }

        // 获取位置ID后，手动触发资产设备表格数据加载
        this.$nextTick(() => {
          if (this.$refs.assetDeviceGrid) {
            this.$refs.assetDeviceGrid.queryData();
          }
        });
      }).catch(error => {
        console.error('获取已绑定位置ID失败:', error);
        this.assetDeviceSearchParams.locationIds = [0];
        this.$message.error('获取已绑定位置信息失败');

        // 即使失败也要触发表格加载，显示空数据
        this.$nextTick(() => {
          if (this.$refs.assetDeviceGrid) {
            this.$refs.assetDeviceGrid.queryData();
          }
        });
      });
    },

    // 刷新资产设备数据（确保使用最新的位置ID）
    refreshAssetDeviceData() {
      console.log('刷新资产设备数据，重新获取最新的位置ID');
      this.getBindedLocationIds();
    },

    // ========== 资产位置相关方法 ==========
    // 资产位置 - 取消绑定
    handleAssetLocationBind(row) {
      this.$confirm('确认取消绑定该资产位置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用取消绑定接口
        this.$api['visualOpsManagement/assetLocation-delete']({
          id: row.id
        }).then(res => {
          console.log('取消绑定资产位置成功:', res);
          this.$message.success('取消绑定成功');
          // 刷新资产位置表格数据
          this.$refs.assetLocationGrid.queryData();
          // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID
          this.getBindedLocationIds();
        }).catch(error => {
          console.error('取消绑定资产位置失败:', error);
          this.$message.error('取消绑定失败，请稍后重试');
        });
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },

    // 资产位置 - 获取列配置
    getAssetLocationColumn(e) {
      this.assetLocationColumns = e;
      setTimeout(() => {
        this.$refs.assetLocationTable.doLayout();
      }, 100);
    },
    // 资产位置 - 获取表格数据
    getAssetLocationDatas(e) {
      this.assetLocationTableData = e;
    },

    // ========== 资产设备相关方法 ==========
    // 资产设备 - 获取列配置
    getAssetDeviceColumn(e) {
      this.assetDeviceColumns = e;
      setTimeout(() => {
        this.$refs.assetDeviceTable.doLayout();
      }, 100);
    },
    // 资产设备 - 获取表格数据
    getAssetDeviceDatas(e) {
      this.assetDeviceTableData = e;
    },

    // 获取状态中文显示
    getStatusText(status) {
      const statusMap = {
        'DRAFT': '未启用',
        'NORMAL': '正常',
        'BREAKDOWN': '故障',
        1: '未启用',
        2: '正常',
        3: '故障'
      };
      return statusMap[status] || status || '未知';
    }
  }
};
</script>

<style lang="less" scoped>
.el-select {
  width: 100%;
}
/deep/.el-tabs--card>.el-tabs__header{
  margin:0px;
}
</style>
