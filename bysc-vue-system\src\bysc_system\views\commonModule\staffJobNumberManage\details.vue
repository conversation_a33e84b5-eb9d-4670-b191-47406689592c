<template>
    <div>
        <el-drawer size="60%" :title="drawerName" :visible.sync="drawer" :direction="direction" :wrapperClosable="false"
            @close="handleClose">
            <div style="width: 100%; padding: 0 10px">
                <Grid api="systems/financeCostHoursDetail-page" :event-bus="searchEventBus" :search-params="searchForm"
                    :newcolumn="columns" @datas="getDatas" @columnChange="getcolumn" :auto-load="true" ref="dtsGrid">
                    <div slot="search">
                        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                            <el-form-item label="员工">
                                <el-input v-model="searchForm.userRealName" placeholder="请输入" size="small" clearable />
                            </el-form-item>
                            <el-form-item label="工号">
                                <el-input v-model="searchForm.jobNum" placeholder="请输入" size="small" clearable />
                            </el-form-item>
                            <el-form-item label="项目名称">
                                <el-input v-model.trim="searchForm.projectName" size="small" placeholder="请输入" clearable></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button size="small" type="primary" style="margin: 0 0 0 10px"
                                    @click="searchTable">搜索</el-button>
                                <el-button size="small" @click="resetTable">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div slot="action">
                        <el-button v-permission="'staffJobNumberManage_dts_add'" size="small" type="primary"
                            @click="handleEdit(0)">新增</el-button>
                    </div>
                    <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe
                        ref="dtstable" style="width: 100%">
                        <el-table-column fixed="left" label="序号" type="index" width="50">
                        </el-table-column>
                        <template v-for="(item, index) in columns">
                            <el-table-column :show-overflow-tooltip="true" :key="item.key" :prop="item.key"
                                :label="item.title" :min-width="item.minWidth ? item.minWidth : '150'"
                                :align="item.align ? item.align : 'center'">
                            </el-table-column>
                        </template>
                        <el-table-column fixed="right" align="center" label="操作" type="action" width="180">
                            <template slot-scope="scope">
                                <el-button v-permission="'staffJobNumberManage_dts_edit'" @click="handleEdit(1, scope.row)"
                                    type="text" size="small">编辑</el-button>
                                <el-button v-permission="'staffJobNumberManage_dts_del'" type="text" size="small"
                                    @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </Grid>
            </div>
        </el-drawer>
        <el-dialog :title="dialogTitle" :visible.sync="showDialog" :close-on-click-modal="false" width="40%">
            <el-form :model="dialogForm" :rules="dialogRules" ref="dialogForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="员工姓名" prop="userId">
                    <el-select v-model="dialogForm.userId" placeholder="请选择" clearable filterable size="small">
                        <el-option v-for="(item, index) in userList" :key="item.key" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工号" prop="jobId">
                    <el-select v-model="dialogForm.jobId" placeholder="请选择" clearable filterable size="small"
                        @change="changeJobId">
                        <el-option v-for="(item, index) in jobNumberList" :key="index" :label="item.jobNum"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工时(小时)" prop="workingHours">
                    <el-input type="number" v-model.trim="dialogForm.workingHours" size="small"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="项目名称" prop="projectName">
                    <el-input disabled v-model.trim="dialogForm.projectName" size="small"
                        placeholder="选择工号后自动代入无需填写"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showDialog = false">取消</el-button>
                <el-button size="small" type="primary" :loading="okLoading" @click="submitForm">提交</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {
    costMonthlyId: ''
};
const defaultDialogForm = {}
export default {
    components: {
        Grid,
    },
    destroyed() {
        this.searchEventBus.$off();
    },
    data() {
        this.searchEventBus = new Vue();
        return {
            okLoading: false,
            dialogRules: {
                userId: [
                    { required: true, message: '请选择员工', trigger: 'blur,change' },
                ],
                jobId: [
                    { required: true, message: '请选择工号', trigger: 'blur,change' },
                ],
                workingHours: [
                    { required: true, message: '请输入工时', trigger: 'blur' },
                    {
                        pattern: /^([0-9]*[1-9][0-9]*(\.[0-9]+)?|[0]+\.[0-9]*[1-9][0-9]*)$/,
                        message: '请输入大于0的数字',
                        trigger: 'blur'
                    }
                ],
            },
            showDialog: false,
            searchForm: _.cloneDeep(defaultSearchForm),
            columns: [
                {
                    title: '员工',
                    key: 'userRealName',
                    tooltip: true,
                    minWidth: 150,
                },
                {
                    title: '工号',
                    key: 'jobNum',
                    tooltip: true,
                    minWidth: 150,
                },
                {
                    title: '项目名称',
                    key: 'projectName',
                    tooltip: true,
                    minWidth: 150,
                },
                {
                    title: '工时(小时)',
                    key: 'workingHours',
                    tooltip: true,
                    minWidth: 150,
                }
            ],
            tableData: [],
            dialogForm: _.cloneDeep(defaultDialogForm),
            dialogTitle: '新增',
            drawer: false,
            drawerName: '员工详情',
            direction: 'rtl',
            userList: [],
            jobNumberList: [],
        };
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        id: String,
        rowItem: Object
    },
    watch: {
        'show': {
            handler(n) {
                this.drawer = n
                if (n) {
                    this.searchForm.costMonthlyId = this.rowItem.id
                    this.$nextTick(() => {
                        this.$refs.dtsGrid.query();
                    });
                }
            },
            // immediate: true
        },
        'rowItem': {
            handler(n) {
                console.log(n)
                this.getUserList()
                this.getJobNumberList()
                // this.searchForm.costMonthlyId = n.id
                // this.$nextTick(() => {
                //     this.$refs.dtsGrid.queryData();
                // });
            },
            // immediate: true,
            deep: true
        }
    },
    mounted() {
    },
    methods: {
        // 查询工号列表
        getJobNumberList() {
            this.$api['systems/costProject-list']({
                costCenterId: this.rowItem.costCenterId
            }).then(data => {
                this.jobNumberList = data
            });
        },
        // 员工列表
        getUserList() {
            this.$api['systems/financeCostMonthly-getUser']({
                costMonthlyId: this.rowItem.id
            }).then(data => {
                this.userList = data
            });
        },
        changeJobId(e) {
            console.log(e)
            this.jobNumberList.map(item => {
                if (item.id == e) {
                    this.dialogForm.projectName = item.projectName
                    return
                }
            })
        },
        handleClose() {
            this.drawer = false
            this.$emit('close')
        },
        async handleEdit(type, row) {
            this.dialogTitle = type == 0 ? '新增' : '编辑'
            if (type == 0) {
                this.dialogForm = _.cloneDeep(defaultDialogForm)
            }
            else {
                await this.$api['systems/financeCostHoursDetail-dts']({ id: row.id }).then(data => {
                    this.dialogForm = _.cloneDeep(data)

                });
            }
            this.showDialog = true;
            this.$nextTick(() => {
                this.$refs.dialogForm.clearValidate()
            });
        },
        handleDelete(e) {
            this.$confirm('确定删除该数据？', '删除提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api['systems/financeCostHoursDetail-del']({ id: e.id }).then(data => {
                    this.$message({
                        type: 'success',
                        message: '删除成功',
                    });
                    this.resetTable();
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        submitForm() {
            this.okLoading = true;
            this.$refs.dialogForm.validate(valid => {
                if (valid) {
                    this.dialogForm.costMonthlyId = this.rowItem.id
                    this.$api['systems/financeCostHoursDetail-save'](this.dialogForm).then(data => {
                        this.okLoading = false;
                        this.$message({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.resetTable();
                        this.showDialog = false
                    }).catch(() => {
                        this.okLoading = false;
                    });
                } else {
                    this.okLoading = false;
                    return false;
                }
            });
        },
        searchTable() {
            this.$refs.dtsGrid.queryData();
        },
        resetTable() {
            this.searchForm = _.cloneDeep(defaultSearchForm);
            this.searchForm.costMonthlyId = this.rowItem.id
            this.$nextTick(() => {
                this.$refs.dtsGrid.query();
            });
        },
        getcolumn(e) {
            this.columns = e;
            setTimeout(() => {
                this.$refs.dtstable.doLayout();
            }, 100);
        },
        getDatas(e) {
            this.tableData = e;
        },
    },
};
</script>
<style lang="less" scoped>
.el-select {
    width: 100%;
}
</style>