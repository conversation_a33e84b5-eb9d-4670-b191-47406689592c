<template>
  <div class="table-container">
    <Grid
      api="techDashboard/sysSecurityControlRating-page"
      :event-bus="searchEventBus"
      :search-params="searchForm"
      :newcolumn="columns"
      @datas="getDatas"
      @columnChange="getcolumn"
      ref="grid"
    >
      <div slot="search">
        <!-- <el-date-picker
          style="width: 200px; margin: 0 10px 0 0"
          v-model="searchForm.date"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择月份"
        ></el-date-picker>
        <el-button
          size="small"
          type="primary"
          style="margin: 0 0 0 10px"
          @click="searchTable"
        >搜索</el-button>
        <el-button size="small" @click="resetTable">重置</el-button> -->
      </div>

      <el-table
        slot="table"
        slot-scope="{ loading }"
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :border="true"
      >
        <el-table-column
          fixed="left"
          :align="'center'"
          label="序号"
          type="index"
          width="50"
        >
        </el-table-column>
        <el-table-column
          v-for="item in columns"
          :key="item.key"
          :show-overflow-tooltip="true"
          :prop="item.key"
          :label="item.title"
          :min-width="item.width ? item.width : '150'"
          :align="item.align ? item.align : 'center'"
        >
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="100"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              v-permission="'daxintechEditBtn'"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </Grid>

    <Panel ref="panel" @refresh="searchTable" />
  </div>
</template>

<script>
import Vue from 'vue';
import _ from 'lodash';
import Grid from "@/components/Grid";
import Panel from './component/panel.vue';

const defaultSearchForm = {
  securityType: 2,
};

export default {
  name: 'CapitalSafetyTable',
  components: {
    Grid,
    Panel
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      tableData: [],
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: "日期",
          key: "securityDate",
          width: "150",
        },
        {
          title: "总分值",
          key: "totalScore",
          width: "150",
        }
      ]
    };
  },
  methods: {
    // 编辑操作
    handleEdit(row) {
      console.log(row, '---');

      this.$refs.panel.open(row, 'edit');
    },

    // 搜索表格
    searchTable() {
      this.$refs.grid.query();
    },

    // 重置表格
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },

    // 获取列配置
    getcolumn(e) {
      this.columns = e;
    },

    // 获取表格数据
    getDatas(e) {
      this.tableData = e;
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
</style>