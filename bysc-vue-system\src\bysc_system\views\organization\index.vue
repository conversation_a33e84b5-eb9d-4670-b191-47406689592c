<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 10:11:20
 * @FilePath: \kdsp_vue_clear\src\systemView\views\menus\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="6">
        <common-tree
          @treeNode="getClickTreeNode"
          :tree-props="treeProps"
          :tree-data="treedata"
        ></common-tree>
      </el-col>
      <el-col :span="18">
        <Grid
          api="systems/organizationPage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-input
              style="width: 200px;"
              v-model.trim="searchForm.organizationName"
              size="small"
              placeholder="请输入组织名称"
            ></el-input>
            <el-button size="small" type="primary" style="margin:0 0 0 10px;" @click="searchTable">搜索</el-button>
            <el-button size="small" @click="resetTable">重置</el-button>
          </div>
          <div slot="action">
            <el-button v-permission="'organization_add'" size="small" type="primary" @click="handleAdd"
              >添加</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading"  :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">{{
                  scope.row[item.slot]
                }}</template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'organization_edit'"
                  style="margin-right:6px"
                  @click="handleRdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-popconfirm
                @confirm="handleDelete(scope.row.id)"
                  title="您确定要删除该组织吗？"
                >
                  <el-button v-permission="'organization_del'" type="text" size="small" slot="reference">删除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="组织编码" prop="organizationKey">
            <el-input
              size="small"
              placeholder="请输入组织编码"
              minlength="2"
              maxlength="32"
              v-model.trim="ruleForm.organizationKey"
            ></el-input>
          </el-form-item>
          <el-form-item label="组织名称" prop="organizationName">
            <el-input
              size="small"
              placeholder="请输入组织名称"
              minlength="2"
              maxlength="32"
              v-model.trim="ruleForm.organizationName"
            ></el-input>
          </el-form-item>
          <el-form-item label="父组织" prop="parentName">
            <el-input
              style="width: 250px"
              size="small"
              disabled
              v-model.trim="ruleForm.parentName"
            ></el-input>
            <el-button
              size="small"
              style="margin-left: 10px"
              type="primary"
              @click="dialogVisible = true"
              >选择父组织</el-button
            >
            <el-button
              size="small"
              style="margin-left: 10px"
              @click="clearParent"
              >清空父组织</el-button
            >
          </el-form-item>
          <el-form-item label="组织简介" prop="comments">
            <el-input
              size="small"
              placeholder="请输入组织简介"
              maxlength="200"
              type="textarea"
              v-model.trim="ruleForm.comments"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              size="small"
              type="primary"
              @click="submitForm('ruleForm')"
              >保存</el-button
            >
            <el-button size="small" @click="resetForm('ruleForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <el-dialog title="父组织" :visible.sync="dialogVisible" width="30%">
      <div>
        <common-tree
          @treeNode="getSelectTreeNode"
          :tree-props="treeProps"
          :highlightcurrent="true"
          :tree-data="organizationtreedata"
        ></common-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="setParentInfo">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import commonTree from '@/components/treeComp/commonTree';
import iconChoose from '@/components/choose/icon-choose';
const defaultSearchForm = {};
const defaultForm = {};
export default {
  name: 'dept',
  components: {commonTree, Grid, iconChoose},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      dialogVisible: false,
      status: true,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        organizationKey: [
          {required: true, message: '请输入组织编码', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
        organizationName: [
          {required: true, message: '请输入组织名称', trigger: 'change,blur'},
          {
            min: 1,
            max: 32,
            message: '长度在 1 到 32 之间',
            trigger: 'change,blur',
          },
        ],
      },
      drawerName: '添加',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      treeProps: {
        children: 'children',
        label: 'organizationName',
      },
      columns: [
        {
          title: '组织编码',
          key: 'organizationKey',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '组织名称',
          key: 'organizationName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '父组织',
          key: 'parentName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '最后更新时间',
          key: 'updateTime',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
      ],
      treedata: [],
      tableData: [],
      organizationtreedata: [],
      selectedResouce: {},
    };
  },
  watch: {
    status(val) {
      this.ruleForm.status = val ? 1 : 0;
    }
  },
  mounted() {
    this.getTrees();
  },

  methods: {
    handleAdd() {
      this.drawer = true;
      this.drawerName = '添加';
      this.ruleForm = _.cloneDeep(defaultForm);
    },
    handleRdit(row) {
      this.ruleForm = Object.assign({}, row);
      this.drawer = true;
      this.drawerName = '编辑';
      // });
    },
    getParentName(arr, param) {
      let name = '';
      arr.forEach(e => {
        if (e.children) {
          if (e.id == param) {
            name = e.organizationName;
          }
          this.getParentName(e.children, param);
        } else {
          if (e.id == param) {
            name = e.organizationName;
          }
        }
      });
      return name;
    },
    handleDelete(e) {
      this.$api['systems/organizationDelete']({id: e}).then(data => {
        this.$refs.grid.query();
        this.getTrees();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    setParentInfo() {
      this.ruleForm.parentId = this.selectedResouce.id;
      this.ruleForm.parentName = this.selectedResouce.organizationName;
      this.$refs.grid.query();
      this.getTrees();
      this.dialogVisible = false;
    },
    getSelectTreeNode(e) {
      this.selectedResouce = e;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$api['systems/addOrganization'](this.ruleForm).then(data => {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            this.drawer = false;
            this.$refs.grid.query();
            this.getTrees();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.searchForm.parentId = null;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    getClickTreeNode(e) {
      this.searchForm.parentId = this.searchForm.parentId == e.id ? null : e.id;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    clearParent() {
      this.ruleForm.parentId = null;
      this.ruleForm.parentName = null;
    },
    getTrees() {
      this.$api['systems/organizationTree']({parentId: 0}).then(data => {
        this.treedata = data;
        this.organizationtreedata = data;
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
