{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue", "mtime": 1754276220638}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.string.includes\";\nimport Vue from 'vue';\nimport Grid from '@/components/Grid';\nimport BindPositionDialog from \"./component/BindPositionDialog\";\nimport { getRouteParams, saveRouteParams, navigateWithParams } from '@/utils/routeParams';\nexport default {\n  name: 'DeviceBinding',\n  components: {\n    Grid: Grid,\n    BindPositionDialog: BindPositionDialog\n  },\n  destroyed: function destroyed() {\n    this.assetLocationSearchEventBus.$off();\n    this.assetDeviceSearchEventBus.$off();\n  },\n  data: function data() {\n    this.assetLocationSearchEventBus = new Vue();\n    this.assetDeviceSearchEventBus = new Vue();\n    return {\n      dialogVisible: false,\n      currentRow: {},\n      activeTab: 'assetLocation',\n      // 当前激活的Tab\n      reginId: '',\n      // 区域ID\n      drawingId: '',\n      // 图纸ID\n\n      // 资产位置搜索参数\n      assetLocationSearchParams: {\n        reginId: ''\n      },\n      // 资产设备搜索参数\n      assetDeviceSearchParams: {\n        locationIds: [] // 已绑定的位置ID数组，每次获取资产设备时都会传递最新的位置ID\n      },\n      // 资产位置相关数据\n      assetLocationColumns: [{\n        title: '资产编号',\n        key: 'locationCode',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '资产位置',\n        key: 'locationDesc',\n        tooltip: true,\n        minWidth: 150\n      }],\n      assetLocationTableData: [],\n      // 资产设备相关数据\n      assetDeviceColumns: [{\n        title: '资产编号',\n        key: 'assetCode',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '状态',\n        key: 'recordStatus',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '所属一线部门',\n        key: 'firstDeptName',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '资产描述',\n        key: 'assetDesc',\n        tooltip: true,\n        minWidth: 100\n      }, {\n        title: '规格',\n        key: 'specification',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '是否主设备',\n        key: 'mainDeviceName',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '关联主设备',\n        key: 'relatedMainDeviceCode',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '位置编码',\n        key: 'locationCode',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '位置描述',\n        key: 'locationDesc',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '安装位置',\n        key: 'installationLocation',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '创建时间',\n        key: 'createTime',\n        tooltip: true,\n        minWidth: 150\n      }],\n      assetDeviceTableData: []\n    };\n  },\n  created: function created() {\n    // 使用工具函数获取参数\n    var params = getRouteParams(this.$route, ['reginId', 'drawingId'], 'deviceBinding_params');\n    if (params.reginId) {\n      this.reginId = params.reginId;\n      this.assetLocationSearchParams.reginId = params.reginId;\n      console.log('获取到 reginId 参数:', params.reginId);\n\n      // 获取已绑定的位置ID\n      this.getBindedLocationIds();\n    } else {\n      console.log('未获取到 reginId 参数');\n      this.$message.warning('缺少区域ID参数，请返回列表重新选择');\n    }\n    if (params.drawingId) {\n      this.drawingId = params.drawingId;\n      console.log('获取到 drawingId 参数:', params.drawingId);\n    } else {\n      console.log('未获取到 drawingId 参数');\n    }\n\n    // 保存参数，确保刷新后不丢失\n    if (params.reginId || params.drawingId) {\n      saveRouteParams(this.$router, this.$route, {\n        reginId: params.reginId,\n        drawingId: params.drawingId\n      }, 'deviceBinding_params');\n    }\n    this.getInit();\n  },\n  watch: {\n    // 监听 Tab 切换\n    activeTab: function activeTab(newTab) {\n      if (newTab === 'assetDevice' && this.reginId) {\n        // 切换到资产设备 Tab 时，重新获取位置ID并加载数据\n        this.getBindedLocationIds();\n      }\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack: function goBack() {\n      // 获取当前页面的参数\n      var drawingId = this.drawingId || this.$route.query.drawingId;\n\n      // 使用导航工具返回到区域绑定页面，并传递参数\n      if (drawingId) {\n        navigateWithParams(this.$router, 'areaBinding', {\n          drawingId: drawingId\n        });\n      } else {\n        // 如果没有参数，直接跳转\n        this.$router.push('/system/visualOpsManagement/areaBinding');\n      }\n    },\n    openDialog: function openDialog(row) {\n      // Handle case where row might be a click event or undefined\n      // Check if row is an event object (has type property) or not a valid data object\n      if (!row || row instanceof Event || row.type === 'click' || row.constructor.name.includes('Event')) {\n        this.currentRow = {};\n      } else {\n        this.currentRow = row;\n      }\n      this.dialogVisible = true;\n    },\n    handleSuccess: function handleSuccess() {\n      this.$message.success('绑定成功');\n      // 刷新资产位置列表\n      this.$refs.assetLocationGrid.queryData();\n      // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\n      this.getBindedLocationIds();\n    },\n    handleCancel: function handleCancel() {\n      console.log('用户取消操作');\n    },\n    refreshList: function refreshList() {\n      // 刷新数据列表\n    },\n    // 初始化数据\n    getInit: function getInit() {\n      // 初始化相关数据\n    },\n    // 获取已绑定的位置ID\n    getBindedLocationIds: function getBindedLocationIds() {\n      var _this = this;\n      if (!this.reginId) {\n        return;\n      }\n      this.$api['visualOpsManagement/maintenanceRegionLocationRel-get']({\n        reginId: this.reginId\n      }).then(function (res) {\n        console.log('获取已绑定位置ID结果:', res);\n        if (res && res.length > 0) {\n          // 提取位置ID数组\n          _this.assetDeviceSearchParams.locationIds = res;\n        } else {\n          _this.assetDeviceSearchParams.locationIds = [0];\n          console.log('该区域暂无绑定的位置');\n        }\n\n        // 获取位置ID后，手动触发资产设备表格数据加载\n        _this.$nextTick(function () {\n          if (_this.$refs.assetDeviceGrid) {\n            _this.$refs.assetDeviceGrid.queryData();\n          }\n        });\n      }).catch(function (error) {\n        console.error('获取已绑定位置ID失败:', error);\n        _this.assetDeviceSearchParams.locationIds = [0];\n        _this.$message.error('获取已绑定位置信息失败');\n\n        // 即使失败也要触发表格加载，显示空数据\n        _this.$nextTick(function () {\n          if (_this.$refs.assetDeviceGrid) {\n            _this.$refs.assetDeviceGrid.queryData();\n          }\n        });\n      });\n    },\n    // 刷新资产设备数据（确保使用最新的位置ID）\n    refreshAssetDeviceData: function refreshAssetDeviceData() {\n      console.log('刷新资产设备数据，重新获取最新的位置ID');\n      this.getBindedLocationIds();\n    },\n    // ========== 资产位置相关方法 ==========\n    // 资产位置 - 取消绑定\n    handleAssetLocationBind: function handleAssetLocationBind(row) {\n      var _this2 = this;\n      this.$confirm('确认取消绑定该资产位置吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        // 调用取消绑定接口\n        _this2.$api['visualOpsManagement/assetLocation-delete']({\n          id: row.id\n        }).then(function (res) {\n          console.log('取消绑定资产位置成功:', res);\n          _this2.$message.success('取消绑定成功');\n          // 刷新资产位置表格数据\n          _this2.$refs.assetLocationGrid.queryData();\n          // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\n          _this2.getBindedLocationIds();\n        }).catch(function (error) {\n          console.error('取消绑定资产位置失败:', error);\n          _this2.$message.error('取消绑定失败，请稍后重试');\n        });\n      }).catch(function () {\n        _this2.$message.info('已取消操作');\n      });\n    },\n    // 资产位置 - 获取列配置\n    getAssetLocationColumn: function getAssetLocationColumn(e) {\n      var _this3 = this;\n      this.assetLocationColumns = e;\n      setTimeout(function () {\n        _this3.$refs.assetLocationTable.doLayout();\n      }, 100);\n    },\n    // 资产位置 - 获取表格数据\n    getAssetLocationDatas: function getAssetLocationDatas(e) {\n      this.assetLocationTableData = e;\n    },\n    // ========== 资产设备相关方法 ==========\n    // 资产设备 - 获取列配置\n    getAssetDeviceColumn: function getAssetDeviceColumn(e) {\n      var _this4 = this;\n      this.assetDeviceColumns = e;\n      setTimeout(function () {\n        _this4.$refs.assetDeviceTable.doLayout();\n      }, 100);\n    },\n    // 资产设备 - 获取表格数据\n    getAssetDeviceDatas: function getAssetDeviceDatas(e) {\n      this.assetDeviceTableData = e;\n    },\n    // 获取状态中文显示\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        'DRAFT': '未启用',\n        'NORMAL': '正常',\n        'BREAKDOWN': '故障',\n        1: '未启用',\n        2: '正常',\n        3: '故障'\n      };\n      return statusMap[status] || status || '未知';\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Grid", "BindPositionDialog", "getRouteParams", "saveRouteParams", "navigateWithParams", "name", "components", "destroyed", "assetLocationSearchEventBus", "$off", "assetDeviceSearchEventBus", "data", "dialogVisible", "currentRow", "activeTab", "reginId", "drawingId", "assetLocationSearchParams", "assetDeviceSearchParams", "locationIds", "assetLocationColumns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "assetLocationTableData", "assetDeviceColumns", "assetDeviceTableData", "created", "params", "$route", "console", "log", "getBindedLocationIds", "$message", "warning", "$router", "getInit", "watch", "newTab", "methods", "goBack", "query", "push", "openDialog", "row", "Event", "type", "constructor", "includes", "handleSuccess", "success", "$refs", "assetLocationGrid", "queryData", "handleCancel", "refreshList", "_this", "$api", "then", "res", "length", "$nextTick", "assetDeviceGrid", "catch", "error", "refreshAssetDeviceData", "handleAssetLocationBind", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "info", "getAssetLocationColumn", "e", "_this3", "setTimeout", "assetLocationTable", "doLayout", "getAssetLocationDatas", "getAssetDeviceColumn", "_this4", "assetDeviceTable", "getAssetDeviceDatas", "getStatusText", "status", "statusMap"], "sources": ["src/bysc_system/views/visualOpsManagement/deviceBinding/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮区域 -->\r\n    <div style=\"margin-bottom: 16px;\">\r\n      <el-button size=\"small\" @click=\"goBack\">\r\n        <i class=\"el-icon-arrow-left\"></i> 返回\r\n      </el-button>\r\n      <el-button v-permission=\"'assetLocation_add'\" type=\"primary\" size=\"small\" @click=\"openDialog\" style=\"margin-left: 10px;\">\r\n        <i class=\"el-icon-plus\"></i> 绑定资产位置\r\n      </el-button>\r\n    </div>\r\n\r\n    <el-tabs v-model=\"activeTab\" type=\"card\">\r\n      <!-- 资产位置 Tab -->\r\n      <el-tab-pane label=\"资产位置\" name=\"assetLocation\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <Grid\r\n              api=\"visualOpsManagement/assetLocation-page\"\r\n              :event-bus=\"assetLocationSearchEventBus\"\r\n              :search-params=\"assetLocationSearchParams\"\r\n              :newcolumn=\"assetLocationColumns\"\r\n              @datas=\"getAssetLocationDatas\"\r\n              @columnChange=\"getAssetLocationColumn\"\r\n              :auto-load=\"true\"\r\n              ref=\"assetLocationGrid\">\r\n              <el-table\r\n                slot=\"table\"\r\n                slot-scope=\"{loading}\"\r\n                v-loading=\"loading\"\r\n                :data=\"assetLocationTableData\"\r\n                stripe\r\n                ref=\"assetLocationTable\"\r\n                style=\"width: 100%\">\r\n                <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n                </el-table-column>\r\n                <template v-for=\"(item, index) in assetLocationColumns\">\r\n                  <el-table-column\r\n                    v-if=\"item.key == 'belongDept'\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :align=\"item.align ? item.align : 'center'\"\r\n                    :key=\"index\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div>\r\n                        {{ scope.row.belongDept || '未分配' }}\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    v-else\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :key=\"item.key\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                    :align=\"item.align ? item.align : 'center'\">\r\n                  </el-table-column>\r\n                </template>\r\n                <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"120\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button  type=\"text\" size=\"small\" @click=\"handleAssetLocationBind(scope.row)\">取消绑定</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </Grid>\r\n          </el-col>\r\n        </el-row>\r\n      </el-tab-pane>\r\n\r\n      <!-- 资产设备 Tab -->\r\n      <el-tab-pane label=\"资产设备\" name=\"assetDevice\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <Grid\r\n              api=\"visualOpsManagement/assetDevice-page\"\r\n              :event-bus=\"assetDeviceSearchEventBus\"\r\n              :search-params=\"assetDeviceSearchParams\"\r\n              :newcolumn=\"assetDeviceColumns\"\r\n              @datas=\"getAssetDeviceDatas\"\r\n              @columnChange=\"getAssetDeviceColumn\"\r\n              :auto-load=\"false\"\r\n              ref=\"assetDeviceGrid\">\r\n              <el-table\r\n                slot=\"table\"\r\n                slot-scope=\"{loading}\"\r\n                v-loading=\"loading\"\r\n                :data=\"assetDeviceTableData\"\r\n                stripe\r\n                ref=\"assetDeviceTable\"\r\n                style=\"width: 100%\">\r\n                <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n                </el-table-column>\r\n                <template v-for=\"(item, index) in assetDeviceColumns\">\r\n                  <el-table-column\r\n                    v-if=\"item.key == 'recordStatus'\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :align=\"item.align ? item.align : 'center'\"\r\n                    :key=\"index\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div>\r\n                        {{ getStatusText(scope.row.recordStatus) }}\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    v-else\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :key=\"item.key\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                    :align=\"item.align ? item.align : 'center'\">\r\n                  </el-table-column>\r\n                </template>\r\n              </el-table>\r\n            </Grid>\r\n          </el-col>\r\n        </el-row>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n\r\n      <!-- 绑定位置弹窗 -->\r\n    <BindPositionDialog\r\n      :visible.sync=\"dialogVisible\"\r\n      :row-data=\"currentRow\"\r\n      :regin-id=\"reginId\"\r\n      :bound-location-ids=\"assetDeviceSearchParams.locationIds\"\r\n      @success=\"handleSuccess\"\r\n      @cancel=\"handleCancel\"\r\n    />\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport BindPositionDialog from './component/BindPositionDialog';\r\nimport {getRouteParams, saveRouteParams, navigateWithParams} from '@/utils/routeParams';\r\n\r\n\r\n\r\nexport default {\r\n  name: 'DeviceBinding',\r\n  components: {\r\n    Grid,\r\n    BindPositionDialog\r\n  },\r\n  destroyed() {\r\n    this.assetLocationSearchEventBus.$off();\r\n    this.assetDeviceSearchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.assetLocationSearchEventBus = new Vue();\r\n    this.assetDeviceSearchEventBus = new Vue();\r\n\r\n    return {\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      activeTab: 'assetLocation', // 当前激活的Tab\r\n      reginId: '', // 区域ID\r\n      drawingId: '', // 图纸ID\r\n\r\n      // 资产位置搜索参数\r\n      assetLocationSearchParams: {\r\n        reginId: ''\r\n      },\r\n      // 资产设备搜索参数\r\n      assetDeviceSearchParams: {\r\n        locationIds: [] // 已绑定的位置ID数组，每次获取资产设备时都会传递最新的位置ID\r\n      },\r\n\r\n      // 资产位置相关数据\r\n      assetLocationColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产位置',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetLocationTableData: [],\r\n\r\n      // 资产设备相关数据\r\n      assetDeviceColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'assetCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'recordStatus',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '所属一线部门',\r\n          key: 'firstDeptName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产描述',\r\n          key: 'assetDesc',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '规格',\r\n          key: 'specification',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '是否主设备',\r\n          key: 'mainDeviceName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '关联主设备',\r\n          key: 'relatedMainDeviceCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置编码',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置描述',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '安装位置',\r\n          key: 'installationLocation',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetDeviceTableData: []\r\n    };\r\n  },\r\n  created() {\r\n    // 使用工具函数获取参数\r\n    const params = getRouteParams(this.$route, ['reginId', 'drawingId'], 'deviceBinding_params');\r\n\r\n    if (params.reginId) {\r\n      this.reginId = params.reginId;\r\n      this.assetLocationSearchParams.reginId = params.reginId;\r\n      console.log('获取到 reginId 参数:', params.reginId);\r\n\r\n      // 获取已绑定的位置ID\r\n      this.getBindedLocationIds();\r\n    } else {\r\n      console.log('未获取到 reginId 参数');\r\n      this.$message.warning('缺少区域ID参数，请返回列表重新选择');\r\n    }\r\n\r\n    if (params.drawingId) {\r\n      this.drawingId = params.drawingId;\r\n      console.log('获取到 drawingId 参数:', params.drawingId);\r\n    } else {\r\n      console.log('未获取到 drawingId 参数');\r\n    }\r\n\r\n    // 保存参数，确保刷新后不丢失\r\n    if (params.reginId || params.drawingId) {\r\n      saveRouteParams(this.$router, this.$route, {\r\n        reginId: params.reginId,\r\n        drawingId: params.drawingId\r\n      }, 'deviceBinding_params');\r\n    }\r\n\r\n    this.getInit();\r\n  },\r\n  watch: {\r\n    // 监听 Tab 切换\r\n    activeTab(newTab) {\r\n      if (newTab === 'assetDevice' && this.reginId) {\r\n        // 切换到资产设备 Tab 时，重新获取位置ID并加载数据\r\n        this.getBindedLocationIds();\r\n      }\r\n    },\r\n\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      // 获取当前页面的参数\r\n      const drawingId = this.drawingId || this.$route.query.drawingId;\r\n\r\n      // 使用导航工具返回到区域绑定页面，并传递参数\r\n      if (drawingId) {\r\n        navigateWithParams(this.$router, 'areaBinding', {\r\n          drawingId: drawingId\r\n        });\r\n      } else {\r\n        // 如果没有参数，直接跳转\r\n        this.$router.push('/system/visualOpsManagement/areaBinding');\r\n      }\r\n    },\r\n\r\n    openDialog(row) {\r\n      // Handle case where row might be a click event or undefined\r\n      // Check if row is an event object (has type property) or not a valid data object\r\n      if (!row || row instanceof Event || row.type === 'click' || row.constructor.name.includes('Event')) {\r\n        this.currentRow = {};\r\n      } else {\r\n        this.currentRow = row;\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    handleSuccess() {\r\n      this.$message.success('绑定成功');\r\n      // 刷新资产位置列表\r\n      this.$refs.assetLocationGrid.queryData();\r\n      // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n      this.getBindedLocationIds();\r\n    },\r\n    handleCancel() {\r\n      console.log('用户取消操作');\r\n    },\r\n    refreshList() {\r\n      // 刷新数据列表\r\n    },\r\n\r\n    // 初始化数据\r\n    getInit() {\r\n      // 初始化相关数据\r\n    },\r\n\r\n    // 获取已绑定的位置ID\r\n    getBindedLocationIds() {\r\n      if (!this.reginId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['visualOpsManagement/maintenanceRegionLocationRel-get']({\r\n        reginId: this.reginId\r\n      }).then(res => {\r\n        console.log('获取已绑定位置ID结果:', res);\r\n        if (res && res.length > 0) {\r\n          // 提取位置ID数组\r\n          this.assetDeviceSearchParams.locationIds = res;\r\n        } else {\r\n          this.assetDeviceSearchParams.locationIds = [0];\r\n          console.log('该区域暂无绑定的位置');\r\n        }\r\n\r\n        // 获取位置ID后，手动触发资产设备表格数据加载\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        console.error('获取已绑定位置ID失败:', error);\r\n        this.assetDeviceSearchParams.locationIds = [0];\r\n        this.$message.error('获取已绑定位置信息失败');\r\n\r\n        // 即使失败也要触发表格加载，显示空数据\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    // 刷新资产设备数据（确保使用最新的位置ID）\r\n    refreshAssetDeviceData() {\r\n      console.log('刷新资产设备数据，重新获取最新的位置ID');\r\n      this.getBindedLocationIds();\r\n    },\r\n\r\n    // ========== 资产位置相关方法 ==========\r\n    // 资产位置 - 取消绑定\r\n    handleAssetLocationBind(row) {\r\n      this.$confirm('确认取消绑定该资产位置吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 调用取消绑定接口\r\n        this.$api['visualOpsManagement/assetLocation-delete']({\r\n          id: row.id\r\n        }).then(res => {\r\n          console.log('取消绑定资产位置成功:', res);\r\n          this.$message.success('取消绑定成功');\r\n          // 刷新资产位置表格数据\r\n          this.$refs.assetLocationGrid.queryData();\r\n          // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n          this.getBindedLocationIds();\r\n        }).catch(error => {\r\n          console.error('取消绑定资产位置失败:', error);\r\n          this.$message.error('取消绑定失败，请稍后重试');\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 资产位置 - 获取列配置\r\n    getAssetLocationColumn(e) {\r\n      this.assetLocationColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetLocationTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产位置 - 获取表格数据\r\n    getAssetLocationDatas(e) {\r\n      this.assetLocationTableData = e;\r\n    },\r\n\r\n    // ========== 资产设备相关方法 ==========\r\n    // 资产设备 - 获取列配置\r\n    getAssetDeviceColumn(e) {\r\n      this.assetDeviceColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetDeviceTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产设备 - 获取表格数据\r\n    getAssetDeviceDatas(e) {\r\n      this.assetDeviceTableData = e;\r\n    },\r\n\r\n    // 获取状态中文显示\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'DRAFT': '未启用',\r\n        'NORMAL': '正常',\r\n        'BREAKDOWN': '故障',\r\n        1: '未启用',\r\n        2: '正常',\r\n        3: '故障'\r\n      };\r\n      return statusMap[status] || status || '未知';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.el-select {\r\n  width: 100%;\r\n}\r\n/deep/.el-tabs--card>.el-tabs__header{\r\n  margin:0px;\r\n}\r\n</style>\r\n"], "mappings": ";;;AA8IA,OAAAA,GAAA;AACA,OAAAC,IAAA;AACA,OAAAC,kBAAA;AACA,SAAAC,cAAA,EAAAC,eAAA,EAAAC,kBAAA;AAIA;EACAC,IAAA;EACAC,UAAA;IACAN,IAAA,EAAAA,IAAA;IACAC,kBAAA,EAAAA;EACA;EACAM,SAAA,WAAAA,UAAA;IACA,KAAAC,2BAAA,CAAAC,IAAA;IACA,KAAAC,yBAAA,CAAAD,IAAA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA,KAAAH,2BAAA,OAAAT,GAAA;IACA,KAAAW,yBAAA,OAAAX,GAAA;IAEA;MACAa,aAAA;MACAC,UAAA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,SAAA;MAAA;;MAEA;MACAC,yBAAA;QACAF,OAAA;MACA;MACA;MACAG,uBAAA;QACAC,WAAA;MACA;MAEA;MACAC,oBAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAC,sBAAA;MAEA;MACAC,kBAAA,GACA;QACAL,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAG,oBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,MAAA,GAAA3B,cAAA,MAAA4B,MAAA;IAEA,IAAAD,MAAA,CAAAd,OAAA;MACA,KAAAA,OAAA,GAAAc,MAAA,CAAAd,OAAA;MACA,KAAAE,yBAAA,CAAAF,OAAA,GAAAc,MAAA,CAAAd,OAAA;MACAgB,OAAA,CAAAC,GAAA,oBAAAH,MAAA,CAAAd,OAAA;;MAEA;MACA,KAAAkB,oBAAA;IACA;MACAF,OAAA,CAAAC,GAAA;MACA,KAAAE,QAAA,CAAAC,OAAA;IACA;IAEA,IAAAN,MAAA,CAAAb,SAAA;MACA,KAAAA,SAAA,GAAAa,MAAA,CAAAb,SAAA;MACAe,OAAA,CAAAC,GAAA,sBAAAH,MAAA,CAAAb,SAAA;IACA;MACAe,OAAA,CAAAC,GAAA;IACA;;IAEA;IACA,IAAAH,MAAA,CAAAd,OAAA,IAAAc,MAAA,CAAAb,SAAA;MACAb,eAAA,MAAAiC,OAAA,OAAAN,MAAA;QACAf,OAAA,EAAAc,MAAA,CAAAd,OAAA;QACAC,SAAA,EAAAa,MAAA,CAAAb;MACA;IACA;IAEA,KAAAqB,OAAA;EACA;EACAC,KAAA;IACA;IACAxB,SAAA,WAAAA,UAAAyB,MAAA;MACA,IAAAA,MAAA,2BAAAxB,OAAA;QACA;QACA,KAAAkB,oBAAA;MACA;IACA;EAEA;EACAO,OAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA;MACA,IAAAzB,SAAA,QAAAA,SAAA,SAAAc,MAAA,CAAAY,KAAA,CAAA1B,SAAA;;MAEA;MACA,IAAAA,SAAA;QACAZ,kBAAA,MAAAgC,OAAA;UACApB,SAAA,EAAAA;QACA;MACA;QACA;QACA,KAAAoB,OAAA,CAAAO,IAAA;MACA;IACA;IAEAC,UAAA,WAAAA,WAAAC,GAAA;MACA;MACA;MACA,KAAAA,GAAA,IAAAA,GAAA,YAAAC,KAAA,IAAAD,GAAA,CAAAE,IAAA,gBAAAF,GAAA,CAAAG,WAAA,CAAA3C,IAAA,CAAA4C,QAAA;QACA,KAAApC,UAAA;MACA;QACA,KAAAA,UAAA,GAAAgC,GAAA;MACA;MACA,KAAAjC,aAAA;IACA;IACAsC,aAAA,WAAAA,cAAA;MACA,KAAAhB,QAAA,CAAAiB,OAAA;MACA;MACA,KAAAC,KAAA,CAAAC,iBAAA,CAAAC,SAAA;MACA;MACA,KAAArB,oBAAA;IACA;IACAsB,YAAA,WAAAA,aAAA;MACAxB,OAAA,CAAAC,GAAA;IACA;IACAwB,WAAA,WAAAA,YAAA;MACA;IAAA,CACA;IAEA;IACAnB,OAAA,WAAAA,QAAA;MACA;IAAA,CACA;IAEA;IACAJ,oBAAA,WAAAA,qBAAA;MAAA,IAAAwB,KAAA;MACA,UAAA1C,OAAA;QACA;MACA;MAEA,KAAA2C,IAAA;QACA3C,OAAA,OAAAA;MACA,GAAA4C,IAAA,WAAAC,GAAA;QACA7B,OAAA,CAAAC,GAAA,iBAAA4B,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,MAAA;UACA;UACAJ,KAAA,CAAAvC,uBAAA,CAAAC,WAAA,GAAAyC,GAAA;QACA;UACAH,KAAA,CAAAvC,uBAAA,CAAAC,WAAA;UACAY,OAAA,CAAAC,GAAA;QACA;;QAEA;QACAyB,KAAA,CAAAK,SAAA;UACA,IAAAL,KAAA,CAAAL,KAAA,CAAAW,eAAA;YACAN,KAAA,CAAAL,KAAA,CAAAW,eAAA,CAAAT,SAAA;UACA;QACA;MACA,GAAAU,KAAA,WAAAC,KAAA;QACAlC,OAAA,CAAAkC,KAAA,iBAAAA,KAAA;QACAR,KAAA,CAAAvC,uBAAA,CAAAC,WAAA;QACAsC,KAAA,CAAAvB,QAAA,CAAA+B,KAAA;;QAEA;QACAR,KAAA,CAAAK,SAAA;UACA,IAAAL,KAAA,CAAAL,KAAA,CAAAW,eAAA;YACAN,KAAA,CAAAL,KAAA,CAAAW,eAAA,CAAAT,SAAA;UACA;QACA;MACA;IACA;IAEA;IACAY,sBAAA,WAAAA,uBAAA;MACAnC,OAAA,CAAAC,GAAA;MACA,KAAAC,oBAAA;IACA;IAEA;IACA;IACAkC,uBAAA,WAAAA,wBAAAtB,GAAA;MAAA,IAAAuB,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAxB,IAAA;MACA,GAAAY,IAAA;QACA;QACAS,MAAA,CAAAV,IAAA;UACAc,EAAA,EAAA3B,GAAA,CAAA2B;QACA,GAAAb,IAAA,WAAAC,GAAA;UACA7B,OAAA,CAAAC,GAAA,gBAAA4B,GAAA;UACAQ,MAAA,CAAAlC,QAAA,CAAAiB,OAAA;UACA;UACAiB,MAAA,CAAAhB,KAAA,CAAAC,iBAAA,CAAAC,SAAA;UACA;UACAc,MAAA,CAAAnC,oBAAA;QACA,GAAA+B,KAAA,WAAAC,KAAA;UACAlC,OAAA,CAAAkC,KAAA,gBAAAA,KAAA;UACAG,MAAA,CAAAlC,QAAA,CAAA+B,KAAA;QACA;MACA,GAAAD,KAAA;QACAI,MAAA,CAAAlC,QAAA,CAAAuC,IAAA;MACA;IACA;IAEA;IACAC,sBAAA,WAAAA,uBAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,KAAAxD,oBAAA,GAAAuD,CAAA;MACAE,UAAA;QACAD,MAAA,CAAAxB,KAAA,CAAA0B,kBAAA,CAAAC,QAAA;MACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAL,CAAA;MACA,KAAAlD,sBAAA,GAAAkD,CAAA;IACA;IAEA;IACA;IACAM,oBAAA,WAAAA,qBAAAN,CAAA;MAAA,IAAAO,MAAA;MACA,KAAAxD,kBAAA,GAAAiD,CAAA;MACAE,UAAA;QACAK,MAAA,CAAA9B,KAAA,CAAA+B,gBAAA,CAAAJ,QAAA;MACA;IACA;IACA;IACAK,mBAAA,WAAAA,oBAAAT,CAAA;MACA,KAAAhD,oBAAA,GAAAgD,CAAA;IACA;IAEA;IACAU,aAAA,WAAAA,cAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA,KAAAA,MAAA;IACA;EACA;AACA"}]}