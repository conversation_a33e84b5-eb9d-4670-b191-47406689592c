<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid api="commonModule/expenseItem-page" :event-bus="searchEventBus" :search-params="searchForm"
          :newcolumn="columns" @datas="getDatas" @columnChange="getcolumn" :auto-load="true" ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="编码">
                <el-input v-model.trim="searchForm.fnumber" size="small" maxlength="32" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="名称">
                <el-input v-model.trim="searchForm.fname" size="small" maxlength="32" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table"
            style="width: 100%">
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column v-if="item.key == 'type'" :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'" :key="index" :prop="item.key" :label="item.title"
                min-width="180">
                <template slot-scope="scope">
                    <div>
                      {{ scope.row.type == 1 ? "收入": scope.row.type == 2 ? "成本":'未分类'}}
                    </div>
                </template>
              </el-table-column>
              <el-table-column v-else :show-overflow-tooltip="true" :key="item.key" :prop="item.key" :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'" :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="180">
              <template slot-scope="scope">
                <el-button  v-permission="'expense_type_set'"  @click="handleBindSalary(scope.row)" type="text"
                  size="small">设置费用类型</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>

    <!-- 设置费用类型 -->
    <el-dialog title="设置费用类型" :visible.sync="showBindDialog" :close-on-click-modal="false" width="40%">
      <el-form :model="dialogBindForm" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="费用类型" prop="type">

          <el-select v-model="dialogBindForm.type" placeholder="请选择"  clearable size="small">
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showBindDialog = false">取消</el-button>
        <el-button size="small" type="primary" :loading="okLoading" @click="submitBindForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultDialogForm = {
};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();

    return {
      typeList: [
        {
          label: '收入',
          value: '1'
        },
        {
          label: '成本',
          value: '2'
        },

      ],

      okLoading: false,
      rules: {
        type: [
          {required: true, message: '请设置费用类别', trigger: 'blur,change'},
        ],
      },
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '编码',
          key: 'fnumber',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '名称',
          key: 'fname',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '费用类别',
          key: 'type',
          tooltip: true,
          minWidth: 150,
        },

      ],
      tableData: [],
      showDialog: false,
      dialogForm: _.cloneDeep(defaultDialogForm),
      dialogTitle: '新增',
      dialogBindForm: {
        type: ''
      },
      showBindDialog: false,
    };
  },
  watch: {
  },
  mounted() {
    // this.init();
  },
  methods: {
    handleBindSalary(e) {
      this.setItem = e;
      this.dialogBindForm.type = e.type ? e.type + '' : '';
      this.showBindDialog = true;
    },

    submitBindForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.$api['commonModule/expenseItem-save']({...this.setItem, ...this.dialogBindForm}).then(data => {
            this.showDialog = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.setItem = null;
            this.showBindDialog = false;
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            this.showBindDialog = false;
            this.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
