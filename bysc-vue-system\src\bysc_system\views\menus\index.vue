<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 10:11:20
 * @FilePath: \kdsp_vue_clear\src\systemView\views\menus\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="6">
        <common-tree
          @treeNode="getClickTreeNode"
          :tree-props="treeProps"
          :tree-data="treedata"
        ></common-tree>
      </el-col>
      <el-col :span="18">
        <Grid
          api="systems/resourcePage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-input
              style="width: 200px;margin:0 10px 0 0;"
              v-model.trim="searchForm.resourceName"
              size="small"
              placeholder="请输入资源名称"
            ></el-input>
            <el-button size="small" type="primary" style="margin:0 0 0 10px;" @click="searchTable">搜索</el-button>
            <el-button size="small" @click="resetTable">重置</el-button>
          </div>
          <div slot="action">
            <el-button size="small" type="primary" @click="handleAdd"
              >添加</el-button
            >
          </div>
          <div slot="action"></div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading"  :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">{{
                  scope.row[item.slot]
                }}</template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  size="small"
                  @click="handleRdit(scope.row)"
                  type="text"
                  >编辑</el-button
                >
                <template>
                  <el-popconfirm
                  style="margin-left:8px"
                  @confirm="handleDelete(scope.row.id)"
                    title="您确定要删除该资源吗？"
                  >
                    <el-button size="small" type="text" slot="reference">删除</el-button>
                  </el-popconfirm>
                  </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="名称" prop="resourceName">
            <el-input
              size="small"
              v-model.trim="ruleForm.resourceName"
            ></el-input>
          </el-form-item>
          <el-form-item label="上级资源" prop="parentName">
            <el-input
              style="width: 250px"
              size="small"
              disabled
              v-model.trim="ruleForm.parentName"
            ></el-input>
            <el-button
              size="small"
              style="margin-left: 10px"
              type="primary"
              @click="dialogVisible = true"
              >选择上级资源</el-button
            >
          </el-form-item>
          <el-form-item label="路由NAME" prop="resourcePageName">
            <el-input
              size="small"
              v-model.trim="ruleForm.resourcePageName"
              :maxlength="30"
            ></el-input>
          </el-form-item>
          <el-form-item label="路由PATH" prop="resourcePath">
            <el-input
              size="small"
              v-model.trim="ruleForm.resourcePath"
            ></el-input>
          </el-form-item>
          <el-form-item label="图标" prop="resourceIcon">
            <iconChoose
              :value="ruleForm.resourceIcon"
              @on-change="getIcon"
              size="small"
            ></iconChoose>
          </el-form-item>
          <el-form-item label="资源类型" prop="resourceType">
            <el-select
              style="width: 250px"
              size="small"
              v-model.trim="ruleForm.resourceType"
              placeholder="请选择活动区域"
            >
              <!-- <el-option label="模块" value="1"></el-option> -->
              <el-option label="菜单" value="2"></el-option>
              <el-option label="按钮" value="3"></el-option>
              <!-- <el-option label="链接" value="4"></el-option> -->
              <el-option label="跳转页" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资源级别" prop="resourceLevel">
            <el-input-number v-model="ruleForm.resourceLevel" controls-position="right" size="small" placeholder="请输入资源级别" :min="0" :max="999" style="width:200px;"></el-input-number>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch v-model.trim="status"></el-switch>
          </el-form-item>
          <el-form-item label="是否展示" prop="resourceShow">
            <el-switch v-model.trim="ruleForm.resourceShow"></el-switch>
          </el-form-item>

          <el-form-item>
            <el-button
              size="small"
              type="primary"
              @click="submitForm('ruleForm')"
              >保存</el-button
            >
            <el-button size="small" @click="resetForm('ruleForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <el-dialog title="上级资源" :visible.sync="dialogVisible" width="30%">
      <div>
        <common-tree
          @treeNode="getSelectTreeNode"
          :tree-props="treeProps"
          :highlightcurrent="true"
          :tree-data="resourcetreedata"
        ></common-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="setParentInfo">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import commonTree from '@/components/treeComp/commonTree';
import iconChoose from '@/components/choose/icon-choose';
const defaultSearchForm = {
  resourceName: '',
  parentId: null,
};
const defaultForm = {
  parentId: null,
  parentName: '',
  resourceName: '',
  resourceType: '2',
  resourceIcon: '',
  resourcePath: '',
  resourceLevel: '1',
  resourceShow: true,
  status: 1,
  comments: null,
  resourcePageName: '',
};
export default {
  components: {commonTree, Grid, iconChoose},
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      dialogVisible: false,
      status: true,
      ruleForm: _.cloneDeep(defaultForm),
      rules: {
        resourceName: [
          {required: true, message: '请输入名称', trigger: 'blur'},
          {
            min: 1,
            max: 15,
            message: '长度在 1 到 15 个字符',
            trigger: 'blur',
          },
        ],
        resourcePath: [
          {required: true, message: '请输入路由PATH', trigger: 'blur'},
        ],
        resourcePageName: [
          {required: true, message: '请输入路由NAME', trigger: 'blur'},
        ],
        region: [
          {required: true, message: '请选择活动区域', trigger: 'change'},
        ],
        date1: [
          {
            type: 'date',
            required: true,
            message: '请选择日期',
            trigger: 'change',
          },
        ],
        date2: [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'change',
          },
        ],
        type: [
          {
            type: 'array',
            required: true,
            message: '请至少选择一个活动性质',
            trigger: 'change',
          },
        ],
        resource: [
          {required: true, message: '请选择活动资源', trigger: 'change'},
        ],
        desc: [{required: true, message: '请填写活动形式', trigger: 'blur'}],
      },
      drawerName: '添加',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      treeProps: {
        children: 'children',
        label: 'resourceName',
      },
      columns: [
        {
          title: '资源级别',
          key: 'resourceLevel',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '资源名称',
          key: 'resourceName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '资源路径',
          key: 'resourcePath',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '资源类型',
          key: 'resourceTypeName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '资源状态',
          key: 'statusName',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
      ],
      treedata: [],
      resourceName: '',
      tableData: [],
      resourcetreedata: [],
      selectedResouce: {},
    };
  },
  watch: {
    status(val) {
      this.ruleForm.status = val ? 1 : 0;
    }
  },
  mounted() {
    this.getTrees();
  },

  methods: {
    handleAdd() {
      this.drawer = true;
      this.drawerName = '添加';
      this.ruleForm = _.cloneDeep(defaultForm);
    },
    handleRdit(e) {
      this.$api['systems/resourceGet']({id: e.id}).then(data => {
        this.ruleForm = data;
        this.status = !!data.status;
        this.drawer = true;
        this.ruleForm.parentName = this.getParentName(this.resourcetreedata, data.parentId);
        this.drawerName = '编辑';
      });
    },
    getParentName(arr, param) {
      let name = '';
      arr.forEach(e => {
        if (e.children) {
          e.id == param && (name = e.resourceName);
          e.children.forEach(i => {
            if (i.children) {
              i.id == param && (name = i.resourceName);
              i.children.forEach(j => {
                if (j.children) {
                  j.id == param && (name = j.resourceName);
                  j.children.forEach(k => {
                    if (k.children) {
                      k.id == param && (name = k.resourceName);
                      k.children.forEach(l => {
                        if (l.children) {
                          l.id == param && (name = l.resourceName);
                        }
                      });
                    }
                  });
                }
              });
            }
          });
        }
      });
      return name;
    },
    handleDelete(e) {
      this.$api['systems/resourceDelete']({id: e}).then(data => {
        this.$refs.grid.query();
        this.getTrees();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    getIcon(e) {
      this.ruleForm.resourceIcon = e;
    },
    setParentInfo() {
      this.ruleForm.parentId = this.selectedResouce.id;
      this.ruleForm.parentName = this.selectedResouce.resourceName;
      this.$refs.grid.query();
      this.dialogVisible = false;
    },
    getSelectTreeNode(e) {
      this.selectedResouce = e;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$api['systems/addMenu'](this.ruleForm).then(data => {
            this.drawer = false;
            this.getTrees();
            this.$refs.grid.query();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    addTable() {},
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.searchForm.parentId = null;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    gerTreeData() {
      if (this.resourceName) {
        this.$api['systems/getTreeList']({
          resourceName: this.resourceName,
        }).then(data => {
          this.treedata = data;
        });
      } else {
        this.searchForm.parentId = null;
        this.getTrees();
      }
    },
    getClickTreeNode(e) {
      this.searchForm.parentId = this.searchForm.parentId == e.id ? null : e.id;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getMenuTrees() {
      this.$api['systems/getPremissTree']({parentId: 0}).then(data => {
        this.resourcetreedata = data;
      });
    },
    getTrees() {
      this.$api['systems/getPremissTree']({parentId: 0}).then(data => {
        this.treedata = data;
        this.resourcetreedata = data;
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
