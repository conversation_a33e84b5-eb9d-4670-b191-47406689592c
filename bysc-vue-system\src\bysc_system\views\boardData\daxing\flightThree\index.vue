<template>
  <div class="table-container">

    <div class="operation-container">
      <!-- 添加时间范围选择器 -->
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        size="small"
        :clearable="false"
        value-format="yyyy-MM-dd HH:mm:ss"
        :default-time="['00:00:00', '23:59:59']"
        style="margin-right: 10px; width: 380px;"
      />
      <!-- 添加查询按钮 -->
      <el-button
        type="primary"
        size="small"
        @click="handleSearch"
      >查询</el-button>

      <!-- 添加重置按钮 -->
      <el-button
        size="small"
        @click="handleReset"
        style="margin-right: 10px;"
      >重置</el-button>

      <el-button
        type="primary"
        @click="handleExport"
        size="small"
        v-permission="'daxingFlightThreeTable-export'"
      >导出</el-button>
    </div>

    <el-table
      :data="tableData"

      stripe
      width="100%"
    >
      <el-table-column
        prop="luggageRateDate"
        label="日期"
        min-width="180"
        header-align="center"
        align="center"
      />
      <el-table-column
        prop="name"
        label="名称"
        min-width="100"
        header-align="center"
        align="center"
      />


      <el-table-column
        prop="luggageVolume"
        label="行李量（件）"
        min-width="100"
        header-align="center"
        align="center"
      />

      <el-table-column
        prop="errorRate"
        label="错分率（%）"
        min-width="100"
        header-align="center"
        align="center"
      />

      <el-table-column
        prop="delayRate"
        label="迟运率（%）"
        min-width="100"
        header-align="center"
        align="center"
      />

      <el-table-column
        prop="brokenRate"
        label="破损率（%）"
        min-width="100"
        header-align="center"
        align="center"
      />



      <el-table-column
        label="操作"
        width="250"
        fixed="right"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            v-permission="'daxingFlightThreeTable-edit'"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <Panel ref="panel" @submit="getTableData" />
  </div>
</template>

<script>
import Panel from './component/panel.vue';
import {mapActions} from "vuex";
import dayjs from "dayjs";
export default {
  name: 'yesterdayFlights',
  components: {
    Panel
  },
  data() {
    return {
      searchParams: {},
      tableData: [],
      current: 1,
      limit: 10,
      total: 0,
      dateRange: [], // 添加时间范围数据
    };
  },
  methods: {
    ...mapActions(["download"]),
    handleEdit(row) {
      this.$refs.panel.open(row, 'edit');
    },
    handleSizeChange(val) {
      this.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.current = val;
      this.getTableData();
    },
    handleSearch() {
      this.current = 1; // 重置页码
      this.getTableData();
    },
    handleReset() {
      this.dateRange = [];
      this.current = 1;
      this.getTableData();
    },
    getTableData() {
      const params = {
        current: this.current,
        limit: this.limit,
        param: {
          beginDateTime: this.dateRange && this.dateRange[0] || '',
          endDateTime: this.dateRange && this.dateRange[1] || ''
        }
      };
      this.searchParams = {
        beginDateTime: params.param.beginDateTime,
        endDateTime: params.param.endDateTime
      };
      this.$api['boardData/dashboardLuggageThreeRate-dx-page'](params).then(res => {
        this.tableData = res.list;
        this.total = res.total;
      });
    },
    handleExport() {
      let s = '';
      Object.keys(this.searchParams).forEach((key, i) => {
        if (this.searchParams[key]) {
          if (i != Object.keys(this.searchParams).length - 1) {
            s = s + key + '=' + this.searchParams[key] + '&';
          } else {
            s = s + key + '=' + this.searchParams[key];
          }
        }
      });
      let api = "/api/syn/dashboardLuggageThreeRate/download-dx";
      let url = `${api}?${s}`;
      let downData = {
        url: url,
        downLoad: `大兴行李数据管理-${dayjs(new Date()).format('YYYYMMDDHHmmss')}.xlsx`,
      };
      this.download(downData); // 下载
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .operation-container {
    margin-bottom: 20px;
    display: flex; // 添加flex布局
    align-items: center; // 垂直居中对齐
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
