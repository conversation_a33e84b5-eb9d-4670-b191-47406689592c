{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue?vue&type=template&id=56c4bbef", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue", "mtime": 1754276220635}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div v-loading=\"spinShow\" class=\"draw\">\n  <!-- <el-loading :fullscreen=\"false\" v-if=\"spinShow\">\n    <div>正在加载图片</div>\n  </el-loading> -->\n  <div class=\"drawTop\" ref=\"drawTop\" v-if=\"lineStep == lineNum\">\n    <!-- <div>\n      <el-upload\n        class=\"upload-demo\"\n        action=\"\"\n        :auto-upload=\"false\"\n        :show-file-list=\"false\"\n        :on-change=\"handleFileChange\">\n        <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n      </el-upload>\n    </div> -->\n    <div>\n      <el-button @click=\"goBack\" size=\"small\">返回</el-button>\n    </div>\n    <!-- <div>\n      是否开启模糊查询（标准层开启，其它关闭）：<el-switch v-model=\"isOpenLike\"/>\n    </div> -->\n    <!-- <div>\n      <el-button @click=\"resetAll\" size=\"small\">重新绘制</el-button>\n    </div> -->\n    <div>\n      <el-button @click=\"deleteAllAreas\" size=\"small\" type=\"danger\">删除全部区域</el-button>\n    </div>\n    <div>\n      <el-button @click=\"toggleAreaNames\" size=\"small\" :type=\"showAreaNames ? 'success' : 'info'\">\n        {{ showAreaNames ? '隐藏区域名称' : '显示区域名称' }}\n      </el-button>\n    </div>\n\n    <div>\n      <el-button-group>\n        <el-button @click=\"zoomIn\" icon=\"el-icon-zoom-in\" size=\"small\">放大</el-button>\n        <el-button @click=\"zoomOut\" icon=\"el-icon-zoom-out\" size=\"small\">缩小</el-button>\n        <!-- <el-button @click=\"resetZoom\" size=\"small\">重置缩放</el-button>\n        <el-button @click=\"autoFit\" type=\"primary\" size=\"small\">自适应屏幕</el-button> -->\n      </el-button-group>\n    </div>\n    <div>\n      <el-button type=\"primary\" @click=\"exportJson\" size=\"small\">保存区域</el-button>\n    </div>\n    <!-- <div>\n      <el-dropdown @command=\"handleDefaultZoomChange\">\n        <el-button size=\"small\">\n          默认缩放: {{ defaultZoomText }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\n        </el-button>\n        <el-dropdown-menu slot=\"dropdown\">\n          <el-dropdown-item command=\"1.0\">100%</el-dropdown-item>\n          <el-dropdown-item command=\"1.3\">130%</el-dropdown-item>\n          <el-dropdown-item command=\"1.5\">150%</el-dropdown-item>\n          <el-dropdown-item command=\"1.8\">180%</el-dropdown-item>\n          <el-dropdown-item command=\"2.0\">200%</el-dropdown-item>\n          <el-dropdown-item command=\"2.2\">220%</el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div> -->\n  </div>\n  <div>\n    （鼠标左键点击图片绘制区域，任意位置点击右键自动连接起点和终点完成区域绘制，可重复此步骤绘制多个区域）\n  </div>\n  <div>\n    （鼠标悬停在已绘制区域上可高亮显示，点击区域内任意位置即可删除该区域）\n  </div>\n  <div style=\"display: flex;\">\n    <div class=\"canvas-container\" ref=\"canvasContainer\">\n      <!-- 固定在容器右上角的滚动控制按钮，只在需要时显示 -->\n      <div v-if=\"showScrollButtons\" class=\"scroll-controls\">\n        <el-button-group>\n          <el-button @click=\"scrollLeft\" icon=\"el-icon-arrow-left\" size=\"mini\" title=\"向左滚动\"></el-button>\n          <el-button @click=\"scrollRight\" icon=\"el-icon-arrow-right\" size=\"mini\" title=\"向右滚动\"></el-button>\n        </el-button-group>\n      </div>\n      <div class=\"content\" ref=\"content\"></div>\n      <input\n        v-show=\"isShow\"\n        type=\"text\"\n        @blur=\"txtBlue\"\n        ref=\"txt\"\n        id=\"txt\"\n        style=\"\n          z-index: 9999;\n          position: absolute;\n          border: 0;\n          background: none;\n          outline: none;\n        \"\n      />\n    </div>\n    <!-- 区域列表显示面板 -->\n    <div style=\"width: 25%; padding-left: 10px; max-height: 600px; overflow-y: auto;\">\n      <h4>已绘制区域列表</h4>\n      <el-empty v-if=\"roomNameList.length === 0\" description=\"暂无区域\"></el-empty>\n      <el-table\n        v-else\n        :data=\"roomNameList\"\n        style=\"width: 100%\"\n        size=\"small\"\n        :max-height=\"550\"\n        @row-click=\"highlightArea\"\n        @row-mouseover=\"mouseoverArea\"\n        @row-mouseout=\"mouseoutArea\">\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"roomName\" label=\"名称\" align=\"center\"></el-table-column>\n        <el-table-column label=\"操作\" width=\"150\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <a\n              style=\"margin-left: 10px;\"\n              @click.stop=\"deleteAreaByIndex(scope.$index)\">\n              删除\n            </a>\n\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n  </div>\n  <el-dialog\n    :visible.sync=\"areaFormVisible\"\n    title=\"区域信息\"\n    width=\"400px\"\n    :close-on-click-modal=\"false\"\n    @closed=\"handleAreaFormCancel\">\n    <el-form\n      ref=\"areaForm\"\n      :model=\"areaForm\"\n      :rules=\"areaFormRules\"\n      label-width=\"100px\"\n      size=\"small\">\n      <el-form-item label=\"名称\" prop=\"roomName\">\n        <el-input v-model=\"areaForm.roomName\" placeholder=\"请输入名称\"></el-input>\n      </el-form-item>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleAreaFormCancel\">取消</el-button>\n      <el-button type=\"primary\" @click=\"handleAreaFormSubmit\" :loading=\"areaFormLoading\">确定</el-button>\n    </div>\n  </el-dialog>\n\n  <el-dialog\n    :visible.sync=\"imgModal\"\n    title=\"更换图片\"\n    width=\"400px\"\n    @closed=\"imgCancel\">\n    <el-form\n      ref=\"imgForm\"\n      :model=\"imgForm\"\n      :rules=\"imgFormValidate\"\n      label-width=\"110px\">\n      <el-form-item label=\"楼层号\" prop=\"floorNo\">\n        <el-input\n          v-model.trim=\"imgForm.floorNo\"\n          maxlength=\"32\"\n          placeholder=\"楼层号\">\n        </el-input>\n      </el-form-item>\n      <el-form-item label=\"图片地址\" prop=\"imgUrl\">\n        <el-input\n          v-model.trim=\"imgForm.imgUrl\"\n          maxlength=\"256\"\n          placeholder=\"图片地址\">\n        </el-input>\n        <!-- <multi-upload-pic-input :maxCount=\"1\" :maxSize=\"10240\" @on-change=\"handleMultiUpload2\" width=\"235px\" ref=\"multiUploadImage\"></multi-upload-pic-input> -->\n      </el-form-item>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"imgCancel\">取消</el-button>\n      <el-button type=\"primary\" :loading=\"imgOkLoading\" @click=\"imgOk\">确定</el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}