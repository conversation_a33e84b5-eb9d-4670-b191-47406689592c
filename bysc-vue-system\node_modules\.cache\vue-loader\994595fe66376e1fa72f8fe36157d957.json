{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue?vue&type=template&id=5e906b0b", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\components\\treeComp\\localtionTree.vue", "mtime": 1754276220642}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div class=\"app-container\">\n  <el-popover\n    v-model=\"showSearchDeptTree\"\n    placement=\"right\"\n    width=\"800\"\n    trigger=\"click\"\n    @show=\"showSearchTreeEvent\"\n    @hide=\"hideSearchTreeEvent\"\n  >\n    <div>\n      <el-input\n        size=\"small\"\n        style=\"width: 100%; margin-bottom: 8px\"\n        placeholder=\"请输入关键字搜索\"\n        v-model=\"locationDesc\"\n        @input=\"onSearch\"\n        @clear=\"onSearch\"\n        clearable\n      >\n        default-expand-all\n      </el-input>\n    </div>\n    <div style=\"height: 460px; overflow-y: scroll\">\n      <el-tree\n        :props=\"props\"\n        :data=\"deptAndUserData\"\n        :show-checkbox=\"showCheckbox\"\n        :load=\"loadNode\"\n        v-bind=\"$attrs\"\n        v-on=\"$listeners\"\n        :lazy=\"isLazy\"\n        @node-click=\"handleNodeClick\"\n        @check=\"getTreeDatas\"\n        :highlight-current=\"highlightCurrent\"\n        node-key=\"primaryId\"\n        :key=\"setTimer\"\n        check-strictly\n        ref=\"tree\"\n        :default-checked-keys=\"Array.isArray(value) ? value : [value]\"\n        v-if=\"showSearchDeptTree\"\n      >\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n          <span>\n            <span style=\"margin-left: 2px\"\n              >{{ data.label }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{\n                node.label\n              }}</span\n            >\n          </span>\n        </span>\n      </el-tree>\n    </div>\n\n    <el-input\n      size=\"small\"\n      style=\"width: 100%; margin-left: 8px\"\n      v-model=\"selectValue\"\n      type=\"textarea\"\n      :placeholder=\"treeplaceholder\"\n      readonly\n      clearable\n      @clear=\"clearSelect\"\n      slot=\"reference\"\n    ></el-input>\n\n    <span class=\"dialog-footer\" v-if=\"$attrs.multiple\">\n      <el-button size=\"small\" @click=\"showSearchDeptTree = false\"\n        >取 消</el-button\n      >\n      <el-button size=\"small\" type=\"primary\" @click=\"submitBindPositionForm\"\n        >确 定</el-button\n      >\n    </span>\n  </el-popover>\n</div>\n", null]}