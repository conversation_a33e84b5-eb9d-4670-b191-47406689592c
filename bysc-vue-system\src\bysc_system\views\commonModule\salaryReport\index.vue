<template>
  <div>
    <Grid
      api="commonModule/financeSalaryPushRecord-page"
      :event-bus="searchEventBus"
      :search-params="searchForm"
      :newcolumn="columns"
      @datas="getDatas"
      @columnChange="getcolumn"
      :auto-load="true"
      ref="grid"
    >
      <div slot="search">
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
        >
          <el-form-item label="月份">
            <el-date-picker v-model="searchForm.pushMonth" value-format="yyyy-MM" type="month" placeholder="选择月份" size="small" clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              style="margin: 0 0 0 10px"
              @click="searchTable"
              >搜索</el-button
            >
            <el-button size="small" @click="resetTable">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="action">
        <el-button v-permission="'salaryReport_generate'" size="small" type="primary" @click="generateReport"
          >工资报表生成</el-button
        >
      </div>
      <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">
        <el-table-column fixed="left" label="序号" type="index" width="50">
        </el-table-column>
        <template v-for="(item, index) in columns">
          <el-table-column
            v-if="item.slot == 'salaryFeeType'"
            :show-overflow-tooltip="true"
            :align="item.align ? item.align : 'center'"
            :key="index"
            :prop="item.key"
            :label="item.title"
            min-width="180"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.salaryFeeType == '3155'">首维</div>
              <div v-if="scope.row.salaryFeeType == '3006'">博维</div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :show-overflow-tooltip="true"
            :key="item.key"
            :prop="item.key"
            :label="item.title"
            :min-width="item.minWidth ? item.minWidth : '150'"
            :align="item.align ? item.align : 'center'"
          >
          </el-table-column>
        </template>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          type="action"
          width="180"
        >
          <template slot-scope="scope">
            <el-button
              v-permission="'salaryReport_download'"
              @click="downloadFile(scope.row)"
              type="text"
              size="small"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </Grid>
    <el-dialog :title="'工资报表生成'" :visible.sync="monthDialog" :close-on-click-modal="false" width="30%">
      <div>
        <el-form
          :model="monthForm"
          :rules="monthFormRules"
          ref="monthForm"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="月份" prop="month">
                <el-date-picker
                  v-model="monthForm.month"
                  format="yyyy-MM"
                  value-format="yyyy-MM"
                  type="month"
                  placeholder="选择月份"
                  size="small"
                  clearable
                  :picker-options="pickerOptions">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="monthDialog = false"
          >取 消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :loading="monthOkLoading"
          @click="submitMonthForm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import {mapActions} from "vuex";
import downLoadNotice from '@/mixins/downLoadNotice.vue';
const defaultSearchForm = {};
export default {
  components: {
    Grid,
  },
  mixins: [downLoadNotice],
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      monthFormRules: {
        month: [
          {required: true, message: '请选择月份', trigger: 'blur,change'},
        ],
      },
      monthForm: {},
      monthDialog: false,
      monthOkLoading: false,
      pickerOptions: {
        disabledDate(time) {
          let time1 = new Date().setMonth(new Date().getMonth() - 1);
          let time2 = new Date().setMonth(new Date().getMonth() - 5);
          return (time.getTime() > time1) || (time.getTime() < time2);
        },
      },

      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '财务组织',
          slot: 'salaryFeeType',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '月份',
          key: 'pushMonth',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    ...mapActions(["download"]),
    generateReport() {
      let today = new Date(); // 获取当前日期
      let year = today.getFullYear(); // 获取当前年份
      let month = today.getMonth(); // 获取当前月份
      // 计算上个月的年月
      if (month === 0) { // 如果当前月份为1月份，则上个月为去年12月份
        year = year - 1;
        month = 12;
      }
      console.log(month);
      // 将年月转换为字符串，格式为YYYY-MM
      let lastMonth = year + '-' + (month < 10 ? '0' + month : month);
      this.monthForm = {
        month: lastMonth,
      };
      this.monthDialog = true;
    },
    submitMonthForm() {
      this.$refs.monthForm.validate(valid => {
        if (valid) {
          this.$confirm('确定生成工资报表？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            var loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$api['commonModule/generate-sap-zip']({
              year: this.monthForm.month.substring(0, 4),
              month: this.monthForm.month.substring(5, 7)
            }).then(data => {
              loading.close();
              this.$refs.grid.query();
              this.$message({
                type: 'success',
                message: '生成工资报表成功',
              });
            }).catch(() => {
              loading.close();
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消成工资报表'
            });
          });
        } else {
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },

    downloadFile(row) {
      this.$api['commonModule/get-sap-zip-name']({
        id: row.id
      }).then(data => {
        if (data) {
          let url
        = "/api/extension/file/download?fileName="
        + data;
          let downData = {
            url: url,
            downLoad: (row.salaryFeeType == '3155' ? '首维' : '博维') + 'SAP报表-' + row.pushMonth + '.zip',
          };
          this.download(downData); // 下载
        }
      }).catch(() => {
      });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
