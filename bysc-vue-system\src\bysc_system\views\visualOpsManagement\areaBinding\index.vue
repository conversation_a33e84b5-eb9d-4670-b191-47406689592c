<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="visualOpsManagement/maintenanceRegion-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          :auto-load="true"
          ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="区域编号">
                <el-input
                  v-model.trim="searchForm.regionCode"
                  size="small"
                  maxlength="32"
                  placeholder="请输入"
                  clearable
                  style="width: 200px">
                </el-input>
              </el-form-item>
              <el-form-item label="区域名称">
                <el-input
                  v-model.trim="searchForm.regionName"
                  size="small"
                  maxlength="32"
                  placeholder="请输入"
                  clearable
                  style="width: 200px">
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">查询</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

           <div slot="action">
            <el-button  size="small" @click="handleBack">
              <i class="el-icon-back"></i> 返回
            </el-button>
          </div>

          <el-table
            slot="table"
            slot-scope="{loading}"
            v-loading="loading"
            :data="tableData"
            stripe
            ref="table"
            style="width: 100%">
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="item in columns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="150">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleBind(scope.row)">绑定设备</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>



  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import {RouteParamsMixin, getRouteParams, saveRouteParams} from '@/utils/routeParams';
import _ from 'lodash';

const defaultSearchForm = {
  regionCode: '',
  regionName: '',
  drawingId: '' // 添加 drawingId 参数
};

export default {
  name: 'AreaBinding',
  mixins: [RouteParamsMixin],
  components: {
    Grid
  },
  created() {
    // 使用工具函数获取参数
    const params = getRouteParams(this.$route, ['drawingId', 'drawingName'], 'areaBinding_params');

    if (params.drawingId) {
      this.searchForm.drawingId = params.drawingId;

      // 保存参数，确保刷新后不丢失
      saveRouteParams(this.$router, this.$route, {
        drawingId: params.drawingId,
        drawingName: params.drawingName
      }, 'areaBinding_params');

      console.log('获取到 drawingId 参数:', params.drawingId);
    } else {
      console.log('未获取到 drawingId 参数');
      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');
    }
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();

    return {
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '区域编号',
          key: 'regionCode',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '区域名称',
          key: 'regionName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '创建人名称',
          key: 'creatorName',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 150,
        }
      ],
      tableData: []
    };
  },

  methods: {
    handleBack() {
      // 返回时可以选择是否清理localStorage中的drawingId
      // localStorage.removeItem('currentDrawingId');
      this.$router.push('/system/visualOpsManagement/drawingManagement');
    },

    // 绑定设备
    handleBind(row) {
      console.log('绑定设备:', row);
      // 使用新的导航工具，自动保存参数
      const {navigateWithParams} = require('@/utils/routeParams');
      navigateWithParams(this.$router, 'deviceBinding', {
        drawingId: this.searchForm.drawingId,
        reginId: row.id,
      });
    },
    // 查看详情
    handleView(row) {
      console.log('查看区域:', row);
      this.$message.info('查看功能待开发');
    },
    // 搜索表格
    searchTable() {
      this.$refs.grid.queryData();
    },
    // 重置表格
    resetTable() {
      // 保存当前的 drawingId 值
      const currentDrawingId = this.searchForm.drawingId;

      // 重置表单
      this.searchForm = _.cloneDeep(defaultSearchForm);

      // 恢复 drawingId 值
      this.searchForm.drawingId = currentDrawingId;

      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    // 获取列配置
    getColumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    // 获取表格数据
    getDatas(e) {
      this.tableData = e;
    }
  }
};
</script>

<style lang="less" scoped>
</style>
