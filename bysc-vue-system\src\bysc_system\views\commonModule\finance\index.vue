<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid api="systems/financeSubject-page" :event-bus="searchEventBus" :search-params="searchForm"
          :newcolumn="columns" @datas="getDatas" @columnChange="getcolumn" :auto-load="true" ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="科目编码">
                <el-input v-model.trim="searchForm.subjectCode" size="small" maxlength="32" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="科目名称">
                <el-input v-model.trim="searchForm.subjectName" size="small" maxlength="32" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="科目全名">
                <el-input v-model.trim="searchForm.subjectFullName" size="small" maxlength="32" placeholder="请输入" clearable></el-input>
              </el-form-item>
              <el-form-item label="用途">
                <el-select v-model="searchForm.usage" placeholder="请选择" clearable size="small">
                  <el-option v-for="(item, index) in usageList" :key="index" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="组织">
                <el-select v-model="searchForm.orgCode" placeholder="请选择" clearable size="small">
                  <el-option v-for="(item, index) in orgCodeList" :key="index" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'finance_add'" size="small" type="primary" @click="handleEdit(0)">新增</el-button>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table"
            style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column v-if="item.slot == 'orgCode'" :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'" :key="index" :prop="item.key" :label="item.title"
                min-width="180">
                <template slot-scope="scope">
                  <div v-for="(cell, i) in orgCodeList" :key="i">
                    <div v-if="cell.value == scope.row.orgCode">{{ cell.label }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-else :show-overflow-tooltip="true" :key="item.key" :prop="item.key" :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'" :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="180">
              <template slot-scope="scope">
                <el-button v-permission="'finance_edit'" @click="handleEdit(1, scope.row)" type="text"
                  size="small">编辑</el-button>
                <el-button v-permission="'finance_del'" @click="handleDelete(scope.row)" type="text"
                  size="small">删除</el-button>
                <!-- <el-button v-permission="'finance_bind'" @click="handleBindSalary(scope.row)" type="text"
                  size="small">绑定人力工资</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <!-- 编辑 -->
    <el-dialog :title="dialogTitle" :visible.sync="showDialog" :close-on-click-modal="false" width="40%">
      <el-form :model="dialogForm" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="科目编码" prop="subjectCode">
          <el-input v-model.trim="dialogForm.subjectCode" size="small" maxlength="32" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="科目名称" prop="subjectName">
          <el-input v-model.trim="dialogForm.subjectName" size="small" maxlength="32" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="科目全名" prop="subjectFullName">
          <el-input v-model.trim="dialogForm.subjectFullName" size="small" maxlength="32" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="用途" prop="usage">
          <el-select v-model="dialogForm.usage" placeholder="请选择" clearable size="small">
            <el-option v-for="(item, index) in usageList" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织" prop="orgCode">
          <el-select v-model="dialogForm.orgCode" placeholder="请选择" clearable size="small">
            <el-option v-for="(item, index) in orgCodeList" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">取消</el-button>
        <el-button size="small" type="primary" :loading="okLoading" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
    <!-- 绑定人力工资字段 -->
    <el-dialog title="绑定人力工资字段" :visible.sync="showBindDialog" :close-on-click-modal="false" width="40%">
      <el-form :model="dialogBindForm" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="人力工资">
          <el-select v-model="dialogBindForm.sectionType" placeholder="请选择" multiple clearable size="small">
            <el-option v-for="(item, index) in usageList" :key="index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showBindDialog = false">取消</el-button>
        <el-button size="small" type="primary" :loading="okLoading" @click="submitBindForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultDialogForm = {
  usage: 'manpower'
};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    const validateFdetailId = (rule, value, callback) => {
      if (!this.fdetailId || this.fdetailId.length == 0) {
        callback(new Error('请选择企业级别'));
      } else {
        callback();
      }
    };
    return {
      usageList: [
        {
          label: '材料',
          value: 'material'
        },
        {
          label: '人力',
          value: 'manpower'
        }
      ],
      orgCodeList: [],
      okLoading: false,
      rules: {
        subjectCode: [
          {required: true, message: '请输入科目编码', trigger: 'blur'},
        ],
        subjectName: [
          {required: true, message: '请输入科目名称', trigger: 'blur'},
        ],
        subjectFullName: [
          {required: true, message: '请输入科目全名', trigger: 'blur'},
        ],
        usage: [
          {required: true, message: '请选择用途', trigger: 'blur,change'},
        ],
        orgCode: [
          {required: true, message: '请选择组织', trigger: 'blur,change'},
        ],
      },
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '科目编码',
          key: 'subjectCode',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '科目名称',
          key: 'subjectName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '科目全名',
          key: 'subjectFullName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '用途',
          key: 'usageName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '组织',
          slot: 'orgCode',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      showDialog: false,
      dialogForm: _.cloneDeep(defaultDialogForm),
      dialogTitle: '新增',
      dialogBindForm: {},
      showBindDialog: false,
    };
  },
  watch: {
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.$api["sysDict/getParam"]({
        id: 'SHIYONGZUZHI'
      }).then(res => {
        res.forEach(item => {
          this.orgCodeList.push({
            label: item.dictName,
            value: item.dictCode,
          });
        });
      });
    },
    async handleEdit(type, row) {
      this.dialogTitle = type == 0 ? '新增' : '编辑';
      if (type == 0) {
        this.dialogForm = _.cloneDeep(defaultDialogForm);
        this.showDialog = true;
      } else {
        await this.$api['systems/financeSubject-dts']({id: row.id}).then(data => {
          this.dialogForm = _.cloneDeep(data);
          this.showDialog = true;
        });
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['systems/financeSubject-del']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleBindSalary(e) {
      this.showBindDialog = true;
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.$api['systems/financeSubject-save'](this.dialogForm).then(data => {
            this.showDialog = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            this.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    submitBindForm() {

    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
