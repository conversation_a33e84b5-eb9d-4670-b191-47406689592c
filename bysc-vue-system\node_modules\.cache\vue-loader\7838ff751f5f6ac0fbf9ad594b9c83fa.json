{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\component\\BindPositionDialog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\component\\BindPositionDialog\\index.vue", "mtime": 1754276220637}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\nimport ltree from \"@/components/treeComp/localtionTree.vue\";\r\nimport _ from 'lodash';\r\n\r\nexport default {\r\n  name: 'BindPositionDialog',\r\n  components: {\r\n    ltree\r\n  },\r\n  props: {\r\n    // 弹窗显示状态\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 弹窗标题\r\n    title: {\r\n      type: String,\r\n      default: '绑定资产位置'\r\n    },\r\n    // 弹窗宽度\r\n    width: {\r\n      type: String,\r\n      default: '40%'\r\n    },\r\n    // 是否多选\r\n    multiple: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 是否懒加载\r\n    isLazy: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // API接口地址\r\n    apiUrl: {\r\n      type: String,\r\n      default: 'firstLineDept/get-loc-tree-true'\r\n    },\r\n    // 保存API接口地址\r\n    saveApiUrl: {\r\n      type: String,\r\n      default: 'visualOpsManagement/assetLocation-save'\r\n    },\r\n    // 获取详情API接口地址\r\n    getDetailApiUrl: {\r\n      type: String,\r\n      default: 'firstLineDept/get-dts'\r\n    },\r\n    // 当前行数据\r\n    rowData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 取消按钮文本\r\n    cancelText: {\r\n      type: String,\r\n      default: '取 消'\r\n    },\r\n    // 确认按钮文本\r\n    confirmText: {\r\n      type: String,\r\n      default: '确 定'\r\n    },\r\n    // 自定义表单验证规则\r\n    customRules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 区域ID\r\n    reginId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 已绑定的位置ID数组\r\n    boundLocationIds: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    const validatePosition = (rule, value, callback) => {\r\n      // 检查表单中的实际值，而不是传入的value参数\r\n      const actualValue = this.form.deviceLocationId;\r\n      console.log('validatePosition called - actualValue:', actualValue, 'type:', typeof actualValue);\r\n      console.log('validatePosition called - value param:', value, 'type:', typeof value);\r\n\r\n      if (!actualValue || actualValue === '' || (Array.isArray(actualValue) && actualValue.length === 0)) {\r\n        console.log('Validation failed: no value selected');\r\n        callback(new Error('请选择绑定资产位置'));\r\n      } else {\r\n        console.log('Validation passed');\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      dialogVisible: false,\r\n      confirmLoading: false,\r\n      form: {\r\n        deviceLocationId: '',\r\n        deviceLocationName: ''\r\n      },\r\n      deviceLocationIds: [],\r\n      defaultFormRules: {\r\n        deviceLocationId: [\r\n          {required: true, validator: validatePosition, trigger: 'blur,change'},\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    formRules() {\r\n      return Object.assign({}, this.defaultFormRules, this.customRules);\r\n    }\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val;\r\n        if (val && this.rowData && this.rowData.id) {\r\n          this.loadData();\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    dialogVisible(val) {\r\n      this.$emit('update:visible', val);\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData() {\r\n      if (!this.rowData || !this.rowData.id) {\r\n        return;\r\n      }\r\n\r\n      this.$api[this.getDetailApiUrl]({\r\n        id: this.rowData.id\r\n      }).then(res => {\r\n        this.form = _.cloneDeep(res);\r\n        if (this.form.deviceLocationId) {\r\n          this.deviceLocationIds = this.form.deviceLocationId.split(',');\r\n          this.form.deviceLocationName = this.form.deviceLocationName;\r\n        } else {\r\n          this.deviceLocationIds = [];\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('加载数据失败');\r\n        console.error('Load data error:', error);\r\n      });\r\n    },\r\n\r\n    // 获取选择的中文名称\r\n    getSelectCnData(cnName, data) {\r\n      this.form.deviceLocationName = cnName;\r\n    },\r\n\r\n    // 获取选择的数据\r\n    getSelectData(ids, node) {\r\n      console.log('getSelectData called with ids:', ids, 'type:', typeof ids);\r\n      this.form.deviceLocationId = ids;\r\n      this.deviceLocationIds = Array.isArray(ids) ? ids : [ids];\r\n      console.log('Updated form.deviceLocationId:', this.form.deviceLocationId);\r\n      console.log('Updated deviceLocationIds:', this.deviceLocationIds);\r\n\r\n      // 数据更新后，手动触发验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.bindPositionForm) {\r\n          this.$refs.bindPositionForm.validateField('deviceLocationId');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理取消\r\n    handleCancel() {\r\n      this.dialogVisible = false;\r\n      this.$emit('cancel');\r\n    },\r\n\r\n    // 处理关闭\r\n    handleClose() {\r\n      this.resetForm();\r\n      this.$emit('close');\r\n    },\r\n\r\n    // 处理确认\r\n    handleConfirm() {\r\n      // 确保数据格式正确\r\n      if (this.deviceLocationIds && this.deviceLocationIds.length > 0) {\r\n        this.form.deviceLocationId = Array.isArray(this.deviceLocationIds)\r\n          ? this.deviceLocationIds.join(',')\r\n          : this.deviceLocationIds;\r\n      } else {\r\n        this.form.deviceLocationId = [];\r\n      }\r\n\r\n      // 使用 nextTick 确保数据更新后再验证\r\n      this.$nextTick(() => {\r\n        this.$refs.bindPositionForm.validate(valid => {\r\n          if (valid) {\r\n            this.confirmLoading = true;\r\n            // 构建保存参数，包含 reginId\r\n            const saveParams = {\r\n              locationIds: this.form.deviceLocationId.split(\",\")\r\n            };\r\n            // 如果有 reginId，添加到参数中\r\n            if (this.reginId) {\r\n              saveParams.reginId = this.reginId;\r\n            }\r\n\r\n            this.$api[this.saveApiUrl](saveParams).then(data => {\r\n              this.dialogVisible = false;\r\n              this.confirmLoading = false;\r\n              this.resetForm();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '操作成功',\r\n              });\r\n              this.$emit('success', data);\r\n            }).catch(error => {\r\n              this.confirmLoading = false;\r\n              this.$emit('error', error);\r\n            });\r\n          } else {\r\n            console.log('表单验证失败');\r\n            return false;\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.form = {\r\n        deviceLocationId: '',\r\n        deviceLocationName: ''\r\n      };\r\n      this.deviceLocationIds = [];\r\n      this.$refs.bindPositionForm && this.$refs.bindPositionForm.resetFields();\r\n    }\r\n  }\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqDA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/visualOpsManagement/deviceBinding/component/BindPositionDialog", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    :close-on-click-modal=\"false\"\r\n    :width=\"width\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div>\r\n      <el-form\r\n        :model=\"form\"\r\n        :rules=\"formRules\"\r\n        ref=\"bindPositionForm\"\r\n        label-width=\"100px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"资产位置\" prop=\"deviceLocationId\">\r\n              <ltree\r\n                v-if=\"dialogVisible\"\r\n                ref=\"searchLtree\"\r\n                :cnvalue=\"form.deviceLocationName\"\r\n                :multiple=\"multiple\"\r\n                :isLazy=\"isLazy\"\r\n                :apiUrl=\"apiUrl\"\r\n                :bound-location-ids=\"boundLocationIds\"\r\n                @getSelectCnData=\"getSelectCnData\"\r\n                @getSelectData=\"getSelectData\"\r\n                :value=\"deviceLocationIds\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </div>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button size=\"small\" @click=\"handleCancel\">\r\n        {{ cancelText }}\r\n      </el-button>\r\n      <el-button\r\n        size=\"small\"\r\n        type=\"primary\"\r\n        :loading=\"confirmLoading\"\r\n        @click=\"handleConfirm\"\r\n      >\r\n        {{ confirmText }}\r\n      </el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport ltree from \"@/components/treeComp/localtionTree.vue\";\r\nimport _ from 'lodash';\r\n\r\nexport default {\r\n  name: 'BindPositionDialog',\r\n  components: {\r\n    ltree\r\n  },\r\n  props: {\r\n    // 弹窗显示状态\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 弹窗标题\r\n    title: {\r\n      type: String,\r\n      default: '绑定资产位置'\r\n    },\r\n    // 弹窗宽度\r\n    width: {\r\n      type: String,\r\n      default: '40%'\r\n    },\r\n    // 是否多选\r\n    multiple: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 是否懒加载\r\n    isLazy: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // API接口地址\r\n    apiUrl: {\r\n      type: String,\r\n      default: 'firstLineDept/get-loc-tree-true'\r\n    },\r\n    // 保存API接口地址\r\n    saveApiUrl: {\r\n      type: String,\r\n      default: 'visualOpsManagement/assetLocation-save'\r\n    },\r\n    // 获取详情API接口地址\r\n    getDetailApiUrl: {\r\n      type: String,\r\n      default: 'firstLineDept/get-dts'\r\n    },\r\n    // 当前行数据\r\n    rowData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 取消按钮文本\r\n    cancelText: {\r\n      type: String,\r\n      default: '取 消'\r\n    },\r\n    // 确认按钮文本\r\n    confirmText: {\r\n      type: String,\r\n      default: '确 定'\r\n    },\r\n    // 自定义表单验证规则\r\n    customRules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 区域ID\r\n    reginId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // 已绑定的位置ID数组\r\n    boundLocationIds: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    const validatePosition = (rule, value, callback) => {\r\n      // 检查表单中的实际值，而不是传入的value参数\r\n      const actualValue = this.form.deviceLocationId;\r\n      console.log('validatePosition called - actualValue:', actualValue, 'type:', typeof actualValue);\r\n      console.log('validatePosition called - value param:', value, 'type:', typeof value);\r\n\r\n      if (!actualValue || actualValue === '' || (Array.isArray(actualValue) && actualValue.length === 0)) {\r\n        console.log('Validation failed: no value selected');\r\n        callback(new Error('请选择绑定资产位置'));\r\n      } else {\r\n        console.log('Validation passed');\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      dialogVisible: false,\r\n      confirmLoading: false,\r\n      form: {\r\n        deviceLocationId: '',\r\n        deviceLocationName: ''\r\n      },\r\n      deviceLocationIds: [],\r\n      defaultFormRules: {\r\n        deviceLocationId: [\r\n          {required: true, validator: validatePosition, trigger: 'blur,change'},\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    formRules() {\r\n      return Object.assign({}, this.defaultFormRules, this.customRules);\r\n    }\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val;\r\n        if (val && this.rowData && this.rowData.id) {\r\n          this.loadData();\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    dialogVisible(val) {\r\n      this.$emit('update:visible', val);\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载数据\r\n    loadData() {\r\n      if (!this.rowData || !this.rowData.id) {\r\n        return;\r\n      }\r\n\r\n      this.$api[this.getDetailApiUrl]({\r\n        id: this.rowData.id\r\n      }).then(res => {\r\n        this.form = _.cloneDeep(res);\r\n        if (this.form.deviceLocationId) {\r\n          this.deviceLocationIds = this.form.deviceLocationId.split(',');\r\n          this.form.deviceLocationName = this.form.deviceLocationName;\r\n        } else {\r\n          this.deviceLocationIds = [];\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('加载数据失败');\r\n        console.error('Load data error:', error);\r\n      });\r\n    },\r\n\r\n    // 获取选择的中文名称\r\n    getSelectCnData(cnName, data) {\r\n      this.form.deviceLocationName = cnName;\r\n    },\r\n\r\n    // 获取选择的数据\r\n    getSelectData(ids, node) {\r\n      console.log('getSelectData called with ids:', ids, 'type:', typeof ids);\r\n      this.form.deviceLocationId = ids;\r\n      this.deviceLocationIds = Array.isArray(ids) ? ids : [ids];\r\n      console.log('Updated form.deviceLocationId:', this.form.deviceLocationId);\r\n      console.log('Updated deviceLocationIds:', this.deviceLocationIds);\r\n\r\n      // 数据更新后，手动触发验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.bindPositionForm) {\r\n          this.$refs.bindPositionForm.validateField('deviceLocationId');\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理取消\r\n    handleCancel() {\r\n      this.dialogVisible = false;\r\n      this.$emit('cancel');\r\n    },\r\n\r\n    // 处理关闭\r\n    handleClose() {\r\n      this.resetForm();\r\n      this.$emit('close');\r\n    },\r\n\r\n    // 处理确认\r\n    handleConfirm() {\r\n      // 确保数据格式正确\r\n      if (this.deviceLocationIds && this.deviceLocationIds.length > 0) {\r\n        this.form.deviceLocationId = Array.isArray(this.deviceLocationIds)\r\n          ? this.deviceLocationIds.join(',')\r\n          : this.deviceLocationIds;\r\n      } else {\r\n        this.form.deviceLocationId = [];\r\n      }\r\n\r\n      // 使用 nextTick 确保数据更新后再验证\r\n      this.$nextTick(() => {\r\n        this.$refs.bindPositionForm.validate(valid => {\r\n          if (valid) {\r\n            this.confirmLoading = true;\r\n            // 构建保存参数，包含 reginId\r\n            const saveParams = {\r\n              locationIds: this.form.deviceLocationId.split(\",\")\r\n            };\r\n            // 如果有 reginId，添加到参数中\r\n            if (this.reginId) {\r\n              saveParams.reginId = this.reginId;\r\n            }\r\n\r\n            this.$api[this.saveApiUrl](saveParams).then(data => {\r\n              this.dialogVisible = false;\r\n              this.confirmLoading = false;\r\n              this.resetForm();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '操作成功',\r\n              });\r\n              this.$emit('success', data);\r\n            }).catch(error => {\r\n              this.confirmLoading = false;\r\n              this.$emit('error', error);\r\n            });\r\n          } else {\r\n            console.log('表单验证失败');\r\n            return false;\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.form = {\r\n        deviceLocationId: '',\r\n        deviceLocationName: ''\r\n      };\r\n      this.deviceLocationIds = [];\r\n      this.$refs.bindPositionForm && this.$refs.bindPositionForm.resetFields();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.demo-ruleForm {\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n"]}]}