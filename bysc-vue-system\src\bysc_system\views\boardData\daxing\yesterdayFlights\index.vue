<template>
  <div class="table-container">
    <el-table
      :data="tableData"

      stripe
      width="100%"
    >
      <el-table-column
        prop="luggageDate"
        label="日期"
        min-width="180"
        header-align="center"
        align="center"
      />
      <el-table-column
        prop="name"
        label="名称"
        min-width="100"
        header-align="center"
        align="center"
      />
      <el-table-column
        prop="luggageQuantity"
        label="行李量(件)"
        min-width="100"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.luggageQuantity || 0 }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="250"
        fixed="right"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
             v-permission="'daxingYesterdayFlights-edit'"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <Panel ref="panel" @submit="getTableData" />
  </div>
</template>

<script>
import Panel from './component/panel.vue';

export default {
  name: 'yesterdayFlights',
  components: {
    Panel
  },
  data() {
    return {
      tableData: [],
      current: 1,
      limit: 10,
      total: 0
    };
  },
  methods: {
    handleEdit(row) {
      this.$refs.panel.open(row, 'edit');
    },
    handleSizeChange(val) {
      this.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.current = val;
      this.getTableData();
    },
    getTableData() {
      const params = {
        current: this.current,
        limit: this.limit,
        param: {}
      };
      this.$api['boardData/dashboardYesterdayLuggage-dx-page'](params).then(res => {
        this.tableData = res.list;
        this.total = res.total;
      });
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
