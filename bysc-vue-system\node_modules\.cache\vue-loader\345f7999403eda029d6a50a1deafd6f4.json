{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\index.vue?vue&type=template&id=f0d54b90&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\index.vue", "mtime": 1754296173293}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"visualOpsManagement/visualOpsManagement-page\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getColumn\"\n        :auto-load=\"true\"\n        ref=\"grid\">\n        <div slot=\"search\">\n          <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\n            <el-form-item label=\"图纸编号\">\n              <el-input\n                v-model.trim=\"searchForm.drawingCode\"\n                size=\"small\"\n                maxlength=\"32\"\n                placeholder=\"请输入\"\n                clearable\n                style=\"width: 200px\">\n              </el-input>\n            </el-form-item>\n            <el-form-item label=\"图纸名称\">\n              <el-input\n                v-model.trim=\"searchForm.drawingName\"\n                size=\"small\"\n                maxlength=\"32\"\n                placeholder=\"请输入\"\n                clearable\n                style=\"width: 200px\">\n              </el-input>\n            </el-form-item>\n            <el-form-item label=\"所属部门\">\n\n\n                  <el-select style=\"width:100%\" v-model=\"searchForm.belongDeptKey\" placeholder=\"请选择\" size=\"small\" filterable clearable>\n                <el-option :label=\"i.name\" :value=\"i.id\" v-for=\"i in deptList\" :key=\"i.id\">\n                </el-option>\n              </el-select>\n\n            </el-form-item>\n            <el-form-item>\n              <el-button size=\"small\" type=\"primary\" style=\"margin: 0 0 0 10px\" @click=\"searchTable\">查询</el-button>\n              <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        <div slot=\"action\">\n          <el-button v-permission=\"'drawingManagement_add'\" type=\"primary\" size=\"small\" @click=\"handleAdd\">\n            <i class=\"el-icon-plus\"></i> 新增图纸\n          </el-button>\n        </div>\n        <el-table\n          slot=\"table\"\n          slot-scope=\"{loading}\"\n          v-loading=\"loading\"\n          :data=\"tableData\"\n          stripe\n          ref=\"table\"\n          style=\"width: 100%\">\n          <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <template v-for=\"(item, index) in columns\">\n            <el-table-column\n              v-if=\"item.key == 'belongDept'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.minWidth ? item.minWidth : '150'\">\n              <template slot-scope=\"scope\">\n                <div>\n                  {{ scope.row.belongDept || '未分配' }}\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.minWidth ? item.minWidth : '150'\"\n              :align=\"item.align ? item.align : 'center'\">\n            </el-table-column>\n          </template>\n          <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"250\">\n            <template slot-scope=\"scope\">\n                              <el-button v-permission=\"'drawingManagement_areDrawing'\" type=\"text\" size=\"small\" @click=\"handleMange(scope.row)\">区域绘制</el-button>\n              <el-button v-permission=\"'drawingManagement_areaManage'\" type=\"text\" size=\"small\" @click=\"handleAraeMange(scope.row)\">区域管理</el-button>\n              <el-button v-permission=\"'drawingManagement_edit'\" type=\"text\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n              <el-button v-permission=\"'drawingManagement_view'\" type=\"text\" size=\"small\" @click=\"handleView(scope.row)\">查看区域</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n\n  <!-- 新增/编辑图纸弹窗 -->\n  <DrawingFormDialog\n    :visible.sync=\"dialogVisible\"\n    :mode=\"dialogMode\"\n    :edit-data=\"editData\"\n    @save=\"handleDialogSave\"\n    @close=\"handleDialogClose\">\n  </DrawingFormDialog>\n\n</div>\n", null]}