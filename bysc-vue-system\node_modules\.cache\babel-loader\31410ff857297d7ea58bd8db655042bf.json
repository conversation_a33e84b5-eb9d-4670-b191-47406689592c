{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaBinding\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaBinding\\index.vue", "mtime": 1754276220632}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport Grid from '@/components/Grid';\nimport { RouteParamsMixin, getRouteParams, saveRouteParams } from '@/utils/routeParams';\nimport _ from 'lodash';\nvar defaultSearchForm = {\n  regionCode: '',\n  regionName: '',\n  drawingId: '' // 添加 drawingId 参数\n};\nexport default {\n  name: 'AreaBinding',\n  mixins: [RouteParamsMixin],\n  components: {\n    Grid: Grid\n  },\n  created: function created() {\n    // 使用工具函数获取参数\n    var params = getRouteParams(this.$route, ['drawingId', 'drawingName'], 'areaBinding_params');\n    if (params.drawingId) {\n      this.searchForm.drawingId = params.drawingId;\n\n      // 保存参数，确保刷新后不丢失\n      saveRouteParams(this.$router, this.$route, {\n        drawingId: params.drawingId,\n        drawingName: params.drawingName\n      }, 'areaBinding_params');\n      console.log('获取到 drawingId 参数:', params.drawingId);\n    } else {\n      console.log('未获取到 drawingId 参数');\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\n    }\n  },\n  destroyed: function destroyed() {\n    this.searchEventBus.$off();\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    return {\n      searchForm: _.cloneDeep(defaultSearchForm),\n      columns: [{\n        title: '区域编号',\n        key: 'regionCode',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '区域名称',\n        key: 'regionName',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '创建人名称',\n        key: 'creatorName',\n        tooltip: true,\n        minWidth: 100\n      }, {\n        title: '创建时间',\n        key: 'createTime',\n        tooltip: true,\n        minWidth: 150\n      }],\n      tableData: []\n    };\n  },\n  methods: {\n    handleBack: function handleBack() {\n      // 返回时可以选择是否清理localStorage中的drawingId\n      // localStorage.removeItem('currentDrawingId');\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\n    },\n    // 绑定设备\n    handleBind: function handleBind(row) {\n      console.log('绑定设备:', row);\n      // 使用新的导航工具，自动保存参数\n      var _require = require('@/utils/routeParams'),\n        navigateWithParams = _require.navigateWithParams;\n      navigateWithParams(this.$router, 'deviceBinding', {\n        drawingId: this.searchForm.drawingId,\n        reginId: row.id\n      });\n    },\n    // 查看详情\n    handleView: function handleView(row) {\n      console.log('查看区域:', row);\n      this.$message.info('查看功能待开发');\n    },\n    // 搜索表格\n    searchTable: function searchTable() {\n      this.$refs.grid.queryData();\n    },\n    // 重置表格\n    resetTable: function resetTable() {\n      var _this = this;\n      // 保存当前的 drawingId 值\n      var currentDrawingId = this.searchForm.drawingId;\n\n      // 重置表单\n      this.searchForm = _.cloneDeep(defaultSearchForm);\n\n      // 恢复 drawingId 值\n      this.searchForm.drawingId = currentDrawingId;\n      this.$nextTick(function () {\n        _this.$refs.grid.query();\n      });\n    },\n    // 获取列配置\n    getColumn: function getColumn(e) {\n      var _this2 = this;\n      this.columns = e;\n      setTimeout(function () {\n        _this2.$refs.table.doLayout();\n      }, 100);\n    },\n    // 获取表格数据\n    getDatas: function getDatas(e) {\n      this.tableData = e;\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Grid", "RouteParamsMixin", "getRouteParams", "saveRouteParams", "_", "defaultSearchForm", "regionCode", "regionName", "drawingId", "name", "mixins", "components", "created", "params", "$route", "searchForm", "$router", "drawing<PERSON>ame", "console", "log", "$message", "warning", "destroyed", "searchEventBus", "$off", "data", "cloneDeep", "columns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "tableData", "methods", "handleBack", "push", "handleBind", "row", "_require", "require", "navigateWithParams", "reginId", "id", "handleView", "info", "searchTable", "$refs", "grid", "queryData", "resetTable", "_this", "currentDrawingId", "$nextTick", "query", "getColumn", "e", "_this2", "setTimeout", "table", "doLayout", "getDatas"], "sources": ["src/bysc_system/views/visualOpsManagement/areaBinding/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"visualOpsManagement/maintenanceRegion-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          :auto-load=\"true\"\r\n          ref=\"grid\">\r\n          <div slot=\"search\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n              <el-form-item label=\"区域编号\">\r\n                <el-input\r\n                  v-model.trim=\"searchForm.regionCode\"\r\n                  size=\"small\"\r\n                  maxlength=\"32\"\r\n                  placeholder=\"请输入\"\r\n                  clearable\r\n                  style=\"width: 200px\">\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"区域名称\">\r\n                <el-input\r\n                  v-model.trim=\"searchForm.regionName\"\r\n                  size=\"small\"\r\n                  maxlength=\"32\"\r\n                  placeholder=\"请输入\"\r\n                  clearable\r\n                  style=\"width: 200px\">\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button size=\"small\" type=\"primary\" style=\"margin: 0 0 0 10px\" @click=\"searchTable\">查询</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n\r\n           <div slot=\"action\">\r\n            <el-button  size=\"small\" @click=\"handleBack\">\r\n              <i class=\"el-icon-back\"></i> 返回\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-table\r\n            slot=\"table\"\r\n            slot-scope=\"{loading}\"\r\n            v-loading=\"loading\"\r\n            :data=\"tableData\"\r\n            stripe\r\n            ref=\"table\"\r\n            style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"item in columns\">\r\n              <el-table-column\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\">\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" size=\"small\" @click=\"handleBind(scope.row)\">绑定设备</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport {RouteParamsMixin, getRouteParams, saveRouteParams} from '@/utils/routeParams';\r\nimport _ from 'lodash';\r\n\r\nconst defaultSearchForm = {\r\n  regionCode: '',\r\n  regionName: '',\r\n  drawingId: '' // 添加 drawingId 参数\r\n};\r\n\r\nexport default {\r\n  name: 'AreaBinding',\r\n  mixins: [RouteParamsMixin],\r\n  components: {\r\n    Grid\r\n  },\r\n  created() {\r\n    // 使用工具函数获取参数\r\n    const params = getRouteParams(this.$route, ['drawingId', 'drawingName'], 'areaBinding_params');\r\n\r\n    if (params.drawingId) {\r\n      this.searchForm.drawingId = params.drawingId;\r\n\r\n      // 保存参数，确保刷新后不丢失\r\n      saveRouteParams(this.$router, this.$route, {\r\n        drawingId: params.drawingId,\r\n        drawingName: params.drawingName\r\n      }, 'areaBinding_params');\r\n\r\n      console.log('获取到 drawingId 参数:', params.drawingId);\r\n    } else {\r\n      console.log('未获取到 drawingId 参数');\r\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\r\n    }\r\n  },\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n\r\n    return {\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '区域编号',\r\n          key: 'regionCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '区域名称',\r\n          key: 'regionName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '创建人名称',\r\n          key: 'creatorName',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      tableData: []\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    handleBack() {\r\n      // 返回时可以选择是否清理localStorage中的drawingId\r\n      // localStorage.removeItem('currentDrawingId');\r\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\r\n    },\r\n\r\n    // 绑定设备\r\n    handleBind(row) {\r\n      console.log('绑定设备:', row);\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'deviceBinding', {\r\n        drawingId: this.searchForm.drawingId,\r\n        reginId: row.id,\r\n      });\r\n    },\r\n    // 查看详情\r\n    handleView(row) {\r\n      console.log('查看区域:', row);\r\n      this.$message.info('查看功能待开发');\r\n    },\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.queryData();\r\n    },\r\n    // 重置表格\r\n    resetTable() {\r\n      // 保存当前的 drawingId 值\r\n      const currentDrawingId = this.searchForm.drawingId;\r\n\r\n      // 重置表单\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n\r\n      // 恢复 drawingId 值\r\n      this.searchForm.drawingId = currentDrawingId;\r\n\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    // 获取列配置\r\n    getColumn(e) {\r\n      this.columns = e;\r\n      setTimeout(() => {\r\n        this.$refs.table.doLayout();\r\n      }, 100);\r\n    },\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n</style>\r\n"], "mappings": "AAoFA,OAAAA,GAAA;AACA,OAAAC,IAAA;AACA,SAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA;AACA,OAAAC,CAAA;AAEA,IAAAC,iBAAA;EACAC,UAAA;EACAC,UAAA;EACAC,SAAA;AACA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAT,gBAAA;EACAU,UAAA;IACAX,IAAA,EAAAA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,MAAA,GAAAX,cAAA,MAAAY,MAAA;IAEA,IAAAD,MAAA,CAAAL,SAAA;MACA,KAAAO,UAAA,CAAAP,SAAA,GAAAK,MAAA,CAAAL,SAAA;;MAEA;MACAL,eAAA,MAAAa,OAAA,OAAAF,MAAA;QACAN,SAAA,EAAAK,MAAA,CAAAL,SAAA;QACAS,WAAA,EAAAJ,MAAA,CAAAI;MACA;MAEAC,OAAA,CAAAC,GAAA,sBAAAN,MAAA,CAAAL,SAAA;IACA;MACAU,OAAA,CAAAC,GAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA,CAAAC,IAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,KAAAF,cAAA,OAAAxB,GAAA;IAEA;MACAgB,UAAA,EAAAX,CAAA,CAAAsB,SAAA,CAAArB,iBAAA;MACAsB,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EAEAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA;MACA,KAAAlB,OAAA,CAAAmB,IAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACAnB,OAAA,CAAAC,GAAA,UAAAkB,GAAA;MACA;MACA,IAAAC,QAAA,GAAAC,OAAA;QAAAC,kBAAA,GAAAF,QAAA,CAAAE,kBAAA;MACAA,kBAAA,MAAAxB,OAAA;QACAR,SAAA,OAAAO,UAAA,CAAAP,SAAA;QACAiC,OAAA,EAAAJ,GAAA,CAAAK;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAN,GAAA;MACAnB,OAAA,CAAAC,GAAA,UAAAkB,GAAA;MACA,KAAAjB,QAAA,CAAAwB,IAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,SAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA;MACA,IAAAC,gBAAA,QAAApC,UAAA,CAAAP,SAAA;;MAEA;MACA,KAAAO,UAAA,GAAAX,CAAA,CAAAsB,SAAA,CAAArB,iBAAA;;MAEA;MACA,KAAAU,UAAA,CAAAP,SAAA,GAAA2C,gBAAA;MAEA,KAAAC,SAAA;QACAF,KAAA,CAAAJ,KAAA,CAAAC,IAAA,CAAAM,KAAA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,KAAA7B,OAAA,GAAA4B,CAAA;MACAE,UAAA;QACAD,MAAA,CAAAV,KAAA,CAAAY,KAAA,CAAAC,QAAA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAAL,CAAA;MACA,KAAAvB,SAAA,GAAAuB,CAAA;IACA;EACA;AACA"}]}