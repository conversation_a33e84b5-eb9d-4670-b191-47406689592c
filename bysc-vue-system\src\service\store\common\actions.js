/* eslint-disable max-nested-callbacks */
import api from '@/plugins/api';
import router from '@/plugins/router';
import {Loading} from 'element-ui';
// eslint-disable-next-line no-duplicate-imports
import {
  routerInstance,
  createNewRoute
} from '@/plugins/router';
// import permission from '@/routes/permission-parking'
// 引入系统路由表
import {
  ROUTERS_PERMISSION
} from '@/global/module-config';
// import qs from 'qs'
// import axios from 'axios'
import md5 from 'js-md5';
import _ from 'lodash';
import axios from 'axios';
import store from '@/plugins/store';
import jsFileDownLoad from 'js-file-download';
import localCache from '@/utils/storage';
import CryptoJS from 'crypto-js';
var flags = true;

// 解密函数 - 用于解密密钥
function decryptKey(encryptedKey) {
  try {
    return atob(encryptedKey);
  } catch (error) {
    console.error('Key decryption failed:', error);
    return '';
  }
}

// 加密函数
function encryptVud(username) {
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 1000000); // 添加6位随机数
  const str = `${username}+${timestamp}${random}`;

  // 使用正确的加密后的key
  const encryptedKey = "JEJvd2VpPUAyMDI1Li44OA==";
  const cryptoKey = decryptKey(encryptedKey);

  try {
    const key = CryptoJS.enc.Utf8.parse(cryptoKey);
    const iv = CryptoJS.enc.Utf8.parse(cryptoKey);
    const strUtf8 = CryptoJS.enc.Utf8.parse(str);
    const encrypted = CryptoJS.AES.encrypt(strUtf8, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
  } catch (error) {
    console.error('Vud encryption failed:', error);
    return '';
  }
}

// 添加 JWT token 解析函数
function parseJwt(token) {
  try {
    if (!token) {
      return null;
    }
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Token parsing failed:', error);
    return null;
  }
}

export default {
  login({
    commit
  }, loginInfo) {
    let flattenTree = tree => {
      const result = [];
      for (const node of tree) {
        result.push(node);
        if (node.children) {
          result.push(...flattenTree(node.children));
        }
      }
      return result;
    };
    commit('clearStore');
    let data = {};
    if (loginInfo) {
      data = {
        username: loginInfo.username,
        password: md5(loginInfo.password),
        grant_type: 'password',
        fromPage: 'noDesktop'
      };
    }
    let username = localCache.getLocal('nowUserName');
    if (!username) {
      username = loginInfo.username;
    }
    // if (location.pathname === '/social/max_key/callback') {
    //   return;
    // }
    var stype = location.pathname.split('/')[2];
    var code = location.search.split('=')[1];
    let path = location.pathname.split('/')[1];
    if (path === 'social') {
      console.log(path, '----------', location);
      if (!flags) {
        return;
      }
      flags = false;
      api['account/socialcallback']({
        code: code,
        socialType: stype
      }).then(data => {
        const bindResult = data;
        if (bindResult && bindResult !== '') {
          if (bindResult.bound) {
            const loginParams = {
              grant_type: 'social',
              social_key: bindResult.key
            };
            let promise = api['account/getToken'](loginParams).then(data => {
              localCache.setLocal('userToken', data.token);
              localCache.setLocal('refreshToken', data.refreshToken);
              localCache.setLocal('userExpir', data.exp);
              api['account/getUsers']().then(e => {
                let btnarr = [];
                e.buttonList.forEach(per => {
                  btnarr.push(per.resourcePageName);
                });
                localCache.setLocal('btnPermissions', btnarr);
                localCache.setLocal('nowUserName', e.account);
                localCache.setSession('username', e.account); // 存储用户信息
                commit('setPermissionList', _.cloneDeep(e.permissions));
                commit('setUserInfo', e);
                let newpermissions = [];
                if (e.menuTree) {
                  newpermissions = flattenTree(e.menuTree);
                }

                commit('setPermissions', newpermissions);
                // route升级，讲vue2的addRoutes换为addRoute
                ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e,
                  index) => {
                  routerInstance.addRoute(index, e);
                });
                // routerInstance.addRoutes(routes)
                setTimeout(() => {
                  let index = e.menuTree.findIndex(r => {
                    return r.resourcePageName === 'system/cfg';
                  });
                  if (e.menuTree[index].children[0].children) {
                    if (!e.menuTree[index].children[0].children[0].children) {
                      router.push({
                        path: e.menuTree[index].children[0].children[0].resourcePath
                      });
                    } else {
                      router.push({
                        path: e.menuTree[index].children[0].resourcePath
                      });
                    }
                  } 
                }, 200);
                commit('setMenuList', e.menuTree);
              });
            });
            return promise;
          }
        }
      });
      throw '';
      // return promises;
    }
    let promise = loginInfo ? api['account/getToken'](data) : api['account/getUsers']();
    promise.then(data => {
      if (loginInfo) {
        loginInfo && localCache.setLocal('userToken', data.token);
        loginInfo && localCache.setLocal('refreshToken', data.refreshToken);
        localCache.setLocal('userExpir', data.exp);
        api['account/getUsers']().then(e => {
          let btnarr = [];
          e.buttonList.forEach(per => {
            btnarr.push(per.resourcePageName);
          });
          localCache.setLocal('btnPermissions', btnarr);
          localCache.setLocal('nowUserName', e.account);
          localCache.setSession('username', e.account); // 存储用户信息
          commit('setPermissionList', _.cloneDeep(e.permissions));
          commit('setUserInfo', e);
          let newpermissions = [];
          if (e.menuTree) {
            newpermissions = flattenTree(e.menuTree);
          }

          commit('setPermissions', newpermissions);
          // route升级，讲vue2的addRoutes换为addRoute
          ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e,
            index) => {
            routerInstance.addRoute(index, e);
          });
          // routerInstance.addRoutes(routes)
          setTimeout(() => {
            let index = e.menuTree.findIndex(r => {
              return r.resourcePageName === 'system/cfg';
            });
            if (e.menuTree[index].children[0].children) {
              if (!e.menuTree[index].children[0].children[0].children) {
                router.push({
                  path: e.menuTree[index].children[0].children[0].resourcePath
                });
              } else {
                router.push({
                  path: e.menuTree[index].children[0].resourcePath
                });
              }
            } 
          }, 200);

          commit('setMenuList', e.menuTree);
        });
      } else {
        if (!data) {
          return;
        }
        let btnarr = [];
        data.buttonList.forEach(per => {
          btnarr.push(per.resourcePageName);
        });
        localCache.setLocal('btnPermissions', btnarr);
        localCache.setLocal('nowUserName', data.account);
        localCache.setSession('username', data.account); // 存储用户信息
        commit('setPermissionList', _.cloneDeep(data.permissions));
        commit('setUserInfo', data);
        let newpermissions = [];
        if (data.menuTree) {
          data.menuTree.forEach(element => {
            newpermissions.push(element);
            if (element.children) {
              element.children.forEach(k => {
                newpermissions.push(k);
                if (k.children) {
                  k.children.forEach(i => {
                    newpermissions.push(i);
                    if (i.children) {
                      i.children.forEach(j => {
                        newpermissions.push(j);
                      });
                    }
                  });
                }
              });
            }
          });
        }
        // route升级，讲vue2的addRoutes换为addRoute
        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {
          routerInstance.addRoute(index, e);
        });
        // routerInstance.addRoutes(routes)
        commit('setPermissions', newpermissions);
        commit('setMenuList', data.menuTree);
      }
    });
    return promise;
  },
  checkToken({
    commit
  }, path) {
    let flattenTree = tree => {
      const result = [];
      for (const node of tree) {
        result.push(node);
        if (node.children) {
          result.push(...flattenTree(node.children));
        }
      }
      return result;
    };
    api['account/getUsers']().then(e => {
      let btnarr = [];
      e.buttonList.forEach(per => {
        btnarr.push(per.resourcePageName);
      });
      localCache.setLocal('btnPermissions', btnarr);
      localCache.setLocal('nowUserName', e.account);
      localCache.setSession('username', e.account); // 存储用户信息
      commit('setPermissionList', _.cloneDeep(e.permissions));
      commit('setUserInfo', e);
      let newpermissions = [];
      if (e.menuTree) {
        newpermissions = flattenTree(e.menuTree);
      }

      commit('setPermissions', newpermissions);
      // route升级，讲vue2的addRoutes换为addRoute
      ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {
        routerInstance.addRoute(index, e);
      });
      // routerInstance.addRoutes(routes)
      setTimeout(() => {
        let index = e.menuTree.findIndex(r => {
          return r.resourcePageName === 'system/cfg';
        });
        if (e.menuTree[index].children[0].children) {
          if (!e.menuTree[index].children[0].children[0].children) {
            router.push({
              path: e.menuTree[index].children[0].children[0].resourcePath
            });
          } else {
            router.push({
              path: e.menuTree[index].children[0].resourcePath
            });
          }
        } 
      }, 200);
      // setTimeout(() => {
      //   router.push({
      //     path: path
      //   });
      // }, 200);

      commit('setMenuList', e.menuTree);
    });
  },
  maxkeylogin({
    commit
  }, loginInfo) {
    let flattenTree = tree => {
      const result = [];
      for (const node of tree) {
        result.push(node);
        if (node.children) {
          result.push(...flattenTree(node.children));
        }
      }
      return result;
    };
    api['account/getToken'](loginInfo).then(data => {
      localCache.setLocal('userToken', data.token);
      localCache.setLocal('refreshToken', data.refreshToken);
      localCache.setLocal('userExpir', data.exp);
      api['account/getUsers']().then(e => {
        let btnarr = [];
        e.buttonList.forEach(per => {
          btnarr.push(per.resourcePageName);
        });
        localCache.setLocal('btnPermissions', btnarr);
        localCache.setLocal('nowUserName', e.account);
        localCache.setSession('username', e.account); // 存储用户信息
        commit('setPermissionList', _.cloneDeep(e.permissions));
        commit('setUserInfo', e);
        let newpermissions = [];
        if (e.menuTree) {
          newpermissions = flattenTree(e.menuTree);
        }
        commit('setPermissions', newpermissions);
        // route升级，讲vue2的addRoutes换为addRoute
        ROUTERS_PERMISSION(newpermissions).length && ROUTERS_PERMISSION(newpermissions).forEach((e, index) => {
          routerInstance.addRoute(index, e);
        });
        // routerInstance.addRoutes(routes)
        setTimeout(() => {
          let index = e.menuTree.findIndex(r => {
            return r.resourcePageName === 'system/cfg';
          });
          if (e.menuTree[index].children[0].children) {
            if (!e.menuTree[index].children[0].children[0].children) {
              router.push({
                path: e.menuTree[index].children[0].children[0].resourcePath
              });
            } else {
              router.push({
                path: e.menuTree[index].children[0].resourcePath
              });
            }
          } 
        }, 200);

        commit('setMenuList', e.menuTree);
      });
    });
    return promise;
  },
  handleLogOut({
    commit
  }) {
    let header = {
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'content-type': 'application/x-www-form-urlencoded'
      }
    };
    let newRouter = createNewRoute();
    routerInstance.matcher = newRouter.matcher;
    api['account/logout']({
      refreshToken: $cookies.get('refreshToken')
    }, header).then(data => {});
    commit('clearStore');
    // 执行退出登录

  },
  setProgress({
    commit
  }, progressObj) {
    commit('setProgress', progressObj);
  },
  delProgress({
    commit
  }, props) {
    commit('delProgress', props);
  },
  download({
    commit
  }, data) {
    let downProgress = {};
    let uniSign = new Date().getTime() + '';
    const loading = Loading.service({
      lock: true,
      text: '导出文件中，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)',
      customClass: 'download-loading-class' // 添加自定义class
    });

    // 获取用户名和生成Vud
    const token = $cookies.get('userToken');
    const tokenData = parseJwt(token);
    const username = tokenData ? tokenData.account : '';
    const finalUsername = username || $cookies.get('nowUserName') || localCache.getLocal('nowUserName') || '';
    const Vud = encryptVud(finalUsername);

    const handleError = e => {
      loading.close();
      this.$message.error('该文件无法下载');
      console.error('Download failed:', e);
    };

    if (data.form) {
      axios.post(
        data.url, data.form, {
          responseType: 'blob',
          headers: {
            'Authorization': 'Bearer ' + $cookies.get('userToken'),
            TenantId: 0,
            Vud: Vud
          },
          onDownloadProgress(progress) {
            downProgress = Math.round(100 * progress.loaded / progress.total);
            store.dispatch('setProgress', {
              path: uniSign,
              'progress': downProgress
            });
          }
        }).then(res => {
        loading.close();
        if (data.downLoad) {
          jsFileDownLoad(res.data, data.downLoad);
        } else {
          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);
        }
      }).catch(handleError);
    } else {
      axios.get(
        data.url, {
          responseType: 'blob',
          headers: {
            'Authorization': 'Bearer ' + $cookies.get('userToken'),
            TenantId: 0,
            Vud: Vud
          },
          onDownloadProgress(progress) {
            downProgress = Math.round(100 * progress.loaded / progress.total);
            store.dispatch('setProgress', {
              path: uniSign,
              'progress': downProgress
            });
          }
        }).then(res => {
        loading.close();
        if (data.downLoad) {
          jsFileDownLoad(res.data, data.downLoad);
        } else {
          jsFileDownLoad(res.data, data.url.split('/')[data.url.split('/').length - 1]);
        }
      }).catch(handleError);
    }
  },
  getUnreadMessageCount() {
    // 获取未读信息数量
  },
  loginFun() {}

  // updateUserInfo ({ commit }) {
  //   api['common/userInfo']().then(data => {
  //     commit('setUserName', data.userName)
  //   })
  // }
};
