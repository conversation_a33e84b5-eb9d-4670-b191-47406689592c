<template>
  <div class="table-container">
    <el-table
      :data="tableData"

      stripe
      width="100%"
      @selection-change="handleSelectionChange"
    >
      <!-- 复选框列 -->
      <!-- <el-table-column
        type="selection"
        width="55"
        header-align="center"
        align="center"
      /> -->

      <!-- 名称列 -->
      <el-table-column
        prop="leaderDutyNo"
        label="名称"
        min-width="180"
        header-align="center"
        align="center"
      />

      <!-- 年份列 -->
      <el-table-column
        prop="year"
        label="年份"
        min-width="100"
        header-align="center"
        align="center"
      />

      <!-- 月份列 -->
      <el-table-column
        prop="month"
        label="月份"
        min-width="100"
        header-align="center"
        align="center"
      />

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        width="250"
        fixed="right"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            v-permission="'daxingForecastManage-edit'"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            size="small"
            v-permission="'daxingForecastManage-look'"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <Panel ref="panel" />
  </div>
</template>

<script>
import Panel from './component/panel.vue';

export default {
  name: 'DataTable',
  components: {
    Panel
  },
  data() {
    return {
      tableData: [],
      current: 1,
      limit: 10,
      total: 0,
      selectedRows: []
    };
  },
  methods: {
    // 选择项变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 编辑操作
    handleEdit(row) {
      this.$refs.panel.open(row, 'edit');
    },

    // 查看操作
    handleView(row) {
      this.$refs.panel.open(row, 'view');
    },

    // 每页条数变化
    handleSizeChange(val) {
      this.limit = val;
      this.getTableData();
    },

    // 页码变化
    handleCurrentChange(val) {
      this.current = val;
      this.getTableData();
    },

    // 获取表格数据
    getTableData() {
      const params = {
        current: this.current,
        limit: this.limit,
        param: {}
      };
      this.$api['boardData/dashboardLeaderDuty-dx-page'](params).then(res => {
        this.tableData = res.list;
        this.total = res.total;
      });
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
