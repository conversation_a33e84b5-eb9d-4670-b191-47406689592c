<template>
  <div>
    <el-row>
      <el-col :span="5">
        <div style="padding: 20px">
          <el-input
            placeholder="输入关键字进行过滤"
            size="small"
            v-model="filterText"
          >
          </el-input>
          <el-tree
            class="filter-tree"
            :data="treedata"
            node-key="id"
            :props="defaultProps"
            :highlight-current="true"
            :expand-on-click-node="false"
            default-expand-all
            @node-click="checkChange"
            :filter-node-method="filterNode"
            style="maxHeight:500px;overflow:auto;"
            ref="tree"
          >
          </el-tree>
        </div>
      </el-col>
      <el-col :span="19">
        <Grid
          api="commonModule/sysOrganzizationKingdeeRel-selectPage"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="组织名称">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.organizationName"
                  size="small"
                  maxlength="32"
                  placeholder="请输入组织名称"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'organization_bind'"
                  @click="handleBind(scope.row)"
                  type="text"
                  size="small"
                  >绑定金蝶部门</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>

    <el-dialog :title="'绑定金蝶部门'" :visible.sync="organizationDialog" :close-on-click-modal="false" width="40%">
      <div>
        <el-form
          :model="organizationForm"
          :rules="organizationFormRules"
          ref="organizationForm"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="绑定金蝶部门" prop="kingdeeDept">
                <el-select
                  v-model="kingdeeDept"
                  placeholder="请选择绑定金蝶部门"
                  clearable
                  filterable
                  style="width: 100%; margin: 0 10px 0 0"
                  size="small"
                  multiple
                >
                  <el-option
                    v-for="(item, index) in kingdeeDeptList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="organizationDialog = false"
          >取 消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :loading="organizationOkLoading"
          @click="submitOrganizationForm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    const validateKingdeeDept = (rule, value, callback) => {
      if (!this.kingdeeDept || this.kingdeeDept.length == 0) {
        callback(new Error('请选择绑定绑定供应商'));
      } else {
        callback();
      }
    };
    return {
      filterText: '',
      treedata: [],
      defaultProps: {
        children: "children",
        label: "organizationName",
      },

      kingdeeDept: [],
      kingdeeDeptList: [],
      organizationFormRules: {
        kingdeeDept: [
          {required: true, validator: validateKingdeeDept, trigger: 'blur,change'},
        ],
      },
      organizationForm: {},
      organizationDialog: false,
      organizationOkLoading: false,

      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '组织编码',
          key: 'organizationKey',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '组织名称',
          key: 'organizationName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '父组织',
          key: 'parentName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '组织简介',
          key: 'comments',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '绑定金蝶部门编码',
          key: 'kingdeeCode',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '绑定金蝶部门名称',
          key: 'kingdeeName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '最后更新时间',
          key: 'updateTime',
          tooltip: true,
          minWidth: 170,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    this.getTreeData();
    this.getKingdeeDeptList();
  },
  methods: {
    getTreeData() {
      this.$api["systems/organizationTree"]({
      }).then(
        data => {
          this.treedata = data;
        }
      );
    },
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data.classificationName.indexOf(value) !== -1;
    },
    checkChange(d, n, p) {
      this.searchForm.parentId = d.id;
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.$refs.tree.setCurrentKey(null);
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },

    getKingdeeDeptList() {
      this.$api["commonModule/KingdeeDepartment-all"]({}).then(data => {
        this.kingdeeDeptList = data;
      });
    },
    handleBind(row) {
      this.kingdeeDept = [];
      this.organizationForm = {
        organizationId: row.id,
        kingdeeCode: '',
        kingdeeName: ''
      };
      let arr = row.kingdeeCode ? row.kingdeeCode.split(',') : [];
      arr.forEach(e => {
        this.kingdeeDept.push(e);
      });
      this.organizationDialog = true;
    },
    submitOrganizationForm() {
      let that = this;
      this.$refs.organizationForm.validate(valid => {
        if (valid) {
          this.organizationOkLoading = true;
          if (this.kingdeeDept.length > 0) {
            this.organizationForm.kingdeeCode = this.kingdeeDept.join(',');
            let arr1 = this.kingdeeDeptList.filter(v => this.kingdeeDept.some(val => val === v.value));
            let arr2 = [];
            arr1.forEach(e => {
              arr2.push(e.label);
            });
            this.organizationForm.kingdeeName = arr2.join(',');
          }
          this.$api['commonModule/sysOrganzizationKingdeeRel-save'](this.organizationForm).then(data => {
            this.organizationDialog = false;
            this.organizationOkLoading = false;
            this.$message({
              type: 'success',
              message: '操作成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.organizationOkLoading = false;
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
