{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue", "mtime": 1754276220628}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport _ from 'lodash';\nimport Grid from \"@/components/Grid\";\nimport Panel from \"./component/panel.vue\";\nvar defaultSearchForm = {};\nexport default {\n  name: 'CapitalSafetyTable',\n  components: {\n    Grid: Grid,\n    Panel: Panel\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    return {\n      tableData: [],\n      searchForm: _.cloneDeep(defaultSearchForm),\n      columns: [{\n        title: \"日期\",\n        key: \"designDate\",\n        width: \"150\"\n      }, {\n        title: \"总分值\",\n        key: \"totalScore\",\n        width: \"150\"\n      }]\n    };\n  },\n  methods: {\n    // 编辑操作\n    handleEdit: function handleEdit(row) {\n      console.log(row, '---');\n      this.$refs.panel.open(row, 'edit');\n    },\n    // 搜索表格\n    searchTable: function searchTable() {\n      this.$refs.grid.query();\n    },\n    // 重置表格\n    resetTable: function resetTable() {\n      var _this = this;\n      this.searchForm = _.cloneDeep(defaultSearchForm);\n      this.$nextTick(function () {\n        _this.$refs.grid.query();\n      });\n    },\n    // 获取列配置\n    getcolumn: function getcolumn(e) {\n      this.columns = e;\n    },\n    // 获取表格数据\n    getDatas: function getDatas(e) {\n      this.tableData = e;\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "_", "Grid", "Panel", "defaultSearchForm", "name", "components", "data", "searchEventBus", "tableData", "searchForm", "cloneDeep", "columns", "title", "key", "width", "methods", "handleEdit", "row", "console", "log", "$refs", "panel", "open", "searchTable", "grid", "query", "resetTable", "_this", "$nextTick", "getcolumn", "e", "getDatas"], "sources": ["src/bysc_system/views/techDashboard/techData/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"table-container\">\r\n    <Grid\r\n      api=\"techDashboard/techManagementRatingDesign-page\"\r\n      :event-bus=\"searchEventBus\"\r\n      :search-params=\"searchForm\"\r\n      :newcolumn=\"columns\"\r\n      @datas=\"getDatas\"\r\n      @columnChange=\"getcolumn\"\r\n      ref=\"grid\"\r\n    >\r\n      <div slot=\"search\">\r\n        <!-- <el-date-picker\r\n          style=\"width: 200px; margin: 0 10px 0 0\"\r\n          v-model=\"searchForm.date\"\r\n          type=\"month\"\r\n          value-format=\"yyyy-MM\"\r\n          placeholder=\"选择月份\"\r\n        ></el-date-picker>\r\n        <el-button\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          style=\"margin: 0 0 0 10px\"\r\n          @click=\"searchTable\"\r\n        >搜索</el-button>\r\n        <el-button size=\"small\" @click=\"resetTable\">重置</el-button> -->\r\n      </div>\r\n\r\n      <el-table\r\n        slot=\"table\"\r\n        slot-scope=\"{ loading }\"\r\n        v-loading=\"loading\"\r\n        :data=\"tableData\"\r\n        stripe\r\n        style=\"width: 100%\"\r\n        :border=\"true\"\r\n      >\r\n        <el-table-column\r\n          fixed=\"left\"\r\n          :align=\"'center'\"\r\n          label=\"序号\"\r\n          type=\"index\"\r\n          width=\"50\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-for=\"item in columns\"\r\n          :key=\"item.key\"\r\n          :show-overflow-tooltip=\"true\"\r\n          :prop=\"item.key\"\r\n          :label=\"item.title\"\r\n          :min-width=\"item.width ? item.width : '150'\"\r\n          :align=\"item.align ? item.align : 'center'\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          fixed=\"right\"\r\n          align=\"center\"\r\n          label=\"操作\"\r\n          width=\"100\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              v-permission=\"'techEditBtn'\"\r\n              @click=\"handleEdit(scope.row)\"\r\n            >编辑</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </Grid>\r\n\r\n    <Panel ref=\"panel\" @refresh=\"searchTable\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport _ from 'lodash';\r\nimport Grid from \"@/components/Grid\";\r\nimport Panel from './component/panel.vue';\r\n\r\nconst defaultSearchForm = {\r\n};\r\n\r\nexport default {\r\n  name: 'CapitalSafetyTable',\r\n  components: {\r\n    Grid,\r\n    Panel\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      tableData: [],\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: \"日期\",\r\n          key: \"designDate\",\r\n          width: \"150\",\r\n        },\r\n        {\r\n          title: \"总分值\",\r\n          key: \"totalScore\",\r\n          width: \"150\",\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑操作\r\n    handleEdit(row) {\r\n      console.log(row, '---');\r\n\r\n      this.$refs.panel.open(row, 'edit');\r\n    },\r\n\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    // 重置表格\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    // 获取列配置\r\n    getcolumn(e) {\r\n      this.columns = e;\r\n    },\r\n\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-container {\r\n  padding: 20px;\r\n  width: 100%;\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n\r\n  .el-table {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>"], "mappings": "AA8EA,OAAAA,GAAA;AACA,OAAAC,CAAA;AACA,OAAAC,IAAA;AACA,OAAAC,KAAA;AAEA,IAAAC,iBAAA,IACA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,IAAA,EAAAA,IAAA;IACAC,KAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,KAAAC,cAAA,OAAAR,GAAA;IACA;MACAS,SAAA;MACAC,UAAA,EAAAT,CAAA,CAAAU,SAAA,CAAAP,iBAAA;MACAQ,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,GAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MAEA,KAAAG,KAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAL,GAAA;IACA;IAEA;IACAM,WAAA,WAAAA,YAAA;MACA,KAAAH,KAAA,CAAAI,IAAA,CAAAC,KAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,KAAAlB,UAAA,GAAAT,CAAA,CAAAU,SAAA,CAAAP,iBAAA;MACA,KAAAyB,SAAA;QACAD,KAAA,CAAAP,KAAA,CAAAI,IAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAI,SAAA,WAAAA,UAAAC,CAAA;MACA,KAAAnB,OAAA,GAAAmB,CAAA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAAD,CAAA;MACA,KAAAtB,SAAA,GAAAsB,CAAA;IACA;EACA;AACA"}]}