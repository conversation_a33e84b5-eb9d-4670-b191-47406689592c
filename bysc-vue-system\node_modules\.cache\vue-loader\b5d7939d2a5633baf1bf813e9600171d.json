{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\component\\BindPositionDialog\\index.vue?vue&type=template&id=665257df&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\component\\BindPositionDialog\\index.vue", "mtime": 1754276220637}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<el-dialog\n  :title=\"title\"\n  :visible.sync=\"dialogVisible\"\n  :close-on-click-modal=\"false\"\n  :width=\"width\"\n  @close=\"handleClose\"\n>\n  <div>\n    <el-form\n      :model=\"form\"\n      :rules=\"formRules\"\n      ref=\"bindPositionForm\"\n      label-width=\"100px\"\n      class=\"demo-ruleForm\"\n    >\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"资产位置\" prop=\"deviceLocationId\">\n            <ltree\n              v-if=\"dialogVisible\"\n              ref=\"searchLtree\"\n              :cnvalue=\"form.deviceLocationName\"\n              :multiple=\"multiple\"\n              :isLazy=\"isLazy\"\n              :apiUrl=\"apiUrl\"\n              :bound-location-ids=\"boundLocationIds\"\n              @getSelectCnData=\"getSelectCnData\"\n              @getSelectData=\"getSelectData\"\n              :value=\"deviceLocationIds\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n  <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button size=\"small\" @click=\"handleCancel\">\n      {{ cancelText }}\n    </el-button>\n    <el-button\n      size=\"small\"\n      type=\"primary\"\n      :loading=\"confirmLoading\"\n      @click=\"handleConfirm\"\n    >\n      {{ confirmText }}\n    </el-button>\n  </span>\n</el-dialog>\n", null]}