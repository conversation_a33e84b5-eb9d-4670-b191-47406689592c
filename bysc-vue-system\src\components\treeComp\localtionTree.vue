<template>
  <div class="app-container">
    <el-popover
      v-model="showSearchDeptTree"
      placement="right"
      width="800"
      trigger="click"
      @show="showSearchTreeEvent"
      @hide="hideSearchTreeEvent"
    >
      <div>
        <el-input
          size="small"
          style="width: 100%; margin-bottom: 8px"
          placeholder="请输入关键字搜索"
          v-model="locationDesc"
          @input="onSearch"
          @clear="onSearch"
          clearable
        >
          default-expand-all
        </el-input>
      </div>
      <div style="height: 460px; overflow-y: scroll">
        <el-tree
          :props="props"
          :data="deptAndUserData"
          :show-checkbox="showCheckbox"
          :load="loadNode"
          v-bind="$attrs"
          v-on="$listeners"
          :lazy="isLazy"
          @node-click="handleNodeClick"
          @check="getTreeDatas"
          :highlight-current="highlightCurrent"
          node-key="primaryId"
          :key="setTimer"
          check-strictly
          ref="tree"
          :default-checked-keys="Array.isArray(value) ? value : [value]"
          v-if="showSearchDeptTree"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>
              <span style="margin-left: 2px"
                >{{ data.label }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{
                  node.label
                }}</span
              >
            </span>
          </span>
        </el-tree>
      </div>

      <el-input
        size="small"
        style="width: 100%; margin-left: 8px"
        v-model="selectValue"
        type="textarea"
        :placeholder="treeplaceholder"
        readonly
        clearable
        @clear="clearSelect"
        slot="reference"
      ></el-input>

      <span class="dialog-footer" v-if="$attrs.multiple">
        <el-button size="small" @click="showSearchDeptTree = false"
          >取 消</el-button
        >
        <el-button size="small" type="primary" @click="submitBindPositionForm"
          >确 定</el-button
        >
      </span>
    </el-popover>
  </div>
</template>

<script>
import {keys} from "lodash";

export default {
  data() {
    return {
      locationDesc: "",
      treeData: [],
      selectedNode: null,
      deptAndUserData: [],
      nodes: null,
      resolves: null,
      setTimer: null,
      timer: null,
      selectedNodes: [],
      selectedUserList: [],
      selectValue: "",
      showSearchDeptTree: false,
      checkedTreeList: [], // 选中的值
      checkedListKey: [],
    };
  },
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          label: "primaryCode",
          children: "children",
          isLeaf: "leaf",
        };
      },
    },
    value: {
      type: String | Array,
      default: "",
    },
    cnvalue: {
      type: String,
      default: "",
    },
    treeplaceholder: {
      type: String,
      default: "请选择数据",
    },
    highlightCurrent: {
      type: Boolean,
      default() {
        return true;
      },
    },
    isLazy: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showCheckbox: {
      type: Boolean,
      default() {
        return true;
      },
    },
    apiUrl: {
      type: String,
      default: "firstLineDept/get-loc-info-lazy-tree",
    },
    // 已绑定的位置ID数组，用于设置节点的disabled状态
    boundLocationIds: {
      type: Array,
      default: () => []
    },
  },
  watch: {
    value(val) {
      if (!val) {
        if (!this.$attrs.multiple) {
          this.clearSelect();
        } else {
          this.$refs.tree && this.$refs.tree.setCheckedKeys(val);
        }
      } else {
        // this.selectValue = filterTree[0] ? filterTree[0][this.treeProps.label] : '';
      }
    },
    cnvalue(val) {
      this.selectValue = val;
    },
  },
  mounted() {
    this.setTimer = new Date().getTime();
    this.selectValue = this.cnvalue;
  },
  methods: {
    // 递归设置节点的disabled属性
    setNodeDisabled(nodes) {
      if (!nodes || !Array.isArray(nodes)) {
        return nodes;
      }

      return nodes.map(node => {
        // 创建节点的副本以避免直接修改原数据
        const newNode = {...node};

        // 检查当前节点的primaryId是否在已绑定的位置ID数组中
        if (this.boundLocationIds && this.boundLocationIds.includes(newNode.primaryId)) {
          newNode.disabled = true;
        } else {
          newNode.disabled = false;
        }

        // 如果有子节点，递归处理
        if (newNode.children && Array.isArray(newNode.children)) {
          newNode.children = this.setNodeDisabled(newNode.children);
        }

        return newNode;
      });
    },

    hideSearchTreeEvent() {
      this.locationDesc = "";
    },
    // 展示树时
    showSearchTreeEvent() {
      this.$api[this.apiUrl]({
        parentId: 0,
      }).then(data => {
        // console.log("dataTree", data);
        // 设置节点的disabled属性
        this.deptAndUserData = this.setNodeDisabled(data);
      });

      // this.$refs.tree && this.$refs.tree.setCheckedKeys([this.value]);
    },
    clearSelect() {
      this.selectValue = "";
      this.$refs.tree && this.$refs.tree.setCheckedKeys([]);
    },
    setCheckedKeys(ids) {
      this.$refs.tree && this.$refs.tree.setCheckedKeys(ids);
    },
    // 搜索树节点
    onSearch() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (!this.isLazy) {
          // 非懒加载

          if (this.locationDesc.length > 0) {
            this.$api[this.apiUrl]({
              desc: this.locationDesc,
            }).then(res => {
              // 设置节点的disabled属性
              this.deptAndUserData = this.setNodeDisabled(res);
              console.log("this.showCheckbox1", this.showCheckbox);
              if (this.showCheckbox) {
                console.log("this.showCheckbox2", this.showCheckbox);
                setTimeout(() => {
                  this.selectedNodes.length
                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);
                }, 500);
              }
            });
          } else {
            this.$api[this.apiUrl]({
              parentId: 0,
            }).then(res => {
              // 设置节点的disabled属性
              this.deptAndUserData = this.setNodeDisabled(res);
              console.log("this.showCheckbox1", this.showCheckbox);
              if (this.showCheckbox) {
                console.log("this.showCheckbox2", this.showCheckbox);
                setTimeout(() => {
                  this.selectedNodes.length
                    && this.$refs.tree.setCheckedNodes(this.selectedNodes);
                }, 500);
              }
            });
          }

          return;
        }
        if (this.locationDesc.length) {
          this.$api[this.apiUrl]({
            desc: this.locationDesc,
          }).then(res => {
            // 设置节点的disabled属性
            this.deptAndUserData = this.setNodeDisabled(res);
            if (this.showCheckbox) {
              setTimeout(() => {
                this.selectedNodes.length
                  && this.$refs.tree.setCheckedNodes(this.selectedNodes);
              }, 500);
            }
          });
        } else {
          this.setTimer = new Date().getTime();
          this.deptAndUserData = [];
          this.nodes.data.primaryId = 0;
          // console.log("====", this.nodes);
          this.loadNode(this.nodes, this.resolves);
        }
      }, 500);
    },
    getTreeDatas(data, list) {
      if (!this.$attrs.multiple) {
        let thisNode = this.$refs.tree.getNode(data.primaryId),
          keys = []; // 获取已勾选节点的key值
        if (thisNode && thisNode.checked) {
          keys = [data];
          // 当前节点若被选中
          for (let i = thisNode.level; i > 1; i--) {
            // 当前子节点选中，取消勾选父节点
            this.$refs.tree.setChecked(thisNode.parent, false);
            // 判断是否有父级节点
            if (!thisNode.parent.checked) {
              // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组
              thisNode = thisNode.parent;
              keys.unshift(thisNode.data);
            }
          }
        }
        let arr = [];
        keys.forEach(row => {
          console.log("row", row, row.primaryCode);

          arr.push(`${row.label} ${row.primaryCode}`);
        });
        //   console.log(keys,arr.join('/'), 'keys++++++++++++++',data.primaryId)
        this.selectedNodes = list.checkedNodes;
        this.selectValue = arr.join("→");
        const checkedNodes = this.$refs.tree.getCheckedNodes();

        if (checkedNodes.length > 0) {
          this.$emit("getSelectCnData", arr.join("→"), data);
          this.$emit("getSelectData", data.primaryId);
          this.$emit("getSelectTreeData", data);
          this.$emit("getCnValue", this.selectValue);
        } else {
          this.$emit("getSelectCnData", "", "");
          this.$emit("getSelectData", "");
          this.$emit("getSelectTreeData", "");
          this.$emit("getCnValue", "");
        }

        this.$forceUpdate();
        this.showSearchDeptTree = false;
      } else {
        let thisNode = this.$refs.tree.getNode(data.primaryId),
          keys = [data]; // 获取已勾选节点的key值
        // 有取消子节点
        if (thisNode && thisNode.checked && thisNode.childNodes.length > 0) {
          this.clearCheckedChildren(thisNode.childNodes);
        }

        if (thisNode && thisNode.checked) {
          // 当前节点若被选中
          for (let i = thisNode.level; i > 1; i--) {
            // 当前子节点选中，取消勾选父节点
            this.$refs.tree.setChecked(thisNode.parent, false);
          }
        }

        // 更新选中的节点列表
        this.checkedTreeList = this.$refs.tree.getCheckedNodes() || [];

        // 立即触发事件，传递选中的数据
        let checkedListValue = [];
        this.checkedListKey = [];

        this.checkedTreeList.forEach(item => {
          this.checkedListKey.push(item.primaryId);
          let thisNode = this.$refs.tree.getNode(item.primaryId);
          let keys = [thisNode.data];
          if (thisNode && thisNode.checked) {
            // 当前节点若被选中
            for (let i = thisNode.level; i > 1; i--) {
              // 判断是否有父级节点
              if (!thisNode.parent.checked) {
                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组
                thisNode = thisNode.parent;
                keys.unshift(thisNode.data);
              }
            }
          }
          let arr = [];
          keys.forEach(row => {
            arr.push(`${row.label} ${row.primaryCode}`);
          });
          checkedListValue.push(arr.join("→"));
        });

        this.selectValue = checkedListValue.join("||");
        this.$emit("getSelectData", this.checkedListKey);
        this.$emit("getSelectCnData", this.selectValue);
      }
      //
    },
    clearCheckedChildren(children) {
      children.length > 0
        && children.forEach(child => {
          this.$refs.tree.setChecked(child, false);
          if (child.childNodes) {
            this.clearCheckedChildren(child.childNodes);
          }
        });
    },
    submitBindPositionForm() {
      if (this.$attrs.multiple) {
        let checkedListValue = [];
        this.checkedListKey = [];
        // console.log("this.checkedTreeList", this.checkedTreeList);
        this.checkedTreeList.forEach(item => {
          this.checkedListKey.push(item.primaryId);
          let thisNode = this.$refs.tree.getNode(item.primaryId);
          let keys = [thisNode.data];
          if (thisNode && thisNode.checked) {
            // 当前节点若被选中
            for (let i = thisNode.level; i > 1; i--) {
              // 判断是否有父级节点
              if (!thisNode.parent.checked) {
                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组
                thisNode = thisNode.parent;
                keys.unshift(thisNode.data);
              }
            }
          }
          let arr = [];
          keys.forEach(row => {
            arr.push(`${row.label} ${row.primaryCode}`);
          });
          checkedListValue.push(arr.join("→"));
          this.selectValue = checkedListValue.join("||");
          this.$emit("getSelectData", this.checkedListKey);
          this.$emit("getSelectCnData", this.selectValue);
          this.showSearchDeptTree = false;
        });
      }
    },
    handleNodeClick(data) {
      if (this.showCheckbox) {
        return;
      }
      if (data.primaryId.indexOf("org") !== -1) {
        setTimeout(() => {
          this.$refs.tree.setCurrentKey(this.selectedNode);
        }, 0);
      } else {
        this.selectedNodes = [data];
        this.selectedNode = data.primaryId;
        this.$emit("treeNode", data);
      }
    },
    // 点击懒加载
    loadNode(node, resolve) {
      if (node.level === 0) {
        this.showSearchTreeEvent();
        return;
      }
      this.nodes = node;
      this.resolves = resolve;
      this.$api[this.apiUrl]({
        parentId: node.data.primaryId,
      }).then(res => {
        // 设置节点的disabled属性
        const processedData = this.setNodeDisabled(res);
        resolve(processedData);
        if (this.showCheckbox) {
          setTimeout(() => {
            this.$refs.tree.setCheckedKeys([this.value]);
          }, 100);
        } else {
          setTimeout(() => {
            this.$refs.tree.setCurrentKey(this.selectedNode);
          }, 0);
        }
      });
    },
  },
};
</script>

<style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}

.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>
