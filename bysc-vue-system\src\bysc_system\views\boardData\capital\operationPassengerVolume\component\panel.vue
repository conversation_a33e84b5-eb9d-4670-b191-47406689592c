<template>
  <el-drawer
    :title="'编辑'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <el-form :model="editForm" ref="editForm" :rules="rules">

        <el-form-item label="行驶里程" prop="mileage">
          <el-input
            v-model="editForm.mileage"
            placeholder="请输入行驶里程">
            <template slot="append">KM</template>
          </el-input>
        </el-form-item>


        <el-form-item label="运送旅客数量" prop="transportPassengerCount">
          <el-input
            v-model="editForm.transportPassengerCount"
            placeholder="请输入旅客数量">
            <template slot="append">人/次</template>
          </el-input>
        </el-form-item>

        <el-form-item label="日期" prop="transportDate">
          <el-date-picker
            v-model="editForm.transportDate"
            type="date"
            disabled
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EditPanel',
  data() {
    const validateNumber = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入数值'));
      } else if (isNaN(Number(value)) || Number(value) < 0) {
        callback(new Error('请输入大于0的数值'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      editForm: {
        transportDate: null,
        transportPassengerCount: '',
        mileage: ''
      },
      rules: {
        transportPassengerCount: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ],
        mileage: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ]
      }
    };
  },
  methods: {
    open(data) {
      this.visible = true;
      this.editForm = _.cloneDeep(data);
    },
    handleCancel() {
      this.visible = false;
      this.$refs.editForm.resetFields();
    },
    handleSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const formData = {
            ...this.editForm,
            transportPassengerCount: Number(this.editForm.transportPassengerCount)
          };

          this.$api['boardData/dashboardTransportPassenger-save'](formData).then(() => {
            this.$message.success('保存成功');
            this.$emit('submit', formData);
            this.visible = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}
</style>
