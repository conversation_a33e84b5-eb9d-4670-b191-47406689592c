<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="commonModule/jdUser-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="京东慧采用户名称">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.username"
                  size="small"
                  maxlength="32"
                  placeholder="请输入京东慧采用户名称"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'jdUser_add'" size="small" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'jdUser_bind'"
                  @click="handleBindUser(scope.row)"
                  type="text"
                  size="small"
                  >绑定人力用户</el-button
                >
                <el-button
                  v-permission="'jdUser_edit'"
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  v-permission="'jdUser_del'"
                  @click="handleDelete(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px;margin-bottom:100px;">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="110px"
          class="demo-form"
        >
          <div style="margin:0 0 10px 0;">
            <el-row>
              <el-col :span="24">
                <el-form-item label="京东慧采用户名称" prop="username" label-width="150px">
                  <el-input size="small" v-model.trim="form.username" placeholder="请输入京东慧采用户名称" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            :loading="okLoading"
            @click="submitForm"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <el-drawer
      size="50%"
      :title="'绑定人力用户'"
      :visible.sync="bindUserDrawer"
      :direction="direction"
    >
      <div style="padding:0 10px;margin-bottom:100px;">
        <div>
          <el-button size="small" type="primary" @click="handleSelectUser"
            >选择用户</el-button
          >
        </div>
        <el-table :data="bindUserTableData" ref="bindUserTable" stripe style="width: 100%">
          <el-table-column fixed="left" type="selection" width="55">
          </el-table-column>
          <el-table-column fixed="left" label="序号" type="index" width="50">
          </el-table-column>
          <template v-for="(item, index) in bindUserColumns">
            <el-table-column
              :show-overflow-tooltip="true"
              :key="item.key"
              :prop="item.key"
              :label="item.title"
              :min-width="item.minWidth ? item.minWidth : '150'"
              :align="item.align ? item.align : 'center'"
            >
            </el-table-column>
          </template>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            type="action"
            width="90"
          >
            <template slot-scope="scope">
              <el-button
                @click="handleDeleteBindUser(scope.row)"
                type="text"
                size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="width: 100%; padding: 0 10px">
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeBindUserDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            :loading="bindUserOkLoading"
            @click="submitBindUserForm"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
    <el-drawer
      size="50%"
      :title="'选择用户'"
      :visible.sync="selectUserDrawer"
      :direction="direction"
    >
      <div style="padding:0 10px;margin-bottom:100px;">
        <Grid
          api="commonModule/jdUserBind-not-bind-user-page"
          :event-bus="searchSelectUserEventBus"
          :search-params="searchSelectUserForm"
          :newcolumn="selectUserColumns"
          @datas="getSelectUserDatas"
          @columnChange="getSelectUserColumn"
          :show-search="true"
          :auto-load="false"
          ref="selectUserGrid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchSelectUserForm"
              class="demo-form-inline"
            >
              <el-form-item label="姓名">
                <el-input
                  style="width: 220px; margin: 0 10px 0 0"
                  v-model.trim="searchSelectUserForm.realName"
                  size="small"
                  maxlength="32"
                  placeholder="请输入姓名"
                ></el-input>
              </el-form-item>
              <el-form-item label="组织">
                <selectFilterTree
                  :value="searchSelectUserForm.organizationId"
                  ref="depttrees"
                  :defaultData="deptTreeList"
                  :treeProps="deptprops"
                  @getSelectData="getSearchDeptData"
                  treeplaceholder="请选择组织"
                  :treeKey="'id'">
                </selectFilterTree>

              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchSelectUserTable"
                  >查询</el-button
                >
                <el-button size="small" @click="resetSelectUserTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="selectUserTableData" ref="selectUserTable" :row-key="getUserRowKeys" @selection-change="handleUserChange" stripe style="width: 100%">
            <el-table-column fixed="left" type="selection" :reserve-selection="true" width="55">
            </el-table-column>
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in selectUserColumns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
          </el-table>
        </Grid>
      </div>
      <div style="width: 100%; padding: 0 10px">
        <div class="demo-drawer-footer">
          <el-button
            size="small"
            type="primary"
            @click="submitSelectUserForm"
            >确定已选</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import selectFilterTree from "@/components/treeComp/selectFilterTree.vue";
const defaultSearchForm = {};
const defaultForm = {};
const defaultSearchSelectUserForm = {};
export default {
  components: {
    Grid,
    selectFilterTree
  },
  destroyed() {
    this.searchEventBus.$off();
    this.searchSelectUserEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    this.searchSelectUserEventBus = new Vue();
    return {
      deptprops: {
        children: "children",
        label: "organizationName",
      },
      deptTreeList: [],

      selectionUser: [],
      searchSelectUserForm: _.cloneDeep(defaultSearchSelectUserForm),
      selectUserColumns: [
        {
          title: '账户名',
          key: 'account',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '姓名',
          key: 'realName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '手机号',
          key: 'mobile',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '组织',
          key: 'organizationName',
          minWidth: 150,
          tooltip: true,
        },
      ],
      selectUserDrawer: false,
      selectUserTableData: [],
      selectUserOkLoading: false,

      bindUserColumns: [
        {
          title: '账户名',
          key: 'account',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '姓名',
          key: 'realName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '手机号',
          key: 'mobile',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '组织',
          key: 'organizationName',
          minWidth: 150,
          tooltip: true,
        },
      ],
      bindUserName: null,
      bindUserDrawer: false,
      bindUserTableData: [],
      bindUserOkLoading: false,

      form: _.cloneDeep(defaultForm),
      okLoading: false,
      rules: {
        username: [
          {required: true, message: '请输入京东慧采用户名称', trigger: 'blur'},
        ],
      },
      drawerTitle: '新增',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '京东慧采用户名称',
          key: 'username',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    this.getDeptData();
  },
  methods: {
    handleAdd() {
      this.form = {};
      this.drawerTitle = '新增';
      this.drawer = true;
    },
    handleEdit(row) {
      this.$api['commonModule/jdUser-get']({id: row.id}).then(data => {
        this.form = data;
        this.drawerTitle = '编辑';
        this.drawer = true;
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['commonModule/jdUser-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    submitForm() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.$api['commonModule/jdUser-save'](this.form).then(data => {
            this.drawer = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.form = _.cloneDeep(defaultForm);
      this.$refs.form.resetFields();
      this.drawer = false;
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },

    getDeptData() {
      this.$api["systems/organizationTree"]({parentId: 0}).then(data => {
        this.deptTreeList = data;
      });
    },
    getSearchDeptData(e) {
      this.searchSelectUserForm.organizationId = e;
    },

    handleBindUser(e) {
      this.bindUserName = e.username;
      this.$api['commonModule/jdUserBind-bind-user-page']({
        ssoUsername: this.bindUserName
      }).then(data => {
        if (data && data.length > 0) {
          this.bindUserTableData = data;
        } else {
          this.bindUserTableData = [];
        }
        this.bindUserDrawer = true;
      });
    },
    closeBindUserDrawer() {
      this.bindUserName = null;
      this.bindUserDrawer = false;
    },
    submitBindUserForm() {
      let that = this;
      // this.$refs.form.validate(valid => {
      //   if (valid) {
      this.bindUserOkLoading = true;
      let sysUserIds = [];
      if (this.bindUserTableData.length > 0) {
        let ids = this.bindUserTableData.reduce((result, row) => {
          result.push(row.id);
          return result;
        }, []);
        sysUserIds = ids;
      }
      this.$api['commonModule/jdUserBind-bind']({
        username: this.bindUserName,
        sysUserIds: sysUserIds
      }).then(data => {
        this.bindUserDrawer = false;
        this.bindUserOkLoading = false;
        this.$message({
          type: 'success',
          message: '保存成功',
        });
      }).catch(() => {
        that.bindUserOkLoading = false;
      });
    },
    handleDeleteBindUser(e) {
      this.bindUserTableData = this.bindUserTableData.filter(item => {
        return item.id !== e.id;
      });
    },
    getUserRowKeys(row) {
      return row.id;
    },
    handleUserChange(selection) {
      // console.log(selection);
      this.selectionUser = selection;
    },

    searchSelectUserTable() {
      this.$refs.selectUserGrid.queryData();
    },
    resetSelectUserTable() {
      this.$nextTick(() => {
        this.$refs.depttrees.clearSelect();
      });
      this.searchSelectUserForm.organizationId = '';
      this.searchSelectUserForm = _.cloneDeep(defaultSearchSelectUserForm);
      let excludeIds = [];
      if (this.bindUserTableData && this.bindUserTableData.length > 0) {
        excludeIds = this.bindUserTableData.reduce((result, row) => {
          result.push(row.id);
          return result;
        }, []);
        this.searchSelectUserForm.excludeIds = excludeIds;
      } else {
        this.searchSelectUserForm.excludeIds = excludeIds;
      }
      this.$nextTick(() => {
        this.$refs.selectUserGrid.query();
      });
    },
    handleSelectUser() {
      this.selectionUser = [];
      this.resetSelectUserTable();
      this.selectUserDrawer = true;
    },
    getSelectUserColumn(e) {
      this.selectUserColumns = e;
      setTimeout(() => {
        this.$refs.selectUserTable.doLayout();
      }, 100);
    },
    getSelectUserDatas(e) {
      this.selectUserTableData = e;
    },
    submitSelectUserForm() {
      this.bindUserTableData = this.selectionUser.concat(this.bindUserTableData);
      this.$refs.selectUserTable.clearSelection();
      this.selectUserDrawer = false;
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
