<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="log/login-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="登录人">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.username"
                  size="small"
                  maxlength="32"
                  placeholder="请输入操作人"
                ></el-input>
              </el-form-item>
              <el-form-item label="登录类型">
                <el-select v-model="searchForm.operationType"   size="small" placeholder="请选择">
                <el-option
                  v-for="item in operationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="客户端">
                <el-select v-model="searchForm.userClient"   size="small" placeholder="请选择">
                <el-option
                  v-for="item in userClientTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
                </el-select>
              </el-form-item>

              <!-- <el-form-item label="客户端">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.userClient"
                  size="small"
                  maxlength="32"
                  placeholder="请输入客户端"
                ></el-input>
              </el-form-item> -->

              <el-form-item label="操作时间">
                <el-date-picker
                  size="small"
                  v-model="operationTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  align="right"
                  @change="getOperationTime">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- <div slot="action">
            <el-button size="small" type="primary" @click="handleAdd"
              >新增</el-button
            >
            <el-button size="small" type="primary" @click="handleSyncData"
              >同步</el-button
            >
          </div> -->
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot == 'operationType'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <div>{{ scope.row.operationType === 'login' ? "登入":"登出" }}</div>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>

          </el-table>
        </Grid>
      </el-col>
    </el-row>



  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultForm = {
  renlibumen: '',
  yuangongxingming: '',
  caiwuzuzhi: '',
  caiwubumen: '',
};
const defaultSyncDataForm = {
  month: ''
};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      operationTypeOptions: [
        {
          value: 'login',
          label: '登入'
        },
        {
          value: 'logout',
          label: '登出'
        }
      ],
      userClientTypeOptions: [
        {
          value: 'PC',
          label: 'PC'
        },
        {
          value: '飞书小程序',
          label: '飞书小程序'
        }
      ],


      operationTime: null,

      monthList: [],
      syncDataFormRules: {
        month: [
          {required: true, message: '请选择月份', trigger: 'blur,change'},
        ],
      },
      syncDataForm: _.cloneDeep(defaultSyncDataForm),
      syncDataDialog: false,

      renlibumenList: [],
      yuangongxingming: [],
      yuangongxingmingList: [],
      caiwuzuzhiList: [],
      caiwubumenList: [],
      form: _.cloneDeep(defaultForm),
      okLoading: false,

      drawerTitle: '新增',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '登录人',
          key: 'username',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '部门名称',
          key: 'deptName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '登录类型',
          slot: 'operationType',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '客户端',
          key: 'userClient',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: 'IP',
          key: 'loginIp',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '操作时间',
          key: 'operationTime',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    // this.getYuangongxingmingList();
  },
  methods: {

    handleSyncData() {
      this.syncDataForm = _.cloneDeep(defaultSyncDataForm);
      this.syncDataDialog = true;
    },






    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.operationTime = null;
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
    getTimes(date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? ('0' + m) : m;
      var d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      var h = date.getHours();
      h = h < 10 ? ('0' + h) : h;
      var min = date.getMinutes();
      min = min < 10 ? ('0' + min) : min;
      var s = date.getSeconds();
      s = s < 10 ? ('0' + s) : s;
      return y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + s;
    },
    getOperationTime(e) {
      if (e) {
        this.searchForm.beginDateTime = this.getTimes(e[0]);
        this.searchForm.endDateTime = this.getTimes(e[1]);
      } else {
        this.searchForm.beginDateTime = null;
        this.searchForm.endDateTime = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
