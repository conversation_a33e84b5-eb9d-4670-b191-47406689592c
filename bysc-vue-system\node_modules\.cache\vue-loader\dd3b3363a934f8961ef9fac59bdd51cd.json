{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue", "mtime": 1754276220628}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\nimport Vue from 'vue';\r\nimport _ from 'lodash';\r\nimport Grid from \"@/components/Grid\";\r\nimport Panel from './component/panel.vue';\r\n\r\nconst defaultSearchForm = {\r\n};\r\n\r\nexport default {\r\n  name: 'CapitalSafetyTable',\r\n  components: {\r\n    Grid,\r\n    Panel\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      tableData: [],\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: \"日期\",\r\n          key: \"designDate\",\r\n          width: \"150\",\r\n        },\r\n        {\r\n          title: \"总分值\",\r\n          key: \"totalScore\",\r\n          width: \"150\",\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑操作\r\n    handleEdit(row) {\r\n      console.log(row, '---');\r\n\r\n      this.$refs.panel.open(row, 'edit');\r\n    },\r\n\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    // 重置表格\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    // 获取列配置\r\n    getcolumn(e) {\r\n      this.columns = e;\r\n    },\r\n\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    }\r\n  }\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8EA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/techDashboard/techData", "sourcesContent": ["<template>\r\n  <div class=\"table-container\">\r\n    <Grid\r\n      api=\"techDashboard/techManagementRatingDesign-page\"\r\n      :event-bus=\"searchEventBus\"\r\n      :search-params=\"searchForm\"\r\n      :newcolumn=\"columns\"\r\n      @datas=\"getDatas\"\r\n      @columnChange=\"getcolumn\"\r\n      ref=\"grid\"\r\n    >\r\n      <div slot=\"search\">\r\n        <!-- <el-date-picker\r\n          style=\"width: 200px; margin: 0 10px 0 0\"\r\n          v-model=\"searchForm.date\"\r\n          type=\"month\"\r\n          value-format=\"yyyy-MM\"\r\n          placeholder=\"选择月份\"\r\n        ></el-date-picker>\r\n        <el-button\r\n          size=\"small\"\r\n          type=\"primary\"\r\n          style=\"margin: 0 0 0 10px\"\r\n          @click=\"searchTable\"\r\n        >搜索</el-button>\r\n        <el-button size=\"small\" @click=\"resetTable\">重置</el-button> -->\r\n      </div>\r\n\r\n      <el-table\r\n        slot=\"table\"\r\n        slot-scope=\"{ loading }\"\r\n        v-loading=\"loading\"\r\n        :data=\"tableData\"\r\n        stripe\r\n        style=\"width: 100%\"\r\n        :border=\"true\"\r\n      >\r\n        <el-table-column\r\n          fixed=\"left\"\r\n          :align=\"'center'\"\r\n          label=\"序号\"\r\n          type=\"index\"\r\n          width=\"50\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-for=\"item in columns\"\r\n          :key=\"item.key\"\r\n          :show-overflow-tooltip=\"true\"\r\n          :prop=\"item.key\"\r\n          :label=\"item.title\"\r\n          :min-width=\"item.width ? item.width : '150'\"\r\n          :align=\"item.align ? item.align : 'center'\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          fixed=\"right\"\r\n          align=\"center\"\r\n          label=\"操作\"\r\n          width=\"100\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"small\"\r\n              v-permission=\"'techEditBtn'\"\r\n              @click=\"handleEdit(scope.row)\"\r\n            >编辑</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </Grid>\r\n\r\n    <Panel ref=\"panel\" @refresh=\"searchTable\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport _ from 'lodash';\r\nimport Grid from \"@/components/Grid\";\r\nimport Panel from './component/panel.vue';\r\n\r\nconst defaultSearchForm = {\r\n};\r\n\r\nexport default {\r\n  name: 'CapitalSafetyTable',\r\n  components: {\r\n    Grid,\r\n    Panel\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      tableData: [],\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: \"日期\",\r\n          key: \"designDate\",\r\n          width: \"150\",\r\n        },\r\n        {\r\n          title: \"总分值\",\r\n          key: \"totalScore\",\r\n          width: \"150\",\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑操作\r\n    handleEdit(row) {\r\n      console.log(row, '---');\r\n\r\n      this.$refs.panel.open(row, 'edit');\r\n    },\r\n\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    // 重置表格\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    // 获取列配置\r\n    getcolumn(e) {\r\n      this.columns = e;\r\n    },\r\n\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-container {\r\n  padding: 20px;\r\n  width: 100%;\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n\r\n  .el-table {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>"]}]}