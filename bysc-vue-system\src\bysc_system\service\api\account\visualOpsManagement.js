export default [
  {
    name: 'visualOpsManagement-page', // 图纸分页
    method: 'POST',
    path: '/syn/maintenanceDrawingManagement/page'
  },
  {
    name: 'visualOpsManagement-save', // 编图纸保存
    method: 'POST',
    path: '/syn/maintenanceDrawingManagement/save'
  },
  {
    name: 'visualOpsManagement-get', // 获取图纸
    method: 'OTHERGET',
    path: '/syn/maintenanceDrawingManagement/get'
  },
  {
    name: 'visualOpsManagement-delete', // 图纸删除
    method: 'POSt',
    path: '/syn/maintenanceDrawingManagement/delete'
  },

  {
    name: 'workorderFirstLineDept-findAll', // 查询一线部门列表
    method: 'POST',
    path: '/workorder/workorderFirstLineDept/findAll'
  },
  //  区域管理分页
  {
    name: 'maintenanceRegion-page', // 区域管理分页
    method: 'POST',
    path: '/syn/maintenanceRegion/page'
  },

  {
    name: 'maintenanceRegionLocationRel-get', // 根据区域ID查询已绑定的位置ID
    method: 'OTHERGET',
    path: '/syn/maintenanceRegionLocationRel/getByreginId'
  },
  {
    name: 'assetLocation-page', // 资产位置关系表分页
    method: 'POST',
    path: '/syn/maintenanceRegionLocationRel/page'
  },
  {
    name: 'assetLocation-save', // 保存可视化区域
    method: 'POST',
    path: '/syn/maintenanceRegionLocationRel/save'
  },
  {
    name: 'assetLocation-delete', // 取消绑定
    method: 'OTHER',
    path: '/syn/maintenanceRegionLocationRel/delete'
  },
  {
    name: 'assetDevice-page', // 查询设备资产卡片分页
    method: 'POST',
    path: '/device/deviceAssetCard/pageByRegionId'
  },



];

