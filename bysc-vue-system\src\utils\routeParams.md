# 路由参数持久化工具使用说明

## 问题描述

在 Vue.js 应用中，当使用 `this.$router.push` 进行页面跳转并传递参数时，如果用户在目标页面刷新浏览器，传递的参数会丢失。这是因为通过 `push` 方法传递的参数只存在于内存中。

## 解决方案

本工具提供了多种方法来解决这个问题：

### 1. 基础工具函数

#### `saveRouteParams(router, route, params, storageKey)`
保存路由参数到 localStorage 和 URL 查询参数中。

#### `getRouteParams(route, paramNames, storageKey)`
获取路由参数，优先从 URL 获取，其次从 localStorage 获取。

#### `navigateWithParams(router, routeName, params, options)`
导航到指定页面并自动保存参数。

### 2. Vue Mixin

`RouteParamsMixin` 为组件提供便捷的参数管理方法。

## 使用示例

### 方式一：使用工具函数（推荐）

#### 发送页面（如：图纸管理页面）

```javascript
// 在跳转方法中使用 navigateWithParams
methods: {
  handleView(row) {
    const { navigateWithParams } = require('@/utils/routeParams');
    navigateWithParams(this.$router, 'areaManagement', {
      drawingId: row.id,
      drawingName: row.name || row.drawingName
    });
  }
}
```

#### 接收页面（如：区域管理页面）

```javascript
// 在 created 钩子中获取参数
created() {
  const { getRouteParams, saveRouteParams } = require('@/utils/routeParams');
  
  // 获取参数
  const params = getRouteParams(this.$route, ['drawingId', 'drawingName'], 'areaManagement_params');
  
  if (params.drawingId) {
    this.currentDrawingId = params.drawingId;
    
    // 确保参数在 URL 中也存在（防止直接访问时丢失）
    saveRouteParams(this.$router, this.$route, {
      drawingId: params.drawingId,
      drawingName: params.drawingName
    }, 'areaManagement_params');
  }
}
```

### 方式二：使用 Mixin

```javascript
import { RouteParamsMixin } from '@/utils/routeParams';

export default {
  mixins: [RouteParamsMixin],
  created() {
    // 获取当前页面的参数
    const params = this.getCurrentRouteParams(['drawingId', 'drawingName']);
    
    if (params.drawingId) {
      this.currentDrawingId = params.drawingId;
      
      // 保存参数
      this.saveCurrentRouteParams({
        drawingId: params.drawingId,
        drawingName: params.drawingName
      });
    }
  }
}
```

### 方式三：传统方式改进

如果不想大幅修改现有代码，可以在现有基础上添加 localStorage 支持：

```javascript
// 发送页面
methods: {
  handleView(row) {
    // 保存到 localStorage
    localStorage.setItem('areaManagement_params', JSON.stringify({
      drawingId: row.id,
      drawingName: row.name || row.drawingName
    }));
    
    // 正常跳转
    this.$router.push({
      name: 'areaManagement',
      query: {
        drawingId: row.id,
        drawingName: row.name || row.drawingName
      }
    });
  }
}

// 接收页面
created() {
  // 优先从 URL 获取，其次从 localStorage 获取
  let drawingId = this.$route.query.drawingId;
  let drawingName = this.$route.query.drawingName;
  
  if (!drawingId) {
    const stored = localStorage.getItem('areaManagement_params');
    if (stored) {
      const params = JSON.parse(stored);
      drawingId = params.drawingId;
      drawingName = params.drawingName;
      
      // 更新 URL
      this.$router.replace({
        query: { ...this.$route.query, drawingId, drawingName }
      });
    }
  }
  
  if (drawingId) {
    this.currentDrawingId = drawingId;
  }
}
```

## 最佳实践

1. **使用唯一的 storageKey**：为每个页面使用不同的存储键，避免参数冲突。

2. **及时清理**：在不需要参数时及时清理，避免 localStorage 积累过多数据。

3. **错误处理**：添加适当的错误处理和用户提示。

4. **参数验证**：在使用参数前进行验证，确保数据的有效性。

## 注意事项

- localStorage 有存储限制（通常 5-10MB）
- 敏感信息不应存储在 localStorage 中
- 考虑使用 sessionStorage 替代 localStorage（页面关闭后自动清理）
- 在生产环境中考虑参数加密

## 清理参数

```javascript
// 清理特定参数
clearRouteParams(['drawingId'], 'areaManagement_params');

// 清理所有参数
clearRouteParams(null, 'areaManagement_params');

// 使用 Mixin 清理
this.clearCurrentRouteParams(['drawingId']);
```
