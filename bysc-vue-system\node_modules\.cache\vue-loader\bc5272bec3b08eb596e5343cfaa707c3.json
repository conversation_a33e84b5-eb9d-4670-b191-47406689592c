{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue?vue&type=style&index=0&id=2291e59b&lang=less&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue", "mtime": 1754276220640}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1752725539056}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752725560473}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752725548209}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752725545250}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n  text-align: right;\r\n  z-index: 1;\r\n}\r\n\r\n.el-form {\r\n  padding-bottom: 80px; // 为底部按钮留出空间\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/deep/ .el-upload--picture-card {\r\n  width: 100px;\r\n  height: 100px;\r\n  line-height: 100px;\r\n}\r\n\r\n/deep/ .el-upload-list--picture-card .el-upload-list__item {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n\r\n/deep/ .el-drawer__body {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n", {"version": 3, "sources": ["DrawingFormDialog.vue"], "names": [], "mappings": ";AAgfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "DrawingFormDialog.vue", "sourceRoot": "src/bysc_system/views/visualOpsManagement/drawingManagement/components", "sourcesContent": ["<template>\r\n  <el-drawer\r\n    :title=\"dialogTitle\"\r\n    :visible.sync=\"visible\"\r\n    direction=\"rtl\"\r\n    size=\"600px\"\r\n    :close-on-press-escape=\"false\"\r\n    :wrapperClosable=\"false\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"formData\"\r\n      :rules=\"rules\"\r\n      label-width=\"100px\"\r\n      label-position=\"left\"\r\n    >\r\n      <el-form-item label=\"图纸名称\" prop=\"drawingName\">\r\n        <el-input\r\n          v-model.trim=\"formData.drawingName\"\r\n          placeholder=\"请输入图纸名称\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n           size=\"small\"\r\n          clearable\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所属部门\" prop=\"belongDeptKey\">\r\n        <el-select\r\n          style=\"width: 100%\"\r\n          v-model=\"formData.belongDeptKey\"\r\n          placeholder=\"请选择\"\r\n          size=\"small\"\r\n          filterable\r\n          clearable\r\n        >\r\n          <el-option\r\n            :label=\"i.name\"\r\n            :value=\"i.id\"\r\n            v-for=\"i in deptList\"\r\n            :key=\"i.id\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"drawingInfo\" required>\r\n        <span slot=\"label\"> 上传图片 </span>\r\n        <el-upload\r\n          ref=\"upload\"\r\n          action=\"#\"\r\n           size=\"small\"\r\n          :file-list=\"fileList\"\r\n          :on-change=\"handleFileChange\"\r\n          :before-remove=\"handleBeforeRemove\"\r\n          :on-remove=\"handleRemove\"\r\n          :on-preview=\"handlePreview\"\r\n          :on-exceed=\"handleExceed\"\r\n          :auto-upload=\"false\"\r\n          accept=\".png,.jpg,.jpeg\"\r\n          list-type=\"picture-card\"\r\n          :limit=\"1\"\r\n        >\r\n          <i class=\"el-icon-plus\"></i>\r\n          <div slot=\"tip\" class=\"el-upload__tip\">\r\n            支持格式：PNG、JPG、JPEG格式<br />\r\n            文件大小：图片各尺寸不超过10MB<br />\r\n            尺寸要求：分辨率控制在2000×2000像素内<br />\r\n            上传数量：仅一张\r\n          </div>\r\n        </el-upload>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"drawer-footer\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSave\" :loading=\"saveLoading\"\r\n        >保存</el-button\r\n      >\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DrawingFormDialog\",\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    mode: {\r\n      type: String,\r\n      default: \"add\", // add: 新增, edit: 编辑\r\n      validator: value => [\"add\", \"edit\"].includes(value),\r\n    },\r\n    editData: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  created() {\r\n    this.getInit();\r\n  },\r\n  data() {\r\n    return {\r\n      deptList: [],\r\n      formData: {\r\n        drawingName: \"\",\r\n        belongDeptKey: \"\",\r\n        drawingInfo: \"\", // 存储base64编码的图片\r\n      },\r\n      fileList: [],\r\n      saveLoading: false,\r\n      rules: {\r\n        drawingName: [\r\n          {required: true, message: \"请输入图纸名称\", trigger: \"blur\"},\r\n          {\r\n            min: 1,\r\n            max: 50,\r\n            message: \"长度在 1 到 50 个字符\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        belongDeptKey: [\r\n          {required: true, message: \"请选择所属部门\", trigger: \"change\"},\r\n        ],\r\n\r\n        drawingInfo: [\r\n          {\r\n            validator: (_rule, _value, callback) => {\r\n              // 检查是否有图片数据（新上传的或编辑时已有的）\r\n              if (this.formData.drawingInfo && this.formData.drawingInfo.trim() !== \"\") {\r\n                callback();\r\n              } else {\r\n                callback(new Error(\"请上传图片\"));\r\n              }\r\n            },\r\n            trigger: [\"change\", \"blur\"],\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    dialogTitle() {\r\n      return this.mode === \"add\" ? \"新增图纸\" : \"编辑图纸\";\r\n    },\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      if (val) {\r\n        this.initFormData();\r\n      }\r\n    },\r\n    editData: {\r\n      handler(val) {\r\n        if (val && Object.keys(val).length > 0) {\r\n          this.initFormData();\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true,\r\n    },\r\n  },\r\n  methods: {\r\n    getInit() {\r\n      this.$api[\"visualOpsManagement/workorderFirstLineDept-findAll\"]({}).then(\r\n        res => {\r\n          this.deptList = res;\r\n        }\r\n      );\r\n    },\r\n    // 处理部门变化\r\n    handleDeptChange(value) {\r\n      this.formData.belongDeptKey = value;\r\n    },\r\n\r\n    // 初始化表单数据\r\n    initFormData() {\r\n      if (this.mode === \"edit\" && this.editData) {\r\n        this.formData = {\r\n          drawingName: this.editData.drawingName || \"\",\r\n          belongDeptKey: this.editData.belongDeptKey || \"\",\r\n          drawingInfo: this.editData.drawingInfo || \"\",\r\n        };\r\n        // 如果有已上传的图片，可以在这里处理\r\n        if (this.editData.drawingInfo) {\r\n          this.fileList = [\r\n            {\r\n              name: this.editData.imageName || \"已上传图片\",\r\n              url: this.editData.drawingInfo,\r\n              uid: Date.now(),\r\n            },\r\n          ];\r\n        } else {\r\n          this.fileList = [];\r\n        }\r\n      } else {\r\n        this.formData = {\r\n          drawingName: \"\",\r\n          belongDeptKey: \"\",\r\n          drawingInfo: \"\",\r\n        };\r\n        this.fileList = [];\r\n      }\r\n\r\n      // 清除表单验证，在编辑模式下如果有图片则重新验证\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.clearValidate();\r\n          // 在编辑模式下，如果有图片数据，触发一次验证以清除可能的错误提示\r\n          if (this.mode === \"edit\" && this.formData.drawingInfo) {\r\n            this.$refs.form.validateField(\"drawingInfo\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 上传前验证\r\n    beforeUpload(file) {\r\n      console.log(\"beforeUpload 被调用:\", file);\r\n\r\n      // 检查是否已经有文件\r\n      if (this.fileList.length >= 1) {\r\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\r\n        return false;\r\n      }\r\n\r\n      const isImage = /^image\\/(png|jpe?g)$/i.test(file.type);\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n\r\n      console.log(\"文件类型检查:\", isImage, file.type);\r\n      console.log(\"文件大小检查:\", isLt10M, file.size);\r\n\r\n      if (!isImage) {\r\n        this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\r\n        return false;\r\n      }\r\n      if (!isLt10M) {\r\n        this.$message.error(\"上传图片大小不能超过 10MB!\");\r\n        return false;\r\n      }\r\n\r\n      console.log(\"开始转换为base64\");\r\n      // 将文件转换为base64\r\n      this.convertToBase64(file);\r\n\r\n      // 阻止默认上传行为，因为我们使用base64方式\r\n      return false;\r\n    },\r\n\r\n    // 将文件转换为base64\r\n    convertToBase64(file) {\r\n      const reader = new FileReader();\r\n      reader.onload = e => {\r\n        this.formData.drawingInfo = e.target.result;\r\n\r\n        // 手动添加到文件列表用于显示\r\n        this.fileList = [\r\n          {\r\n            name: file.name,\r\n            url: e.target.result,\r\n            uid: file.uid || Date.now(),\r\n          },\r\n        ];\r\n\r\n        // 触发表单验证，清除drawingInfo字段的错误提示\r\n        this.$nextTick(() => {\r\n          if (this.$refs.form) {\r\n            this.$refs.form.validateField(\"drawingInfo\");\r\n          }\r\n        });\r\n\r\n        this.$message.success(\"图片上传成功\");\r\n      };\r\n      reader.onerror = error => {\r\n        console.error(\"FileReader 错误:\", error);\r\n        this.$message.error(\"图片读取失败\");\r\n      };\r\n      reader.readAsDataURL(file);\r\n    },\r\n\r\n    // 预览图片（Element UI 上传组件的预览回调）\r\n    handlePreview(file) {\r\n      // 使用文件的 url 或者 formData 中的 drawingInfo进行预览\r\n      const imageUrl = file.url || this.formData.drawingInfo;\r\n      if (imageUrl) {\r\n        // 创建一个新窗口来显示图片\r\n        const newWindow = window.open(\"\", \"_blank\");\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head><title>图片预览 - ${file.name || \"图片\"}</title></head>\r\n            <body style=\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\">\r\n              <div style=\"margin-bottom:10px;font-size:16px;color:#333;\">\r\n                ${file.name || \"图片预览\"}\r\n              </div>\r\n              <img src=\"${imageUrl}\" style=\"max-width:100%;max-height:90vh;object-fit:contain;border:1px solid #ddd;border-radius:4px;\" alt=\"预览图片\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        this.$message.warning(\"无法预览图片\");\r\n      }\r\n    },\r\n\r\n    // 预览图片（通用方法，保留兼容性）\r\n    previewImage() {\r\n      if (this.formData.drawingInfo) {\r\n        // 创建一个新窗口来显示图片\r\n        const newWindow = window.open(\"\", \"_blank\");\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head><title>图片预览</title></head>\r\n            <body style=\"margin:0;padding:20px;text-align:center;background:#f5f5f5;\">\r\n              <img src=\"${this.formData.drawingInfo}\" style=\"max-width:100%;max-height:100vh;object-fit:contain;\" alt=\"预览图片\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      }\r\n    },\r\n\r\n    // 替换图片\r\n    replaceImage() {\r\n      this.$refs.hiddenFileInput.click();\r\n    },\r\n\r\n    // 删除图片\r\n    removeImage() {\r\n      this.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.formData.drawingInfo = \"\";\r\n          this.currentImageName = \"\";\r\n          this.currentImageSize = 0;\r\n          this.fileList = [];\r\n          this.$message.success(\"图片已删除\");\r\n        })\r\n        .catch(() => {\r\n          // 用户取消删除\r\n        });\r\n    },\r\n\r\n    // 处理文件选择变化（Element UI 的 on-change 事件）\r\n    handleFileChange(file, fileList) {\r\n      // 检查是否已经有文件\r\n      if (fileList.length > 1) {\r\n        this.$message.error(\"只能上传一张图片，请先删除已有图片!\");\r\n        // 移除新添加的文件，保留第一个\r\n        this.fileList = [fileList[0]];\r\n        return;\r\n      }\r\n\r\n      if (file && file.raw) {\r\n        const rawFile = file.raw;\r\n\r\n        // 验证文件\r\n        const isImage = /^image\\/(png|jpe?g)$/i.test(rawFile.type);\r\n        const isLt10M = rawFile.size / 1024 / 1024 < 10;\r\n\r\n        if (!isImage) {\r\n          this.$message.error(\"上传图片只能是 PNG、JPG、JPEG 格式!\");\r\n          this.fileList = [];\r\n          return;\r\n        }\r\n        if (!isLt10M) {\r\n          this.$message.error(\"上传图片大小不能超过 10MB!\");\r\n          this.fileList = [];\r\n          return;\r\n        }\r\n\r\n        // 转换为base64\r\n        this.convertToBase64(rawFile);\r\n      }\r\n    },\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) {\r\n        return \"0 B\";\r\n      }\r\n      const k = 1024;\r\n      const sizes = [\"B\", \"KB\", \"MB\", \"GB\"];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\r\n    },\r\n\r\n    // 删除前确认（Element UI 的 before-remove 钩子）\r\n    handleBeforeRemove() {\r\n      return new Promise((resolve, reject) => {\r\n        this.$confirm(\"确定要删除当前图片吗？\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            // 用户确认删除，允许删除操作\r\n            resolve(); // 允许删除\r\n          })\r\n          .catch(() => {\r\n            // 用户取消删除\r\n            reject(); // 阻止删除\r\n          });\r\n      });\r\n    },\r\n\r\n    // 移除文件（Element UI 删除完成后的回调）\r\n    handleRemove() {\r\n      // 文件删除完成后，清空相关数据\r\n      this.formData.drawingInfo = \"\";\r\n      this.currentImageName = \"\";\r\n      this.currentImageSize = 0;\r\n\r\n      // 触发表单验证，显示drawingInfo字段的错误提示\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.validateField(\"drawingInfo\");\r\n        }\r\n      });\r\n\r\n      this.$message.success(\"图片已删除\");\r\n      console.log(\"文件删除完成\");\r\n    },\r\n\r\n    // 超出文件数量限制（保留方法以兼容旧代码）\r\n    handleExceed() {\r\n      this.$message.warning(\r\n        \"只能上传一张图片，请先删除已有图片后再上传新图片!\"\r\n      );\r\n    },\r\n\r\n    // 保存\r\n    handleSave() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.saveLoading = true;\r\n\r\n          // 根据选中的部门ID找到对应的部门名称\r\n          const selectedDept = this.deptList.find(dept => dept.id === this.formData.belongDeptKey);\r\n          const belongDept = selectedDept ? selectedDept.name : '';\r\n\r\n          // 准备保存的数据\r\n          const saveData = {\r\n            drawingName: this.formData.drawingName,\r\n            belongDeptKey: this.formData.belongDeptKey,\r\n            belongDept: belongDept, // 添加部门名称（中文）\r\n            drawingInfo: this.formData.drawingInfo,\r\n            id: this.editData && this.editData.id,\r\n            // ...this.formData,\r\n            // mode: this.mode,\r\n            // editId: this.editData && this.editData.id,\r\n          };\r\n\r\n          console.log(\"保存数据:\", saveData);\r\n          console.log(\"选中的部门:\", selectedDept);\r\n          console.log(\"部门名称:\", belongDept);\r\n          console.log(\"图片Base64长度:\", this.formData.drawingInfo ? this.formData.drawingInfo.length : 0);\r\n\r\n          this.$api[\"visualOpsManagement/visualOpsManagement-save\"](saveData).then(\r\n            () => {\r\n              this.saveLoading = false;\r\n              this.$message.success(\r\n                this.mode === \"add\" ? \"新增成功\" : \"编辑成功\"\r\n              );\r\n\r\n              // 触发保存事件，传递表单数据\r\n              this.$emit(\"save\", saveData);\r\n\r\n              this.handleClose();\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n\r\n    // 取消\r\n    handleCancel() {\r\n      this.handleClose();\r\n    },\r\n\r\n    // 关闭弹窗\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false);\r\n      this.$emit(\"close\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-top: 1px solid #e8e8e8;\r\n  text-align: right;\r\n  z-index: 1;\r\n}\r\n\r\n.el-form {\r\n  padding-bottom: 80px; // 为底部按钮留出空间\r\n}\r\n\r\n.el-upload__tip {\r\n  color: #999;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/deep/ .el-upload--picture-card {\r\n  width: 100px;\r\n  height: 100px;\r\n  line-height: 100px;\r\n}\r\n\r\n/deep/ .el-upload-list--picture-card .el-upload-list__item {\r\n  width: 100px;\r\n  height: 100px;\r\n}\r\n\r\n/deep/ .el-drawer__body {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n</style>\r\n"]}]}