import Main from '@/components/main/Main';
// 支付模块的路由

const systemRoutes = [
  {
    name: 'system/cfg',
    path: '/system/cfg',
    component: Main,
    redirect: '/permission',
    meta: {
      showInMenu: true,
      showAlways: true, // 表示一定要展示子菜单
      title: '系统管理',
    },
    children: [
      {
        name: 'systemHome',
        path: '/system/home',
        component: () => import('@/bysc_system/views/home'),
        meta: {
          showInMenu: true,
          path: '/system/home',
          title: '首页',
          icon: 'logo-apple'
        }
      },
      {
        name: 'permission',
        path: '/permission',
        redirect: '/system/user/table',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/permission',
          title: '权限管理',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'userTable',
            path: '/system/user/table',
            component: () => import('@/bysc_system/views/user'),
            meta: {
              showInMenu: true,
              path: '/system/user/table',
              title: '用户管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'roleTable',
            path: '/system/role/table',
            component: () => import('@/bysc_system/views/role'),
            meta: {
              showInMenu: true,
              path: '/system/role/table',
              title: '角色管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'resourceTable',
            path: '/system/resource/table',
            component: () => import('@/bysc_system/views/menus'),
            meta: {
              showInMenu: true,
              path: '/system/resource/table',
              title: '资源管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'organizationTable',
            path: '/system/organization/table',
            component: () => import('@/bysc_system/views/organization'),
            meta: {
              showInMenu: true,
              path: '/system/organization/table',
              title: '组织管理',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'systemBase',
        path: '/system/base',
        redirect: '/system/dict/table',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/base',
          title: '基础配置',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'dictTable',
            path: '/system/dict/table',
            component: () => import('@/bysc_system/views/sysDict'),
            meta: {
              showInMenu: true,
              path: '/system/dict/table',
              title: '系统字典',
              icon: 'logo-apple'
            }
          },
          // {
          //   name: 'dictBusinessTable',
          //   path: '/system/dict/business/table',
          //   component: () => import('@/bysc_system/views/businessDict'),
          //   meta: {
          //     showInMenu: true,
          //     path: '/system/dict/business/table',
          //     title: '业务字典',
          //     icon: 'logo-apple'
          //   }
          // },
          {
            name: 'tenantTable',
            path: '/base/tenant/table',
            component: () => import('@/bysc_system/views/tenant'),
            meta: {
              showInMenu: true,
              path: '/base/tenant/table',
              title: '租户管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'codeRuleTable',
            path: '/base/codeRule/table',
            component: () => import('@/bysc_system/views/codeRule'),
            meta: {
              showInMenu: true,
              path: '/base/codeRule/table',
              title: '编码规则',
              icon: 'logo-apple'
            }
          },
          {
            name: 'codeRuleSectionTable',
            path: '/base/codeRuleSection/table',
            component: () => import('@/bysc_system/views/codeRule/codeRuleSection'),
            meta: {
              showInMenu: false,
              path: '/base/codeRuleSection/table',
              title: '编码规则组成',
              icon: 'logo-apple'
            }
          },
          {
            name: 'config',
            path: '/system/base/config',
            component: () => import('@/bysc_system/views/config'),
            meta: {
              showInMenu: true,
              path: '/system/base/config',
              title: '系统设置',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'systemCode',
        path: '/system/code',
        redirect: '/system/code/generation',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/code',
          title: '代码生成',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'codeGeneration',
            path: '/system/code/generation',
            component: () => import('@/bysc_system/views/codeGeneration'),
            meta: {
              showInMenu: true,
              path: '/system/code/generation',
              title: '代码生成',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'thirdLogin',
        path: '/system/thirdLogin',
        redirect: '/system/thirdLogin/thirdList',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/thirdLogin',
          title: '第三方登录',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'thirdList',
            path: '/system/thirdLogin/thirdList',
            component: () => import('@/bysc_system/views/thirdLogin'),
            meta: {
              showInMenu: true,
              path: '/system/thirdLogin/thirdList',
              title: '第三方列表',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'commonModule',
        path: '/commonModule',
        redirect: '/system/commonModule/supplier',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          showAlways: true, // 表示一定要展示子菜单
          title: '公共模块',
        },
        children: [
          {
            name: 'supplier',
            path: '/system/commonModule/supplier',
            component: () => import('@/bysc_system/views/commonModule/supplier'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/supplier',
              title: '供应商',
              icon: 'logo-apple'
            },
          },
          {
            name: 'organization',
            path: '/system/commonModule/organization',
            component: () => import('@/bysc_system/views/commonModule/organization'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/organization',
              title: '组织管理',
              icon: 'logo-apple'
            },
          },
          {
            name: 'kingdeeUser',
            path: '/system/commonModule/kingdeeUser',
            component: () => import('@/bysc_system/views/commonModule/kingdeeUser'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/kingdeeUser',
              title: '金蝶账号',
              icon: 'logo-apple'
            },
          },
          {
            name: 'jdUser',
            path: '/system/commonModule/jdUser',
            component: () => import('@/bysc_system/views/commonModule/jdUser'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/jdUser',
              title: '京东慧采账号',
              icon: 'logo-apple'
            },
          },
          {
            name: 'costCenterManage',
            path: '/system/commonModule/costCenterManage',
            component: () => import('@/bysc_system/views/commonModule/costCenterManage'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/costCenterManage',
              title: '成本中心管理',
              icon: 'logo-apple'
            },
          },
          {
            name: 'jobNumberManage',
            path: '/system/commonModule/jobNumberManage',
            component: () => import('@/bysc_system/views/commonModule/jobNumberManage'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/jobNumberManage',
              title: '工号管理',
              icon: 'logo-apple'
            },
          },
          {
            name: 'staffCostCenterManage',
            path: '/system/commonModule/staffCostCenterManage',
            component: () => import('@/bysc_system/views/commonModule/staffCostCenterManage'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/staffCostCenterManage',
              title: '员工成本中心管理',
              icon: 'logo-apple'
            },
          },
          {
            name: 'finance',
            path: '/system/commonModule/finance',
            component: () => import('@/bysc_system/views/commonModule/finance'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/finance',
              title: '财务科目',
              icon: 'logo-apple'
            },
          },
          {
            name: 'staffJobNumberManage',
            path: '/system/commonModule/staffJobNumberManage',
            component: () => import('@/bysc_system/views/commonModule/staffJobNumberManage'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/staffJobNumberManage',
              title: '员工工时管理',
              icon: 'logo-apple'
            },
          },
          {
            name: 'salaryReport',
            path: '/system/commonModule/salaryReport',
            component: () => import('@/bysc_system/views/commonModule/salaryReport'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/salaryReport',
              title: '工资报表生成',
              icon: 'logo-apple'
            },
          },
          {
            name: 'expense',
            path: '/system/commonModule/expense',
            component: () => import('@/bysc_system/views/commonModule/expense'),
            meta: {
              showInMenu: true,
              path: '/system/commonModule/expense',
              title: '费用项目',
              icon: 'logo-apple'
            },
          },
        ]
      },
      {
        name: 'log',
        path: '/log',
        redirect: '/system/log/auditLog',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          showAlways: true, // 表示一定要展示子菜单
          title: '日志管理',
        },
        children: [
          {
            name: 'auditLog',
            path: '/system/log/auditLog',
            component: () => import('@/bysc_system/views/log/auditLog'),
            meta: {
              showInMenu: true,
              path: '/system/log/auditLog',
              title: '审计日志',
              icon: 'logo-apple'
            },
          },
          {
            name: 'loginLog',
            path: '/system/log/loginLog',
            component: () => import('@/bysc_system/views/log/loginLog'),
            meta: {
              showInMenu: true,
              path: '/system/log/loginLog',
              title: '登录审计',
              icon: 'logo-apple'
            },
          },
          {
            name: 'changePwdLog',
            path: '/system/log/changePwdLog',
            component: () => import('@/bysc_system/views/log/changePwdLog'),
            meta: {
              showInMenu: true,
              path: '/system/log/changePwdLog',
              title: '修改密码日志',
              icon: 'logo-apple'
            },
          },
        ]
      },
      // 看板管理
      {
        name: 'boardData',
        path: '/system/boardData',
        redirect: '/system/boardData/capital',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/boardData',
          title: '看板数据管理',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'capitalAirport',
            path: '/system/boardData/capital',
            redirect: '/system/boardData/capital/forecast',
            component: () => import('@/components/main/components/empty-router-view/empty'),
            meta: {
              showInMenu: true,
              title: '首都机场',
              icon: 'logo-apple'
            },
            children: [
              {
                name: 'forecastManage',
                path: '/system/boardData/capital/forecast',
                component: () => import('@/bysc_system/views/boardData/capital/forecast'),
                meta: {
                  showInMenu: true,
                  title: '值班领导管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'yesterdayFlights',
                path: '/system/boardData/capital/yesterdayFlights',
                component: () => import('@/bysc_system/views/boardData/capital/yesterdayFlights'),
                meta: {
                  showInMenu: true,
                  title: '昨日行李量管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'flightThreeTable',
                path: '/system/boardData/capital/flightThree',
                component: () => import('@/bysc_system/views/boardData/capital/flightThree'),
                meta: {
                  showInMenu: true,
                  title: '首都行李数据管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'yesterdayFlightData',
                path: '/system/boardData/capital/yesterdayFlightData',
                component: () => import('@/bysc_system/views/boardData/capital/yesterdayFlightData'),
                meta: {
                  showInMenu: true,
                  // title: '昨日航班/航班/保障航班架次数据管理',
                  title: '航班及旅客管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'yesterdayRouteDistance',
                path: '/system/boardData/capital/yesterdayRouteDistance',
                component: () => import('@/bysc_system/views/boardData/capital/yesterdayRouteDistance'),
                meta: {
                  showInMenu: true,
                  title: '昨日行驶里程管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'operationPassengerVolume',
                path: '/system/boardData/capital/operationPassengerVolume',
                component: () => import('@/bysc_system/views/boardData/capital/operationPassengerVolume'),
                meta: {
                  showInMenu: true,
                  title: '首都捷运数据管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'capitalAPU',
                path: '/system/boardData/capital/APU',
                component: () => import('@/bysc_system/views/boardData/capital/APU'),
                meta: {
                  showInMenu: true,
                  title: '首都客桥数据管理',
                  icon: 'logo-apple'
                }
              }
            ]
          },


          // 大兴机场
          {
            name: 'daxingAirport',
            path: '/system/boardData/daxing',
            redirect: '/system/boardData/daxing/forecast',
            component: () => import('@/components/main/components/empty-router-view/empty'),
            meta: {
              showInMenu: true,
              title: '大兴机场',
              icon: 'logo-apple'
            },
            children: [
              {
                name: 'daxingForecastManage',
                path: '/system/boardData/daxing/forecast',
                component: () => import('@/bysc_system/views/boardData/daxing/forecast'),
                meta: {
                  showInMenu: true,
                  title: '值班领导管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'daxingYesterdayFlights',
                path: '/system/boardData/daxing/yesterdayFlights',
                component: () => import('@/bysc_system/views/boardData/daxing/yesterdayFlights'),
                meta: {
                  showInMenu: true,
                  title: '昨日行李量管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'daxingFlightThreeTable',
                path: '/system/boardData/daxing/flightThree',
                component: () => import('@/bysc_system/views/boardData/daxing/flightThree'),
                meta: {
                  showInMenu: true,
                  title: '大兴行李数据管理',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'daxingYesterdayFlightData',
                path: '/system/boardData/daxing/yesterdayFlightData',
                component: () => import('@/bysc_system/views/boardData/daxing/yesterdayFlightData'),
                meta: {
                  showInMenu: true,
                  // title: '昨日航班/航班/保障航班架次数据管理',
                  title: '航班及旅客管理',
                  icon: 'logo-apple'
                }
              },

              {
                name: 'daxingAPU',
                path: '/system/boardData/daxing/APU',
                component: () => import('@/bysc_system/views/boardData/daxing/APU'),
                meta: {
                  showInMenu: true,
                  title: '大兴客桥数据管理',
                  icon: 'logo-apple'
                }
              }

            ]
          }


        ]
      },
      // 新增技术部看板
      {
        name: 'techDashboard',
        path: '/system/techDashboard',
        redirect: '/system/techDashboard/techData',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/techDashboard',
          title: '技术部看板',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'techData',
            path: '/system/techDashboard/techData',
            component: () => import('@/bysc_system/views/techDashboard/techData'),
            meta: {
              showInMenu: true,
              path: '/system/techDashboard/techData',
              title: '技术管理数据',
              icon: 'logo-apple'
            }
          },
          {
            name: 'safetyManagement',
            path: '/system/techDashboard/safety',
            redirect: '/system/techDashboard/safety/capital',
            component: () => import('@/components/main/components/empty-router-view/empty'),
            meta: {
              showInMenu: true,
              path: '/system/techDashboard/safety',
              title: '安全管理数据',
              icon: 'logo-apple'
            },
            children: [
              {
                name: 'capitalSafetyData',
                path: '/system/techDashboard/safety/capital',
                component: () => import('@/bysc_system/views/techDashboard/safety/capital'),
                meta: {
                  showInMenu: true,
                  path: '/system/techDashboard/safety/capital',
                  title: '首都安全管理数据',
                  icon: 'logo-apple'
                }
              },
              {
                name: 'daxingSafetyData',
                path: '/system/techDashboard/safety/daxing',
                component: () => import('@/bysc_system/views/techDashboard/safety/daxing'),
                meta: {
                  showInMenu: true,
                  path: '/system/techDashboard/safety/daxing',
                  title: '大兴安全管理数据',
                  icon: 'logo-apple'
                }
              }
            ]
          }
        ]
      },
      // 新增人力看板菜单
      {
        name: 'hrDashboard',
        path: '/system/hrDashboard',
        redirect: '/system/hrDashboard/overview',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          showAlways: true,
          title: '人力看板',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'hrOverview',
            path: '/system/hrDashboard/overview',
            component: () => import('@/bysc_system/views/hrDashboard/overview'),
            meta: {
              showInMenu: true,
              path: '/system/hrDashboard/overview',
              title: '人力概览',
              icon: 'logo-apple'
            }
          },
          {
            name: 'enterData',
            path: '/system/hrDashboard/enterData',
            component: () => import('@/bysc_system/views/hrDashboard/enterData'),
            meta: {
              showInMenu: true,
              path: '/system/hrDashboard/enterData',
              title: '数据录入',
              icon: 'logo-apple'
            }
          }
        ]
      },
      // 可视化运维管理

      {
        name: 'visualOpsManagement',
        path: '/system/visualOpsManagement',
        redirect: '/system/visualOpsManagement/drawingManagement',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          showAlways: true,
          title: '可视化运维管理',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'drawingManagement',
            path: '/system/visualOpsManagement/drawingManagement',
            component: () => import('@/bysc_system/views/visualOpsManagement/drawingManagement'),
            meta: {
              showInMenu: true,
              path: '/system/visualOpsManagement/drawingManagement',
              title: '图纸管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'areaManagement',
            path: '/system/visualOpsManagement/areaManagement',
            component: () => import('@/bysc_system/views/visualOpsManagement/areaManagement'),
            meta: {
              showInMenu: true,
              path: '/system/visualOpsManagement/areaManagement',
              title: '区域绘制',
              icon: 'logo-apple'
            }
          },
          {
            name: 'areaBinding',
            path: '/system/visualOpsManagement/areaBinding',
            component: () => import('@/bysc_system/views/visualOpsManagement/areaBinding'),
            meta: {
              showInMenu: true,
              path: '/system/visualOpsManagement/areaBinding',
              title: '区域管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'deviceBinding',
            path: '/system/visualOpsManagement/deviceBinding',
            component: () => import('@/bysc_system/views/visualOpsManagement/deviceBinding'),
            meta: {
              showInMenu: true,
              path: '/system/visualOpsManagement/deviceBinding',
              title: '区域绑定设备',
              icon: 'logo-apple'
            }
          }
        ]
      }

    ]
  }
];

export default systemRoutes;
