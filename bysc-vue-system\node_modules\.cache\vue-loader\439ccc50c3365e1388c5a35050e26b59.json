{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue?vue&type=template&id=2291e59b&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\components\\DrawingFormDialog.vue", "mtime": 1754276220640}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<el-drawer\n  :title=\"dialogTitle\"\n  :visible.sync=\"visible\"\n  direction=\"rtl\"\n  size=\"600px\"\n  :close-on-press-escape=\"false\"\n  :wrapperClosable=\"false\"\n  @close=\"handleClose\"\n>\n  <el-form\n    ref=\"form\"\n    :model=\"formData\"\n    :rules=\"rules\"\n    label-width=\"100px\"\n    label-position=\"left\"\n  >\n    <el-form-item label=\"图纸名称\" prop=\"drawingName\">\n      <el-input\n        v-model.trim=\"formData.drawingName\"\n        placeholder=\"请输入图纸名称\"\n        maxlength=\"50\"\n        show-word-limit\n         size=\"small\"\n        clearable\n      >\n      </el-input>\n    </el-form-item>\n\n    <el-form-item label=\"所属部门\" prop=\"belongDeptKey\">\n      <el-select\n        style=\"width: 100%\"\n        v-model=\"formData.belongDeptKey\"\n        placeholder=\"请选择\"\n        size=\"small\"\n        filterable\n        clearable\n      >\n        <el-option\n          :label=\"i.name\"\n          :value=\"i.id\"\n          v-for=\"i in deptList\"\n          :key=\"i.id\"\n        >\n        </el-option>\n      </el-select>\n    </el-form-item>\n\n    <el-form-item prop=\"drawingInfo\" required>\n      <span slot=\"label\"> 上传图片 </span>\n      <el-upload\n        ref=\"upload\"\n        action=\"#\"\n         size=\"small\"\n        :file-list=\"fileList\"\n        :on-change=\"handleFileChange\"\n        :before-remove=\"handleBeforeRemove\"\n        :on-remove=\"handleRemove\"\n        :on-preview=\"handlePreview\"\n        :on-exceed=\"handleExceed\"\n        :auto-upload=\"false\"\n        accept=\".png,.jpg,.jpeg\"\n        list-type=\"picture-card\"\n        :limit=\"1\"\n      >\n        <i class=\"el-icon-plus\"></i>\n        <div slot=\"tip\" class=\"el-upload__tip\">\n          支持格式：PNG、JPG、JPEG格式<br />\n          文件大小：图片各尺寸不超过10MB<br />\n          尺寸要求：分辨率控制在2000×2000像素内<br />\n          上传数量：仅一张\n        </div>\n      </el-upload>\n    </el-form-item>\n  </el-form>\n  <div class=\"drawer-footer\">\n    <el-button @click=\"handleCancel\">取消</el-button>\n    <el-button type=\"primary\" @click=\"handleSave\" :loading=\"saveLoading\"\n      >保存</el-button\n    >\n  </div>\n</el-drawer>\n", null]}