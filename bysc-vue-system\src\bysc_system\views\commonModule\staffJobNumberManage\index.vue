<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid api="systems/financeCostMonthly-page" :event-bus="searchEventBus" :search-params="searchForm"
          @datas="getDatas" @columnChange="getcolumn" :auto-load="true" ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="成本中心">
                <el-input v-model="searchForm.costCenterName" placeholder="请输入" size="small" clearable />
              </el-form-item>
              <el-form-item label="月份">
                <el-date-picker v-model="searchForm.workingMonth" value-format="yyyy-MM" type="month" placeholder="选择月" size="small" clearable>
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'staffJobNumberManage_add'" size="small" type="primary"
              @click="handleAdd">新增</el-button>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table"
            style="width: 100%">
            <el-table-column fixed="left" label="序号" type="index" width="50"></el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" prop="costCenterName"
              label="成本中心"></el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" prop="workingMonth"
              label="月份"></el-table-column>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="180">
              <template slot-scope="scope">
                <el-button v-permission="'staffJobNumberManage_copy'" @click="handleCopy(scope.row)" type="text"
                  size="small">复制</el-button>
                <el-button v-permission="'staffJobNumberManage_del'" @click="handleDelete(scope.row)" type="text"
                  size="small">删除</el-button>
                <el-button v-permission="'staffJobNumberManage_dts'" @click="handleGoDts(scope.row)" type="text"
                  size="small">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <!-- 新增 -->
    <el-dialog :title="'新增'" :visible.sync="showDialog" :close-on-click-modal="false" width="40%">
      <el-form :model="dialogForm" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="成本中心" prop="costCenterId">
          <el-select v-model="dialogForm.costCenterId" placeholder="请选择" clearable size="small">
            <el-option v-for="(item, index) in costCenterList" :key="index" :label="item.departName"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="月份" prop="workingMonth">
          <!-- <el-input disabled v-model.trim="dialogForm.workingMonth" size="small" placeholder="请输入"></el-input> -->
          <el-date-picker v-model="dialogForm.workingMonth" value-format="yyyy-MM" type="month" placeholder="选择月" size="small" clearable style="width:100%;">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showDialog = false">取消</el-button>
        <el-button size="small" type="primary" :loading="okLoading" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
    <user_details :show="showDts" :rowItem="rowItem" @refresh="resetTable" @close="showDts = false"></user_details>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
import dayjs from 'dayjs';
import user_details from './details.vue';
const defaultSearchForm = {};
const defaultDialogForm = {
  workingMonth: dayjs(new Date()).format('YYYY-MM')
};
export default {
  components: {
    Grid,
    user_details
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      okLoading: false,
      rules: {
        costCenterId: [
          {required: true, message: '请选择成本中心', trigger: 'blur,change'},
        ],
        workingMonth: [
          {required: true, message: '请选择月份', trigger: 'blur,change'},
        ]
      },
      showDialog: false,
      searchForm: _.cloneDeep(defaultSearchForm),
      tableData: [],
      dialogForm: _.cloneDeep(defaultDialogForm),
      showDts: false,
      costCenterList: [],
      rowItem: {},
    };
  },
  watch: {
  },
  mounted() {
    this.getcostCenterList();
  },
  methods: {
    getcostCenterList() {
      // 成本中心列表
      this.$api['systems/my-cost-center-for-add-monthly-data']({
        settlement: "working_hours"
      }).then(data => {
        this.costCenterList = data;
      });
    },
    handleAdd() {
      this.dialogForm = _.cloneDeep(defaultDialogForm);
      this.showDialog = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    // 复制
    handleCopy(e) {
      this.$confirm('确定复制该数据？', '复制提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['systems/financeCostMonthly-copy']({
          id: e.id
        }).then(data => {
          this.$message({
            type: 'success',
            message: '复制成功',
          });
          this.$refs.grid.query();
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消复制'
        });
      });
    },
    handleGoDts(e) {
      this.showDts = true;
      this.rowItem = e;
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['systems/financeCostMonthly-del']({id: e.id}).then(data => {
          this.$message({
            type: 'success',
            message: '删除成功',
          });
          this.resetTable();
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    submitForm() {
      this.okLoading = true;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$api['systems/financeCostMonthly-save'](this.dialogForm).then(data => {
            this.showDialog = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            this.okLoading = false;
          });
        } else {
          this.okLoading = false;
          return false;
        }
      });
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
  },
};
</script>
<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
