<template>
  <div class="table-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="自有人员历史数据环比" name="own">
        <div class="button-group">

          <el-button type="primary" size="small" v-permission="'hrData-add'" @click="handleAdd('OWN')">新增</el-button>

          <el-button type="success" size="small" v-permission="'hrData-import'" @click="handleDown ">下载导入模板</el-button>
          <el-button type="warning" size="small" v-permission="'hrData-export'" @click="handleImportTemplate">导入</el-button>
        </div>
        <el-table :data="tableData"  stripe fit>
          <el-table-column prop="year" label="年份"  align="center" header-align="center" />
          <el-table-column prop="personNum" label="年末自有人员总数" align="center" header-align="center" />
          <el-table-column prop="bwPersonNum" label="博维年末自有人员" align="center" header-align="center" />
          <el-table-column prop="swPersonNum" label="首维年末自有人员" align="center" header-align="center" />
          <el-table-column label="操作" width="200" fixed="right" align="center" header-align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" v-permission="'hrData-edit'" @click="handleEdit(scope.row, 'OWN')">
                编辑
              </el-button>
              <el-button type="text" size="small" v-permission="'hrData-delete'" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            :current-page="ownQuery.current"
            :page-size="ownQuery.limit"
            :total="ownTotal"
            layout="total, prev, pager, next"
            @current-change="handleOwnPageChange"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="融德人员历史数据环比" name="rongde">
        <div class="button-group">
          <!--  -->
          <el-button type="primary" size="small" v-permission="'hrData-add'" @click="handleAdd('RONGDE')">新增</el-button>
          <el-button type="success" size="small" v-permission="'hrData-import'" @click="handleDown">下载导入模板</el-button>
          <el-button type="warning" size="small" v-permission="'hrData-export'"  @click="handleImportTemplate">导入</el-button>
        </div>
        <el-table :data="rongdeTableData"  stripe fit>
          <el-table-column prop="year" label="年份"  align="center" header-align="center" />
          <el-table-column prop="personNum" label="年末融德人员总数" align="center" header-align="center" />
          <el-table-column prop="bwPersonNum" label="博维年末融德人员" align="center" header-align="center" />
          <el-table-column prop="swPersonNum" label="首维年末融德人员" align="center" header-align="center" />
          <el-table-column label="操作" width="200" fixed="right" align="center" header-align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" v-permission="'hrData-edit'" @click="handleEdit(scope.row, 'RONGDE')">
                编辑
              </el-button>
              <el-button type="text" size="small" v-permission="'hrData-delete'" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            :current-page="rongdeQuery.current"
            :page-size="rongdeQuery.limit"
            :total="rongdeTotal"
            layout="total, prev, pager, next"
            @current-change="handleRongdePageChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <Panel ref="panel" @submit="getTableData" />
    <bw-import
      :action="importUrl"
      :on-success="successInit"
      :on-error="errorInit"
      :before-upload="handleBeforeUpload"
      :extra-params="importExtraParams"
      accept=".xlsx"
      ref="importNode"
    ></bw-import>
  </div>
</template>

<script>

import Panel from './component/panel.vue';
import {mapActions} from "vuex";
import BwImport from "@/components/BwImport";
export default {
  name: 'HrDataComparison',
  components: {
    Panel,
    BwImport
  },
  data() {
    return {
      importUrl: '/api/syn/financePersonDataQoq/import-qoq',
      importExtraParams: {
        belongOrg: 'OWN'
      },
      activeTab: 'own',
      tableData: [],
      rongdeTableData: [],
      ownQuery: {
        current: 1,
        limit: 10,
        param: {
          belongOrg: 'OWN'
        }
      },
      rongdeQuery: {
        current: 1,
        limit: 10,
        param: {
          belongOrg: 'RONGDE'
        }
      },
      ownTotal: 0,
      rongdeTotal: 0
    };
  },
  methods: {
    ...mapActions(["download"]),
    handleAdd(type) {
      this.$refs.panel.open(null, 'add', type);
    },
    handleEdit(row, type) {
      this.$refs.panel.open(row, 'edit', type);
    },
    handleDelete(row) {
      console.log('row11', row);
      this.$confirm('确认删除该条记录?', '提示', {
        type: 'warning'
      }).then(() => {
        this.$api['hrDashboard/financePersonDataQoq-delete']({id: row.id}).then(() => {
          this.$message.success('删除成功');
          this.getTableData();
        });
      });
    },
    handleOwnPageChange(page) {
      this.ownQuery.current = page;
      this.getTableData();
    },
    handleRongdePageChange(page) {
      this.rongdeQuery.current = page;
      this.getTableData();
    },
    getTableData() {
      // 获取自有人员数据
      this.$api['hrDashboard/financePersonDataQoq-page'](this.ownQuery).then(res => {
        this.tableData = res.list;
        this.ownTotal = res.total;
      });

      // 获取融德人员数据
      this.$api['hrDashboard/financePersonDataQoq-page'](this.rongdeQuery).then(res => {
        this.rongdeTableData = res.list;
        this.rongdeTotal = res.total;
      });
    },
    handleImportTemplate() {
      this.importExtraParams.belongOrg = this.activeTab === 'own' ? 'OWN' : 'RONGDE';
      this.$refs.importNode.$refs.fileUpload.$refs['upload-inner'].handleClick();
    },
    handleBeforeUpload(file) {
      console.log('file', file);
      return true;
    },
    successInit(response, file, fileList) {
      console.log('response, file, fileList', response, file, fileList);
      this.$nextTick(() => {
        this.$message.success("导入成功");
        this.getTableData(); // 刷新表格数据
      });
    },
    errorInit(response, file, fileList) {
      const res = JSON.parse(JSON.stringify(response));
      this.$message.error(res.msg || "导入失败");
    },
    handleDown() {
      let url = `/api/syn/financePersonDataQoq/templateDownload?belongOrg=${this.activeTab === 'own' ? 'OWN' : 'RONGDE'}`;
      let downData = {
        url: url,
        downLoad: this.activeTab === 'own' ? '自有人员数据环比导入模板.xlsx' : '融德人员数据环比导入模板.xlsx',

      };
      this.download(downData); // 下载
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .button-group {
    margin-bottom: 16px;

    .el-button {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
