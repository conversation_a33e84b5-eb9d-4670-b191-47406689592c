<template>
  <el-drawer
    :title="'编辑'"
    :visible.sync="visible"
    :size="'60%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <el-form :model="editForm" ref="editForm" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="自动生成首都东区" disabled />
        </el-form-item>


        <el-form-item label="行李量" prop="luggageVolume">
          <el-input
            v-model="editForm.luggageVolume"
            placeholder="请输入行李量">
            <template slot="append">件</template>
          </el-input>
        </el-form-item>

        <el-form-item label="错分率" prop="errorRate">
          <el-input
            v-model="editForm.errorRate"
            placeholder="请输入错分率">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>

        <el-form-item label="迟运率" prop="delayRate">
          <el-input
            v-model="editForm.delayRate"
            placeholder="请输入迟运率">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>

        <el-form-item label="破损率" prop="brokenRate">
          <el-input
            v-model="editForm.brokenRate"
            placeholder="请输入破损率">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>

        <el-form-item label="日期" prop="luggageRateDate">
          <el-date-picker
            v-model="editForm.luggageRateDate"
            type="date"
            placeholder="自动生成前一天"
            disabled
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EditPanel',
  data() {
    const validatePercentage = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入百分比值'));
      } else if (isNaN(value) || value < 0 || value > 100) {
        callback(new Error('请输入0-100之间的数值'));
      } else {
        callback();
      }
    };

    const validateNumber = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入数值'));
      } else if (!Number.isInteger(Number(value)) || Number(value) < 0) {
        callback(new Error('请输入大于等于0的正整数'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      editForm: {
        name: '',
        errorRate: '',
        delayRate: '',
        brokenRate: '',
        luggageRateDate: null,
        luggageVolume: ''
      },
      rules: {
        errorRate: [
          {required: true, validator: validatePercentage, trigger: 'blur'}
        ],
        luggageVolume: [
          {required: true, validator: validateNumber, trigger: 'blur'}
        ],
        delayRate: [
          {required: true, validator: validatePercentage, trigger: 'blur'}
        ],
        brokenRate: [
          {required: true, validator: validatePercentage, trigger: 'blur'}
        ]
      }
    };
  },
  methods: {
    open(data) {
      this.visible = true;
      this.editForm = _.cloneDeep(data);
    },
    handleCancel() {
      this.visible = false;
      this.$refs.editForm.resetFields();
    },
    handleSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          // TODO: 调用保存接口
          this.$api['boardData/dashboardLuggageThreeRate-save'](this.editForm).then(() => {
            this.$message.success('保存成功');
            this.$emit('submit', this.editForm);
            this.visible = false;
          });

        }
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}
</style>
