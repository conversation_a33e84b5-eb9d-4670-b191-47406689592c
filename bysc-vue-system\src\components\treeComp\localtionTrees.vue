<template>
  <div class="app-container" style="padding:20px">
      <div>
        <!-- <el-input
          size="small"
          style="width: 100%;margin-bottom:8px"
          placeholder="请输入关键字搜索"
          v-model="locationDesc"
          @input="onSearch"
          @clear="onSearch"
          clearable
        >
        default-expand-all
        </el-input> -->
      </div>
      <div style="height: 80vh; overflow-y: scroll">
        <el-tree
          :props="props"
          :data="deptAndUserData"
          :show-checkbox="showCheckbox"
          :load="loadNode"
          v-bind="$attrs"
          v-on="$listeners"
          :lazy="isLazy"
          @node-click="handleNodeClick"
          @check="getTreeDatas"
          @check-change="handleCheckChange"
          :highlight-current="highlightCurrent"
          node-key="primaryId"
          :check-strictly="false"
          :key="setTimer"
          ref="tree"
          :default-checked-keys="value"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>
              <span style="margin-left: 2px">{{ node.label }}</span>
            </span>
          </span>
        </el-tree>
      </div>
      <div>
        <el-button style="float:right" size="small" type="primary" @click="savedata">保存</el-button>
      </div>
      <!-- <el-input
        size="small"
        style="width: 100%; margin-left: 8px"
        v-model="selectValue"
        type="textarea"
        :placeholder="treeplaceholder"
        readonly
        clearable
        @clear="clearSelect"
        slot="reference"
      ></el-input> -->

      <span class="dialog-footer" v-if="$attrs.multiple">
        <el-button size="small" @click="showSearchDeptTree = false"
          >取 消</el-button
        >
        <el-button size="small" type="primary" @click="submitBindPositionForm"
          >确 定</el-button
        >
      </span>
  </div>
</template>

<script>
import { keys } from "lodash";

export default {
  data() {
    return {
      temporaryStorageids:null,
      temporaryStorageNames:null,
      locationDesc: "",
      treeData: [],
      selectedNode: null,
      deptAndUserData: [],
      nodes: null,
      resolves: null,
      setTimer: null,
      timer: null,
      selectedNodes: [],
      selectedUserList: [],
      selectValue: "",
      showSearchDeptTree: false,
      checkedTreeList: [], // 选中的值
      checkedListKey: [],
    };
  },
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          label: "primaryCode",
          children: "children",
          isLeaf: "leaf",
        };
      },
    },
    value: {
      type: String | Array,
      default: "",
    },
    cnvalue: {
      type: String,
      default: "",
    },
    treeplaceholder: {
      type: String,
      default: "请选择数据",
    },
    highlightCurrent: {
      type: Boolean,
      default() {
        return true;
      },
    },
    isLazy: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showCheckbox: {
      type: Boolean,
      default() {
        return true;
      },
    },
    apiUrl: {
      type: String,
      default: "firstLineDept/get-loc-info-lazy-tree",
    },
  },
  watch: {
    value(val) {
      if (!val) {
        if (!this.$attrs.multiple) {
          this.clearSelect();
        } else {
          this.$refs.tree && this.$refs.tree.setCheckedKeys(val);
        }
      } else {
        // this.selectValue = filterTree[0] ? filterTree[0][this.treeProps.label] : '';
      }
    },
    cnvalue(val) {
      this.selectValue = val;
    },
  },
  mounted() {
    this.setTimer = new Date().getTime();
    this.selectValue = this.cnvalue;
    this.$api[this.apiUrl]({
        parentId: 0,
      }).then((data) => {
        this.deptAndUserData = data;
      });
      this.$refs.tree && this.$refs.tree.setCheckedKeys(this.value);
      this.temporaryStorageids = this.value;
      this.temporaryStorageNames = this.cnvalue.split(',');
  },
  methods: {
    // 展示树时
    showSearchTreeEvent() {
      this.$api[this.apiUrl]({
        parentId: 0,
      }).then((data) => {
        this.deptAndUserData = data;
      });
      this.$refs.tree && this.$refs.tree.setCheckedKeys(this.value);
      this.temporaryStorageids = this.value;
      this.temporaryStorageNames = this.cnvalue.split(',');
    },
    clearSelect() {
      this.selectValue = "";
      this.$refs.tree && this.$refs.tree.setCheckedKeys([]);
    },
    setCheckedKeys(ids) {
      this.$refs.tree && this.$refs.tree.setCheckedKeys(ids);
    },
    // 搜索树节点
    onSearch() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (this.locationDesc.length) {
          this.$api[this.apiUrl]({
            desc: this.locationDesc,
          }).then((res) => {
            this.deptAndUserData = res;
            if (this.showCheckbox) {
              setTimeout(() => {
                this.selectedNodes.length &&
                  this.$refs.tree.setCheckedNodes(this.selectedNodes);
              }, 500);
            }
            // else{
            //   this.$refs.tree.setCurrentKey(this.selectedNode);
            // }
          });
        } else {
          this.setTimer = new Date().getTime();
          this.deptAndUserData = [];
          this.nodes.data.primaryId = 0;
          this.loadNode(this.nodes, this.resolves);
        }
      }, 500);
    },
    handleCheckChange(data, checked,ii) {
        if (!checked) {
          // setTimeout(() => {
          //   let index = this.temporaryStorageids.findIndex(e=>{
          //     return e == data.primaryId;
          //   })
          //   console.log(index,checked,this.temporaryStorageids,data.primaryId,'index,pakdpajdpaodk');
          // }, 350);
        }
      },
    getTreeDatas(data, list) {
      // console.log(data, "====", list);
      let ids = list.checkedKeys;
      let cnarr = [];
      let arr = [];
      list.checkedNodes.forEach((e) => {
        // console.log(e);
        // let thisNode = this.$refs.tree.getNode(e.primaryId),
        //   keys = [e];
        // if (thisNode && thisNode.checked) {
        //   for (let i = thisNode.level; i > 1; i--) {
        //     this.$refs.tree.setChecked(thisNode.parent, false);
        //     if (!thisNode.parent.checked) {
        //       thisNode = thisNode.parent;
        //       keys.unshift(thisNode.data);
        //     }
        //   }
        // }
        // keys.forEach((row) => {
        //   arr.push(row.primaryCode);
        // });
        cnarr.push(e.primaryCode);
      });
      this.temporaryStorageids = ids;
      this.temporaryStorageNames = cnarr;
     
    },
    savedata(){
      console.log(this.$refs.tree.getCheckedNodes(),'this.$refs.tree.getCheckedNodes()');
      let selectNode = this.$refs.tree.getCheckedNodes();
      let ids = [];
      let cnarr = [];
      selectNode.forEach((e) => {
        ids.push(e.primaryId);
        cnarr.push(e.primaryCode);
      });
      this.temporaryStorageids = ids;
      this.temporaryStorageNames = cnarr;
      this.$emit("getSelectData", this.temporaryStorageids);
      this.$emit("getCnValue", this.temporaryStorageNames);
      this.showSearchDeptTree = false;
    },
    clearCheckedChildren(children) {
      children.length > 0 &&
        children.forEach((child) => {
          this.$refs.tree.setChecked(child, false);
          if (child.childNodes) {
            this.clearCheckedChildren(child.childNodes);
          }
        });
    },
    submitBindPositionForm() {
      if (this.$attrs.multiple) {
        let checkedListValue = [];
        this.checkedListKey = [];
        console.log("this.checkedTreeList", this.checkedTreeList);
        this.checkedTreeList.forEach((item) => {
          this.checkedListKey.push(item.primaryId);
          let thisNode = this.$refs.tree.getNode(item.primaryId);
          let keys = [thisNode.data];
          if (thisNode && thisNode.checked) {
            // 当前节点若被选中
            for (let i = thisNode.level; i > 1; i--) {
              // 判断是否有父级节点
              if (!thisNode.parent.checked) {
                // 父级节点未被选中，则将父节点替换成当前节点，往上继续查询，并将此节点key存入keys数组
                thisNode = thisNode.parent;
                keys.unshift(thisNode.data);
              }
            }
          }
          let arr = [];
          keys.forEach((row) => {
            arr.push(row.primaryCode);
          });
          checkedListValue.push(arr.join("/"));
          this.selectValue = checkedListValue.join("||");
          this.$emit("getSelectData", this.checkedListKey);
          this.$emit("getSelectCnData", this.selectValue);
          this.showSearchDeptTree = false;
        });
      }
    },
    handleNodeClick(data) {
      if (this.showCheckbox) {
        return;
      }
      if (data.primaryId.indexOf("org") !== -1) {
        setTimeout(() => {
          this.$refs.tree.setCurrentKey(this.selectedNode);
        }, 0);
      } else {
        this.selectedNodes = [data];
        this.selectedNode = data.primaryId;
        this.$emit("treeNode", data);
      }
    },
    // 点击懒加载
    loadNode(node, resolve) {
      if (node.level === 0) {
        return;
      }
      this.nodes = node;
      this.resolves = resolve;
      this.$api[this.apiUrl]({
        parentId: node.data.primaryId,
      }).then((res) => {
        resolve(res);
        if (this.showCheckbox) {
          setTimeout(() => {
            // console.log(this.value, '====');
            this.$refs.tree.setCheckedKeys(this.temporaryStorageids);
          }, 100);
        } else {
          setTimeout(() => {
            this.$refs.tree.setCurrentKey(this.selectedNode);
          }, 0);
        }
      });
    },
  },
};
</script>

    <style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}

.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>