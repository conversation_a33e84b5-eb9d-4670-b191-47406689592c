{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\utils\\routeParams.js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\utils\\routeParams.js", "mtime": 1754276220644}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1752725539784}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/web.dom.iterable\";\nimport _defineProperty from \"D:/boweiWorkSpace/pc/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\r\n * 路由参数持久化工具\r\n * 解决页面刷新后参数丢失的问题\r\n */\n\n/**\r\n * 保存路由参数到localStorage和URL\r\n * @param {Object} router - Vue Router实例\r\n * @param {Object} route - 当前路由对象\r\n * @param {Object} params - 要保存的参数对象\r\n * @param {string} storageKey - localStorage的键名\r\n */\nexport function saveRouteParams(router, route, params) {\n  var storageKey = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'routeParams';\n  // 保存到localStorage\n  var existingParams = getStoredParams(storageKey) || {};\n  var updatedParams = _objectSpread(_objectSpread({}, existingParams), params);\n  localStorage.setItem(storageKey, JSON.stringify(updatedParams));\n\n  // 更新URL查询参数\n  var currentQuery = _objectSpread({}, route.query);\n  Object.keys(params).forEach(function (key) {\n    if (params[key] !== null && params[key] !== undefined) {\n      currentQuery[key] = params[key];\n    }\n  });\n\n  // 如果URL参数有变化，则更新URL\n  if (JSON.stringify(currentQuery) !== JSON.stringify(route.query)) {\n    router.replace({\n      query: currentQuery\n    });\n  }\n}\n\n/**\r\n * 获取路由参数（优先从URL获取，其次从localStorage）\r\n * @param {Object} route - 当前路由对象\r\n * @param {string|Array} paramNames - 参数名称（字符串或数组）\r\n * @param {string} storageKey - localStorage的键名\r\n * @returns {Object|string} 参数值\r\n */\nexport function getRouteParams(route, paramNames) {\n  var storageKey = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'routeParams';\n  var storedParams = getStoredParams(storageKey) || {};\n  if (typeof paramNames === 'string') {\n    // 单个参数\n    return route.query[paramNames] || route.params[paramNames] || storedParams[paramNames];\n  } else if (Array.isArray(paramNames)) {\n    // 多个参数\n    var result = {};\n    paramNames.forEach(function (name) {\n      result[name] = route.query[name] || route.params[name] || storedParams[name];\n    });\n    return result;\n  }\n  return null;\n}\n\n/**\r\n * 从localStorage获取存储的参数\r\n * @param {string} storageKey - localStorage的键名\r\n * @returns {Object|null} 存储的参数对象\r\n */\nexport function getStoredParams() {\n  var storageKey = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'routeParams';\n  try {\n    var stored = localStorage.getItem(storageKey);\n    return stored ? JSON.parse(stored) : null;\n  } catch (error) {\n    console.error('Error parsing stored route params:', error);\n    return null;\n  }\n}\n\n/**\r\n * 清除存储的路由参数\r\n * @param {string|Array} paramNames - 要清除的参数名称（可选，不传则清除所有）\r\n * @param {string} storageKey - localStorage的键名\r\n */\nexport function clearRouteParams() {\n  var paramNames = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  var storageKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'routeParams';\n  if (!paramNames) {\n    // 清除所有参数\n    localStorage.removeItem(storageKey);\n  } else {\n    // 清除指定参数\n    var storedParams = getStoredParams(storageKey) || {};\n    var names = Array.isArray(paramNames) ? paramNames : [paramNames];\n    names.forEach(function (name) {\n      delete storedParams[name];\n    });\n    if (Object.keys(storedParams).length === 0) {\n      localStorage.removeItem(storageKey);\n    } else {\n      localStorage.setItem(storageKey, JSON.stringify(storedParams));\n    }\n  }\n}\n\n/**\r\n * Vue Mixin - 为组件提供路由参数持久化功能\r\n */\nexport var RouteParamsMixin = {\n  methods: {\n    /**\r\n     * 保存当前页面的路由参数\r\n     * @param {Object} params - 要保存的参数\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\n    saveCurrentRouteParams: function saveCurrentRouteParams(params, storageKey) {\n      var key = storageKey || \"\".concat(this.$route.name, \"_params\");\n      saveRouteParams(this.$router, this.$route, params, key);\n    },\n    /**\r\n     * 获取当前页面的路由参数\r\n     * @param {string|Array} paramNames - 参数名称\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\n    getCurrentRouteParams: function getCurrentRouteParams(paramNames, storageKey) {\n      var key = storageKey || \"\".concat(this.$route.name, \"_params\");\n      return getRouteParams(this.$route, paramNames, key);\n    },\n    /**\r\n     * 清除当前页面的路由参数\r\n     * @param {string|Array} paramNames - 要清除的参数名称（可选）\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\n    clearCurrentRouteParams: function clearCurrentRouteParams(paramNames, storageKey) {\n      var key = storageKey || \"\".concat(this.$route.name, \"_params\");\n      clearRouteParams(paramNames, key);\n    }\n  }\n};\n\n/**\r\n * 页面导航时自动保存参数的工具函数\r\n * @param {Object} router - Vue Router实例\r\n * @param {string} routeName - 目标路由名称\r\n * @param {Object} params - 路由参数\r\n * @param {Object} options - 其他选项\r\n */\nexport function navigateWithParams(router, routeName) {\n  var params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _options$query = options.query,\n    query = _options$query === void 0 ? {} : _options$query,\n    _options$replace = options.replace,\n    replace = _options$replace === void 0 ? false : _options$replace,\n    storageKey = options.storageKey;\n\n  // 保存参数到localStorage\n  var key = storageKey || \"\".concat(routeName, \"_params\");\n  localStorage.setItem(key, JSON.stringify(params));\n\n  // 合并查询参数\n  var finalQuery = _objectSpread(_objectSpread({}, query), params);\n\n  // 导航\n  var navigationMethod = replace ? 'replace' : 'push';\n  router[navigationMethod]({\n    name: routeName,\n    query: finalQuery\n  });\n}", {"version": 3, "names": ["saveRouteParams", "router", "route", "params", "storageKey", "arguments", "length", "undefined", "existingParams", "getStoredParams", "updatedParams", "_objectSpread", "localStorage", "setItem", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "query", "Object", "keys", "for<PERSON>ach", "key", "replace", "getRouteParams", "paramNames", "storedParams", "Array", "isArray", "result", "name", "stored", "getItem", "parse", "error", "console", "clearRouteParams", "removeItem", "names", "RouteParamsMixin", "methods", "saveCurrentRouteParams", "concat", "$route", "$router", "getCurrentRouteParams", "clearCurrentRouteParams", "navigateWithParams", "routeName", "options", "_options$query", "_options$replace", "finalQuery", "navigationMethod"], "sources": ["D:/boweiWorkSpace/pc/bysc-vue-system/src/utils/routeParams.js"], "sourcesContent": ["/**\r\n * 路由参数持久化工具\r\n * 解决页面刷新后参数丢失的问题\r\n */\r\n\r\n/**\r\n * 保存路由参数到localStorage和URL\r\n * @param {Object} router - Vue Router实例\r\n * @param {Object} route - 当前路由对象\r\n * @param {Object} params - 要保存的参数对象\r\n * @param {string} storageKey - localStorage的键名\r\n */\r\nexport function saveRouteParams(router, route, params, storageKey = 'routeParams') {\r\n  // 保存到localStorage\r\n  const existingParams = getStoredParams(storageKey) || {};\r\n  const updatedParams = { ...existingParams, ...params };\r\n  localStorage.setItem(storageKey, JSON.stringify(updatedParams));\r\n  \r\n  // 更新URL查询参数\r\n  const currentQuery = { ...route.query };\r\n  Object.keys(params).forEach(key => {\r\n    if (params[key] !== null && params[key] !== undefined) {\r\n      currentQuery[key] = params[key];\r\n    }\r\n  });\r\n  \r\n  // 如果URL参数有变化，则更新URL\r\n  if (JSON.stringify(currentQuery) !== JSON.stringify(route.query)) {\r\n    router.replace({\r\n      query: currentQuery\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * 获取路由参数（优先从URL获取，其次从localStorage）\r\n * @param {Object} route - 当前路由对象\r\n * @param {string|Array} paramNames - 参数名称（字符串或数组）\r\n * @param {string} storageKey - localStorage的键名\r\n * @returns {Object|string} 参数值\r\n */\r\nexport function getRouteParams(route, paramNames, storageKey = 'routeParams') {\r\n  const storedParams = getStoredParams(storageKey) || {};\r\n  \r\n  if (typeof paramNames === 'string') {\r\n    // 单个参数\r\n    return route.query[paramNames] || \r\n           route.params[paramNames] || \r\n           storedParams[paramNames];\r\n  } else if (Array.isArray(paramNames)) {\r\n    // 多个参数\r\n    const result = {};\r\n    paramNames.forEach(name => {\r\n      result[name] = route.query[name] || \r\n                    route.params[name] || \r\n                    storedParams[name];\r\n    });\r\n    return result;\r\n  }\r\n  \r\n  return null;\r\n}\r\n\r\n/**\r\n * 从localStorage获取存储的参数\r\n * @param {string} storageKey - localStorage的键名\r\n * @returns {Object|null} 存储的参数对象\r\n */\r\nexport function getStoredParams(storageKey = 'routeParams') {\r\n  try {\r\n    const stored = localStorage.getItem(storageKey);\r\n    return stored ? JSON.parse(stored) : null;\r\n  } catch (error) {\r\n    console.error('Error parsing stored route params:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * 清除存储的路由参数\r\n * @param {string|Array} paramNames - 要清除的参数名称（可选，不传则清除所有）\r\n * @param {string} storageKey - localStorage的键名\r\n */\r\nexport function clearRouteParams(paramNames = null, storageKey = 'routeParams') {\r\n  if (!paramNames) {\r\n    // 清除所有参数\r\n    localStorage.removeItem(storageKey);\r\n  } else {\r\n    // 清除指定参数\r\n    const storedParams = getStoredParams(storageKey) || {};\r\n    const names = Array.isArray(paramNames) ? paramNames : [paramNames];\r\n    \r\n    names.forEach(name => {\r\n      delete storedParams[name];\r\n    });\r\n    \r\n    if (Object.keys(storedParams).length === 0) {\r\n      localStorage.removeItem(storageKey);\r\n    } else {\r\n      localStorage.setItem(storageKey, JSON.stringify(storedParams));\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Vue Mixin - 为组件提供路由参数持久化功能\r\n */\r\nexport const RouteParamsMixin = {\r\n  methods: {\r\n    /**\r\n     * 保存当前页面的路由参数\r\n     * @param {Object} params - 要保存的参数\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\r\n    saveCurrentRouteParams(params, storageKey) {\r\n      const key = storageKey || `${this.$route.name}_params`;\r\n      saveRouteParams(this.$router, this.$route, params, key);\r\n    },\r\n    \r\n    /**\r\n     * 获取当前页面的路由参数\r\n     * @param {string|Array} paramNames - 参数名称\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\r\n    getCurrentRouteParams(paramNames, storageKey) {\r\n      const key = storageKey || `${this.$route.name}_params`;\r\n      return getRouteParams(this.$route, paramNames, key);\r\n    },\r\n    \r\n    /**\r\n     * 清除当前页面的路由参数\r\n     * @param {string|Array} paramNames - 要清除的参数名称（可选）\r\n     * @param {string} storageKey - 存储键名（可选）\r\n     */\r\n    clearCurrentRouteParams(paramNames, storageKey) {\r\n      const key = storageKey || `${this.$route.name}_params`;\r\n      clearRouteParams(paramNames, key);\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * 页面导航时自动保存参数的工具函数\r\n * @param {Object} router - Vue Router实例\r\n * @param {string} routeName - 目标路由名称\r\n * @param {Object} params - 路由参数\r\n * @param {Object} options - 其他选项\r\n */\r\nexport function navigateWithParams(router, routeName, params = {}, options = {}) {\r\n  const { query = {}, replace = false, storageKey } = options;\r\n  \r\n  // 保存参数到localStorage\r\n  const key = storageKey || `${routeName}_params`;\r\n  localStorage.setItem(key, JSON.stringify(params));\r\n  \r\n  // 合并查询参数\r\n  const finalQuery = { ...query, ...params };\r\n  \r\n  // 导航\r\n  const navigationMethod = replace ? 'replace' : 'push';\r\n  router[navigationMethod]({\r\n    name: routeName,\r\n    query: finalQuery\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAA8B;EAAA,IAA5BC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,aAAa;EAC/E;EACA,IAAMG,cAAc,GAAGC,eAAe,CAACL,UAAU,CAAC,IAAI,CAAC,CAAC;EACxD,IAAMM,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQH,cAAc,GAAKL,MAAM,CAAE;EACtDS,YAAY,CAACC,OAAO,CAACT,UAAU,EAAEU,IAAI,CAACC,SAAS,CAACL,aAAa,CAAC,CAAC;;EAE/D;EACA,IAAMM,YAAY,GAAAL,aAAA,KAAQT,KAAK,CAACe,KAAK,CAAE;EACvCC,MAAM,CAACC,IAAI,CAAChB,MAAM,CAAC,CAACiB,OAAO,CAAC,UAAAC,GAAG,EAAI;IACjC,IAAIlB,MAAM,CAACkB,GAAG,CAAC,KAAK,IAAI,IAAIlB,MAAM,CAACkB,GAAG,CAAC,KAAKd,SAAS,EAAE;MACrDS,YAAY,CAACK,GAAG,CAAC,GAAGlB,MAAM,CAACkB,GAAG,CAAC;IACjC;EACF,CAAC,CAAC;;EAEF;EACA,IAAIP,IAAI,CAACC,SAAS,CAACC,YAAY,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACb,KAAK,CAACe,KAAK,CAAC,EAAE;IAChEhB,MAAM,CAACqB,OAAO,CAAC;MACbL,KAAK,EAAED;IACT,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,cAAcA,CAACrB,KAAK,EAAEsB,UAAU,EAA8B;EAAA,IAA5BpB,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,aAAa;EAC1E,IAAMoB,YAAY,GAAGhB,eAAe,CAACL,UAAU,CAAC,IAAI,CAAC,CAAC;EAEtD,IAAI,OAAOoB,UAAU,KAAK,QAAQ,EAAE;IAClC;IACA,OAAOtB,KAAK,CAACe,KAAK,CAACO,UAAU,CAAC,IACvBtB,KAAK,CAACC,MAAM,CAACqB,UAAU,CAAC,IACxBC,YAAY,CAACD,UAAU,CAAC;EACjC,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IACpC;IACA,IAAMI,MAAM,GAAG,CAAC,CAAC;IACjBJ,UAAU,CAACJ,OAAO,CAAC,UAAAS,IAAI,EAAI;MACzBD,MAAM,CAACC,IAAI,CAAC,GAAG3B,KAAK,CAACe,KAAK,CAACY,IAAI,CAAC,IAClB3B,KAAK,CAACC,MAAM,CAAC0B,IAAI,CAAC,IAClBJ,YAAY,CAACI,IAAI,CAAC;IAClC,CAAC,CAAC;IACF,OAAOD,MAAM;EACf;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASnB,eAAeA,CAAA,EAA6B;EAAA,IAA5BL,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,aAAa;EACxD,IAAI;IACF,IAAMyB,MAAM,GAAGlB,YAAY,CAACmB,OAAO,CAAC3B,UAAU,CAAC;IAC/C,OAAO0B,MAAM,GAAGhB,IAAI,CAACkB,KAAK,CAACF,MAAM,CAAC,GAAG,IAAI;EAC3C,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAAA,EAAgD;EAAA,IAA/CX,UAAU,GAAAnB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAED,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,aAAa;EAC5E,IAAI,CAACmB,UAAU,EAAE;IACf;IACAZ,YAAY,CAACwB,UAAU,CAAChC,UAAU,CAAC;EACrC,CAAC,MAAM;IACL;IACA,IAAMqB,YAAY,GAAGhB,eAAe,CAACL,UAAU,CAAC,IAAI,CAAC,CAAC;IACtD,IAAMiC,KAAK,GAAGX,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;IAEnEa,KAAK,CAACjB,OAAO,CAAC,UAAAS,IAAI,EAAI;MACpB,OAAOJ,YAAY,CAACI,IAAI,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAIX,MAAM,CAACC,IAAI,CAACM,YAAY,CAAC,CAACnB,MAAM,KAAK,CAAC,EAAE;MAC1CM,YAAY,CAACwB,UAAU,CAAChC,UAAU,CAAC;IACrC,CAAC,MAAM;MACLQ,YAAY,CAACC,OAAO,CAACT,UAAU,EAAEU,IAAI,CAACC,SAAS,CAACU,YAAY,CAAC,CAAC;IAChE;EACF;AACF;;AAEA;AACA;AACA;AACA,OAAO,IAAMa,gBAAgB,GAAG;EAC9BC,OAAO,EAAE;IACP;AACJ;AACA;AACA;AACA;IACIC,sBAAsB,WAAAA,uBAACrC,MAAM,EAAEC,UAAU,EAAE;MACzC,IAAMiB,GAAG,GAAGjB,UAAU,OAAAqC,MAAA,CAAO,IAAI,CAACC,MAAM,CAACb,IAAI,YAAS;MACtD7B,eAAe,CAAC,IAAI,CAAC2C,OAAO,EAAE,IAAI,CAACD,MAAM,EAAEvC,MAAM,EAAEkB,GAAG,CAAC;IACzD,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIuB,qBAAqB,WAAAA,sBAACpB,UAAU,EAAEpB,UAAU,EAAE;MAC5C,IAAMiB,GAAG,GAAGjB,UAAU,OAAAqC,MAAA,CAAO,IAAI,CAACC,MAAM,CAACb,IAAI,YAAS;MACtD,OAAON,cAAc,CAAC,IAAI,CAACmB,MAAM,EAAElB,UAAU,EAAEH,GAAG,CAAC;IACrD,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIwB,uBAAuB,WAAAA,wBAACrB,UAAU,EAAEpB,UAAU,EAAE;MAC9C,IAAMiB,GAAG,GAAGjB,UAAU,OAAAqC,MAAA,CAAO,IAAI,CAACC,MAAM,CAACb,IAAI,YAAS;MACtDM,gBAAgB,CAACX,UAAU,EAAEH,GAAG,CAAC;IACnC;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,kBAAkBA,CAAC7C,MAAM,EAAE8C,SAAS,EAA6B;EAAA,IAA3B5C,MAAM,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAE2C,OAAO,GAAA3C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC7E,IAAA4C,cAAA,GAAoDD,OAAO,CAAnD/B,KAAK;IAALA,KAAK,GAAAgC,cAAA,cAAG,CAAC,CAAC,GAAAA,cAAA;IAAAC,gBAAA,GAAkCF,OAAO,CAAvC1B,OAAO;IAAPA,OAAO,GAAA4B,gBAAA,cAAG,KAAK,GAAAA,gBAAA;IAAE9C,UAAU,GAAK4C,OAAO,CAAtB5C,UAAU;;EAE/C;EACA,IAAMiB,GAAG,GAAGjB,UAAU,OAAAqC,MAAA,CAAOM,SAAS,YAAS;EAC/CnC,YAAY,CAACC,OAAO,CAACQ,GAAG,EAAEP,IAAI,CAACC,SAAS,CAACZ,MAAM,CAAC,CAAC;;EAEjD;EACA,IAAMgD,UAAU,GAAAxC,aAAA,CAAAA,aAAA,KAAQM,KAAK,GAAKd,MAAM,CAAE;;EAE1C;EACA,IAAMiD,gBAAgB,GAAG9B,OAAO,GAAG,SAAS,GAAG,MAAM;EACrDrB,MAAM,CAACmD,gBAAgB,CAAC,CAAC;IACvBvB,IAAI,EAAEkB,SAAS;IACf9B,KAAK,EAAEkC;EACT,CAAC,CAAC;AACJ"}]}