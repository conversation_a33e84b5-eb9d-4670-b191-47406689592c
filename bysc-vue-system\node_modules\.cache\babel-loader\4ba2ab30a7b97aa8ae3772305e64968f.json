{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\areaManagement\\index.vue", "mtime": 1754276220635}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\babel.config.js", "mtime": 1726624932602}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es6.array.fill\";\nimport \"core-js/modules/web.dom.iterable\";\nimport _toConsumableArray from \"D:/boweiWorkSpace/pc/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _defineProperty from \"D:/boweiWorkSpace/pc/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport \"core-js/modules/es6.regexp.replace\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport default {\n  name: 'callout',\n  components: {},\n  props: {\n    areaArr: Array\n  },\n  data: function data() {\n    return {\n      imgForm: {},\n      imgModal: false,\n      imgOkLoading: false,\n      imgFormValidate: {},\n      isOpenLike: false,\n      imgUrl: '',\n      roomNameList: [],\n      spinShow: false,\n      imgPath: '',\n      finalArr: [],\n      // 绝对坐标数组，用于绘制\n      finalArrRelative: [],\n      // 相对坐标数组，用于保存\n      dataArr: [],\n      isShow: false,\n      canvas: '',\n      ctx: '',\n      ctxX: 0,\n      ctxY: 0,\n      lineWidth: 3,\n      type: 'L',\n      typeOption: [{\n        label: '线',\n        value: 'L'\n      }\n      // { label: '矩形', value: 'R' }\n      //   {label: \"箭头\", value: \"A\"},\n      //   {label: \"文字\", value: \"T\"},\n      ],\n      canvasHistory: [],\n      step: 0,\n      loading: false,\n      fillStyle: '#CB0707',\n      strokeStyle: '#1E90FF',\n      lineNum: 2,\n      linePeak: [],\n      lineStep: 2,\n      ellipseR: 0.5,\n      dialogVisible: false,\n      isUnfold: true,\n      fontSize: 18,\n      fontColor: '#333333',\n      fontFamily: 'Microsoft YaHei, Arial, sans-serif',\n      img: new Image(),\n      hoveredAreaIndex: -1,\n      // 当前鼠标悬停的区域索引\n      highlightColor: '#2E8AE6',\n      deleteMode: false,\n      // 是否处于删除模式\n      lastHoveredAreaIndex: -1,\n      // 用于表格行悬停时记录之前高亮的区域索引\n      zoomLevel: 1,\n      initialZoomLevel: 1,\n      defaultZoomFactor: 1,\n      // 增大默认缩放系数到1.5\n      showAreaNames: true,\n      // 区域表单相关\n      areaFormVisible: false,\n      areaFormLoading: false,\n      areaForm: {\n        roomName: '',\n        roomType: '',\n        remark: ''\n      },\n      areaFormRules: {\n        roomName: [{\n          required: true,\n          message: '请输入名称',\n          trigger: 'blur'\n        }]\n      },\n      tempAreaData: null,\n      // 临时存储新绘制的区域数据\n      debounceTimer: null,\n      // 添加防抖定时器\n      // 滚动控制相关\n      scrollLeftBtn: null,\n      scrollRightBtn: null,\n      // 保存当前drawingId，用于刷新页面时恢复\n      currentDrawingId: ''\n    };\n  },\n  computed: {\n    defaultZoomText: function defaultZoomText() {\n      return \"\".concat(Math.round(this.defaultZoomFactor * 100), \"%\");\n    },\n    // 判断是否需要显示滚动按钮\n    showScrollButtons: function showScrollButtons() {\n      if (!this.$refs.canvasContainer || !this.canvas) {\n        return false;\n      }\n      // 当画布宽度大于容器宽度时显示滚动按钮\n      return this.canvas.width * this.zoomLevel > this.$refs.canvasContainer.clientWidth;\n    }\n  },\n  created: function created() {\n    // 从URL参数或localStorage获取drawingId\n    var drawingId = this.$route.query.drawingId || localStorage.getItem('currentDrawingId');\n\n    // 如果有drawingId，保存到组件状态和localStorage\n    if (drawingId) {\n      this.currentDrawingId = drawingId;\n      localStorage.setItem('currentDrawingId', drawingId);\n\n      // 如果URL中没有drawingId但localStorage有，则更新URL\n      if (!this.$route.query.drawingId) {\n        this.$router.replace({\n          query: _objectSpread(_objectSpread({}, this.$route.query), {}, {\n            drawingId: drawingId\n          })\n        });\n      }\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 移除窗口大小变化监听器\n    window.removeEventListener('resize', this.checkScrollNeeded);\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n    // 添加窗口大小变化监听器\n    window.addEventListener('resize', this.checkScrollNeeded);\n\n    // 获取drawingId并加载数据\n    var drawingId = this.currentDrawingId || this.$route.query.drawingId;\n    if (drawingId) {\n      this.getList();\n    } else {\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\n      setTimeout(function () {\n        _this2.goBack();\n      }, 2000);\n    }\n  },\n  methods: {\n    getList: function getList() {\n      var _this3 = this;\n      // 优先使用组件状态中的drawingId，其次是URL参数\n      var drawingId = this.currentDrawingId || this.$route.query.drawingId;\n      if (!drawingId) {\n        this.$message.warning('缺少图纸ID参数');\n        return;\n      }\n      this.$api['areaManagement/maintenanceDrawingManagement-get']({\n        drawingId: drawingId\n      }).then(function (data) {\n        console.log(data, 'data');\n        if (data) {\n          // 获取base64图片数据\n          var drawingData = data;\n          if (drawingData.drawingInfo) {\n            // 设置图片路径为base64数据\n            _this3.imgPath = drawingData.drawingInfo;\n            // 图纸基本信息\n            _this3.imgUrl = drawingData.drawingInfo;\n\n            // 初始化画布，渲染图片\n            _this3.init();\n\n            // 图片渲染完成后，获取已有区域数据\n            _this3.getRegionList();\n          } else {\n            _this3.$message.error('图片数据不存在');\n          }\n        }\n      }).catch(function (error) {\n        console.error('获取图纸数据失败:', error);\n        _this3.$message.error('获取图纸数据失败');\n      });\n    },\n    // 获取已有区域数据\n    getRegionList: function getRegionList() {\n      var _this4 = this;\n      // 优先使用组件状态中的drawingId，其次是URL参数\n      var drawingId = this.currentDrawingId || this.$route.query.drawingId;\n      if (!drawingId) {\n        return;\n      }\n      this.$api['areaManagement/maintenanceRegion-list']({\n        drawingId: drawingId\n      }).then(function (data) {\n        if (data && data.length > 0) {\n          // 将区域信息转换为需要的格式，并保存原始数据中的ID\n          _this4.roomNameList = data.map(function (region) {\n            return {\n              roomName: region.regionName,\n              roomType: '',\n              remark: region.remark || '',\n              id: region.id || null // 保存原始区域ID，用于后续删除操作\n            };\n          });\n\n          // 解析区域坐标数据\n          var areaCoordinates = data.map(function (region) {\n            try {\n              return JSON.parse(region.pointJson || '[]');\n            } catch (e) {\n              console.error('Error parsing pointJson:', e);\n              return [];\n            }\n          }).filter(function (coords) {\n            return coords.length > 0;\n          });\n\n          // 保存相对坐标数据\n          _this4.finalArrRelative = _toConsumableArray(areaCoordinates);\n\n          // 处理区域数据并绘制到画布上\n          if (areaCoordinates.length > 0) {\n            // 转换成绝对坐标并保存\n            _this4.finalArr = _this4.processAreaData(areaCoordinates);\n\n            // 绘制所有区域\n            _this4.redrawCanvas();\n          }\n        }\n      }).catch(function (error) {\n        console.error('获取区域数据失败:', error);\n      });\n    },\n    goBack: function goBack() {\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\n    },\n    editAreaByIndex: function editAreaByIndex(index) {},\n    handleFileChange: function handleFileChange(file) {\n      var _this5 = this;\n      var rawFile = file.raw;\n      if (!rawFile) {\n        return;\n      }\n      var reader = new FileReader();\n      reader.onload = function (e) {\n        _this5.imgPath = e.target.result;\n        _this5.spinShow = true;\n        _this5.init();\n      };\n      reader.readAsDataURL(rawFile);\n    },\n    // 检查是否需要滚动按钮\n    checkScrollNeeded: function checkScrollNeeded() {\n      if (this.$refs.canvasContainer && this.canvas) {\n        // 通过Vue的响应式系统触发计算属性重新计算\n        this.$forceUpdate();\n        // 不再需要调整容器高度\n        // this.adjustContainerHeight();\n      }\n    },\n    // 不再需要调整容器高度的方法\n    /*\r\n    adjustContainerHeight() {\r\n    if (this.$refs.canvasContainer && this.canvas) {\r\n      const canvasHeight = this.canvas.height * this.zoomLevel;\r\n      // 设置容器高度为画布高度加上一些边距\r\n      this.$refs.canvasContainer.style.height = `${canvasHeight + 20}px`;\r\n    }\r\n    },\r\n    */\n    // 向左滚动画布\n    scrollLeft: function scrollLeft() {\n      if (this.$refs.canvasContainer) {\n        var container = this.$refs.canvasContainer;\n        var scrollAmount = Math.min(300, container.clientWidth / 2);\n        container.scrollLeft -= scrollAmount;\n      }\n    },\n    // 向右滚动画布\n    scrollRight: function scrollRight() {\n      if (this.$refs.canvasContainer) {\n        var container = this.$refs.canvasContainer;\n        var scrollAmount = Math.min(300, container.clientWidth / 2);\n        container.scrollLeft += scrollAmount;\n      }\n    },\n    init: function init() {\n      var _this = this;\n      var image = new Image();\n      image.setAttribute('crossOrigin', 'anonymous');\n      image.src = this.imgPath;\n      image.onload = function () {\n        // 图片加载完，再draw 和 toDataURL\n        if (image.complete) {\n          _this.spinShow = false;\n          _this.img = image;\n          var content = _this.$refs.content;\n          _this.canvas = document.createElement('canvas');\n          _this.canvas.height = _this.img.height;\n          _this.canvas.width = _this.img.width;\n          _this.canvas.setAttribute('style', 'border:2px solid red;');\n          _this.canvas.setAttribute('id', 'myCanvas');\n          _this.ctx = _this.canvas.getContext('2d');\n          _this.ctx.globalAlpha = 1;\n          _this.ctx.drawImage(_this.img, 0, 0);\n          _this.canvasHistory.push(_this.canvas.toDataURL());\n          _this.ctx.globalCompositeOperation = _this.type;\n\n          // 清空之前的内容\n          content.innerHTML = '';\n          content.appendChild(_this.canvas);\n\n          // 重置交互状态\n          _this.deleteMode = false;\n          _this.hoveredAreaIndex = -1;\n\n          // 预设最小缩放比例，防止图片太小\n          _this.defaultZoomFactor = Math.max(_this.defaultZoomFactor, 1.2);\n\n          // 自动适应屏幕\n          _this.$nextTick(function () {\n            _this.autoFit();\n            // 不再需要调整容器高度\n            // _this.adjustContainerHeight();\n            // 检查是否需要滚动按钮\n            _this.checkScrollNeeded();\n          });\n          _this.bindEventLisner();\n          if (_this.areaArr) {\n            _this.finalArr = _this.processAreaData(_this.areaArr);\n            _this.finalArr.forEach(function (i) {\n              _this.createL2(i);\n            });\n          }\n        }\n      };\n    },\n    radioClick: function radioClick(item) {\n      if (item != 'T') {\n        this.txtBlue();\n        this.resetTxt();\n      }\n    },\n    // 下载画布\n    downLoad: function downLoad() {\n      var _this = this;\n      var url = _this.canvas.toDataURL('image/png');\n      var fileName = 'canvas.png';\n      if ('download' in document.createElement('a')) {\n        // 非IE下载\n        var elink = document.createElement('a');\n        elink.download = fileName;\n        elink.style.display = 'none';\n        elink.href = url;\n        document.body.appendChild(elink);\n        elink.click();\n        document.body.removeChild(elink);\n      } else {\n        // IE10+下载\n        navigator.msSaveBlob(url, fileName);\n      }\n    },\n    // 重置所有内容\n    resetAll: function resetAll() {\n      this.dataArr = [];\n      this.finalArr = [];\n      this.finalArrRelative = []; // 同时清空相对坐标数组\n      this.roomNameList = [];\n      this.$emit('getPointArr', this.finalArr);\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n      this.canvasHistory = [];\n      this.ctx.drawImage(this.img, 0, 0);\n      this.canvasHistory.push(this.canvas.toDataURL());\n      this.step = 0;\n      this.resetTxt();\n      this.hoveredAreaIndex = -1;\n      this.deleteMode = false;\n      // 应用缩放\n      this.applyZoom();\n    },\n    // 保存区域规划\n    save: function save() {\n      // this.finalArr\n    },\n    // 清空当前画布\n    reset: function reset() {\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n      this.ctx.drawImage(this.img, 0, 0);\n      this.resetTxt();\n    },\n    // 撤销方法\n    repeal: function repeal() {\n      var _this = this;\n      if (this.isShow) {\n        _this.resetTxt();\n        _this._repeal();\n      } else {\n        _this._repeal();\n      }\n    },\n    _repeal: function _repeal() {\n      var _this6 = this;\n      if (this.step >= 1) {\n        this.step = this.step - 1;\n        var canvasPic = new Image();\n        canvasPic.src = this.canvasHistory[this.step];\n        canvasPic.addEventListener('load', function () {\n          _this6.ctx.clearRect(0, 0, _this6.canvas.width, _this6.canvas.height);\n          _this6.ctx.drawImage(canvasPic, 0, 0);\n          _this6.loading = true;\n        });\n      } else {\n        this.$message.warning('不能再继续撤销了');\n      }\n    },\n    // 恢复方法\n    canvasRedo: function canvasRedo() {\n      var _this7 = this;\n      if (this.step < this.canvasHistory.length - 1) {\n        if (this.step == 0) {\n          this.step = 1;\n        } else {\n          this.step++;\n        }\n        var canvasPic = new Image();\n        canvasPic.src = this.canvasHistory[this.step];\n        canvasPic.addEventListener('load', function () {\n          _this7.ctx.clearRect(0, 0, _this7.canvas.width, _this7.canvas.height);\n          _this7.ctx.drawImage(canvasPic, 0, 0);\n        });\n      } else {\n        this.$message.warning('已经是最新的记录了');\n      }\n    },\n    // 绘制历史数组中的最后一个\n    rebroadcast: function rebroadcast() {\n      var _this8 = this;\n      var canvasPic = new Image();\n      canvasPic.src = this.canvasHistory[this.step];\n      canvasPic.addEventListener('load', function () {\n        _this8.ctx.clearRect(0, 0, _this8.canvas.width, _this8.canvas.height);\n        _this8.ctx.drawImage(canvasPic, 0, 0);\n        _this8.loading = true;\n      });\n    },\n    // 绑定事件,判断分支\n    bindEventLisner: function bindEventLisner() {\n      var _this = this;\n      var r1, r2; // 绘制圆形，矩形需要\n      this.canvas.addEventListener('click', function (e) {\n        if (_this.type == 'L' && e.button == 0) {\n          var coords = _this.getAdjustedCoordinates(e);\n          // 如果处于删除模式，检查是否点击在区域内\n          if (_this.deleteMode) {\n            _this.checkAndDeleteArea(coords);\n          } else {\n            _this.createL(coords, 'begin');\n          }\n        }\n      });\n      this.canvas.oncontextmenu = function (e) {\n        var coords = _this.getAdjustedCoordinates(e);\n        _this.createL(coords, 'end');\n        return false;\n      };\n\n      // 添加鼠标移动事件监听器，用于区域高亮，使用防抖处理\n      this.canvas.addEventListener('mousemove', function (e) {\n        // 清除之前的定时器\n        if (_this.debounceTimer) {\n          clearTimeout(_this.debounceTimer);\n        }\n\n        // 创建新的定时器，延迟执行高亮逻辑\n        _this.debounceTimer = setTimeout(function () {\n          if (_this.finalArr && _this.finalArr.length > 0) {\n            var coords = _this.getAdjustedCoordinates(e);\n            var isOverArea = false;\n            var hoveredIndex = -1;\n\n            // 检查鼠标是否在任何一个区域内\n            for (var i = 0; i < _this.finalArr.length; i++) {\n              if (_this.isPointInPolygon(coords, _this.finalArr[i])) {\n                hoveredIndex = i;\n                isOverArea = true;\n                break;\n              }\n            }\n\n            // 只有当悬停状态发生变化时才重绘\n            if (_this.hoveredAreaIndex !== hoveredIndex) {\n              _this.hoveredAreaIndex = hoveredIndex;\n              _this.redrawWithHighlight();\n              document.body.style.cursor = isOverArea ? 'pointer' : 'default';\n            }\n          }\n        }, 10); // 10毫秒的防抖延迟，平衡响应性和性能\n      });\n\n      // 添加鼠标离开画布事件\n      this.canvas.addEventListener('mouseleave', function () {\n        if (_this.debounceTimer) {\n          clearTimeout(_this.debounceTimer);\n        }\n        if (_this.hoveredAreaIndex !== -1) {\n          _this.hoveredAreaIndex = -1;\n          _this.redrawCanvas();\n          document.body.style.cursor = 'default';\n        }\n      });\n    },\n    // 判断点是否在多边形内（原有方法，保留但不使用）\n    judge: function judge(dot, coordinates) {\n      return this.isPointInPolygon(dot, coordinates);\n    },\n    // 绘制线条\n    createL: function createL(coords, status) {\n      var _this = this;\n      if (status == 'begin') {\n        // 点击开始绘制前，检查点击位置是否在已有区域内\n        var mousePoint = {\n          x: coords.x,\n          y: coords.y\n        };\n        for (var i = 0; i < this.finalArr.length; i++) {\n          if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\n            this.$message.warning('您点击的位置已在其他区域内，请选择其他位置绘制');\n            return; // 阻止继续执行\n          }\n        }\n        if (_this.dataArr && _this.dataArr.length === 0) {\n          _this.dataArr.push({\n            x: coords.x,\n            y: coords.y\n          });\n          _this.ctx.beginPath();\n          _this.ctx.moveTo(coords.x, coords.y);\n          _this.ctx.lineTo(coords.x + 1, coords.y + 1);\n          _this.ctx.strokeStyle = _this.strokeStyle;\n          _this.ctx.lineWidth = _this.lineWidth;\n          _this.ctx.stroke();\n        } else if (_this.dataArr && _this.dataArr.length !== 0) {\n          _this.dataArr.push({\n            x: coords.x,\n            y: coords.y\n          });\n          _this.ctx.lineTo(coords.x, coords.y);\n          _this.ctx.strokeStyle = _this.strokeStyle;\n          _this.ctx.lineWidth = _this.lineWidth;\n          _this.ctx.stroke();\n        }\n      } else if (status == 'end') {\n        if (_this.dataArr && _this.dataArr.length !== 0) {\n          _this.ctx.moveTo(_this.dataArr[_this.dataArr.length - 1].x, _this.dataArr[_this.dataArr.length - 1].y);\n          _this.ctx.lineTo(_this.dataArr[0].x, _this.dataArr[0].y);\n          _this.ctx.stroke();\n          _this.ctx.closePath();\n          _this.step = _this.step + 1;\n          if (_this.step < _this.canvasHistory.length - 1) {\n            _this.canvasHistory.length = _this.step;\n          }\n          _this.canvasHistory.push(_this.canvas.toDataURL());\n          if (_this.dataArr && _this.dataArr.length < 3) {\n            _this.$message.info('该区域点数少于三个，不保存');\n            _this._repeal();\n          } else {\n            // 检查新绘制的区域是否与现有区域有重叠\n            var newArea = _this.dataArr;\n            var hasOverlap = false;\n            for (var _i2 = 0; _i2 < _this.finalArr.length; _i2++) {\n              if (_this.checkPolygonsOverlap(newArea, _this.finalArr[_i2])) {\n                hasOverlap = true;\n                break;\n              }\n            }\n            if (hasOverlap) {\n              _this.$message.warning('新绘制的区域与现有区域重叠，请重新绘制');\n              _this._repeal();\n            } else {\n              // 存储新绘制的区域数据，等待表单提交后再保存\n              _this.tempAreaData = {\n                area: _this.dataArr,\n                relativeArea: _this.dataArr.map(function (point) {\n                  return _this.toRelativeCoordinates(point);\n                })\n              };\n\n              // 打开区域信息表单\n              _this.areaForm = {\n                roomName: \"\\u533A\\u57DF\".concat(_this.roomNameList.length + 1),\n                roomType: '',\n                remark: ''\n              };\n              _this.areaFormVisible = true;\n            }\n          }\n          this.$emit('getPointArr', _this.finalArr);\n          _this.dataArr = [];\n          _this.canvas.onmousemove = null;\n        }\n      }\n    },\n    // 重绘画布并高亮显示悬停区域\n    redrawWithHighlight: function redrawWithHighlight() {\n      // 清空画布\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\n      // 重绘背景图\n      this.ctx.drawImage(this.img, 0, 0);\n\n      // 重绘所有区域\n      for (var i = 0; i < this.finalArr.length; i++) {\n        this.drawArea(this.finalArr[i], i === this.hoveredAreaIndex);\n      }\n    },\n    // 统一的区域绘制函数\n    drawArea: function drawArea(area) {\n      var isHighlighted = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      if (!area || area.length < 3) {\n        return;\n      }\n      this.ctx.beginPath();\n      this.ctx.moveTo(area[0].x, area[0].y);\n\n      // 绘制区域轮廓\n      for (var i = 1; i < area.length; i++) {\n        this.ctx.lineTo(area[i].x, area[i].y);\n      }\n\n      // 闭合路径\n      this.ctx.lineTo(area[0].x, area[0].y);\n\n      // 设置样式\n      if (isHighlighted) {\n        // 高亮显示 - 增加线宽，使用更深的蓝色\n        this.ctx.strokeStyle = this.highlightColor;\n        this.ctx.lineWidth = this.lineWidth + 2;\n\n        // 填充半透明颜色\n        this.ctx.fillStyle = this.highlightColor + '22'; // 添加22作为透明度 (13%)\n        this.ctx.fill();\n      } else {\n        this.ctx.strokeStyle = this.strokeStyle;\n        this.ctx.lineWidth = this.lineWidth;\n      }\n      this.ctx.stroke();\n      this.ctx.closePath();\n\n      // 如果启用了显示区域名称，并且能找到对应的名\n      var areaIndex = this.finalArr.indexOf(area);\n      if (this.showAreaNames && areaIndex >= 0 && this.roomNameList[areaIndex]) {\n        this.drawAreaName(area, this.roomNameList[areaIndex].roomName);\n      }\n    },\n    // 绘制区域名称\n    drawAreaName: function drawAreaName(area, name) {\n      if (!area || area.length < 3 || !name) {\n        return;\n      }\n\n      // 计算区域的中心点\n      var centerX = 0,\n        centerY = 0;\n      for (var i = 0; i < area.length; i++) {\n        centerX += area[i].x;\n        centerY += area[i].y;\n      }\n      centerX /= area.length;\n      centerY /= area.length;\n\n      // 保存当前上下文状态\n      this.ctx.save();\n\n      // 设置固定的字体大小，不受图片尺寸和缩放影响\n      // 首先获取画布的尺寸\n      var canvasWidth = this.canvas.width;\n      var canvasHeight = this.canvas.height;\n\n      // 计算固定的字体大小，基于画布尺寸的一个合适比例\n      // 这样可以确保在不同大小的图片上，字体大小相对于图片尺寸的比例是一致的\n      var baseFontSize = 36; // 固定基础字体大小\n\n      // 应用字体设置\n      this.ctx.font = \"bold \".concat(baseFontSize, \"px \").concat(this.fontFamily);\n      this.ctx.textAlign = 'center';\n      this.ctx.textBaseline = 'middle';\n\n      // 测量文本宽度\n      var textWidth = this.ctx.measureText(name).width;\n\n      // 增强文字阴影以提高可读性\n      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';\n      this.ctx.shadowBlur = 4;\n      this.ctx.shadowOffsetX = 1;\n      this.ctx.shadowOffsetY = 1;\n\n      // 使用更亮的蓝色，但与线条颜色(#1E90FF)有区别\n      this.ctx.fillStyle = '#38B0DE'; // 天蓝色，比线条颜色略微偏青一些\n      this.ctx.fillText(name, centerX, centerY);\n\n      // 恢复上下文状态\n      this.ctx.restore();\n    },\n    // 绘制圆角矩形\n    roundRect: function roundRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {\n      if (typeof radius === 'undefined') {\n        radius = 5;\n      }\n      if (typeof radius === 'number') {\n        radius = {\n          tl: radius,\n          tr: radius,\n          br: radius,\n          bl: radius\n        };\n      } else {\n        var defaultRadius = {\n          tl: 0,\n          tr: 0,\n          br: 0,\n          bl: 0\n        };\n        for (var side in defaultRadius) {\n          radius[side] = radius[side] || defaultRadius[side];\n        }\n      }\n      ctx.beginPath();\n      ctx.moveTo(x + radius.tl, y);\n      ctx.lineTo(x + width - radius.tr, y);\n      ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);\n      ctx.lineTo(x + width, y + height - radius.br);\n      ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);\n      ctx.lineTo(x + radius.bl, y + height);\n      ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);\n      ctx.lineTo(x, y + radius.tl);\n      ctx.quadraticCurveTo(x, y, x + radius.tl, y);\n      ctx.closePath();\n      if (fillColor) {\n        ctx.fillStyle = fillColor;\n        ctx.fill();\n      }\n      if (strokeColor) {\n        ctx.strokeStyle = strokeColor;\n        ctx.lineWidth = 1;\n        ctx.stroke();\n      }\n    },\n    // 重绘画布\n    redrawCanvas: function redrawCanvas() {\n      if (!this.canvas || !this.ctx) {\n        return;\n      }\n\n      // 清空画布\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\n      // 绘制背景图\n      this.ctx.drawImage(this.img, 0, 0);\n\n      // 重绘所有保留的区域\n      for (var i = 0; i < this.finalArr.length; i++) {\n        this.drawArea(this.finalArr[i]);\n      }\n    },\n    // 创建区域2（读取已有区域）\n    createL2: function createL2(e) {\n      var _this = this;\n\n      // 使用统一的绘制函数\n      this.drawArea(e);\n      _this.step = _this.step + 1;\n      if (_this.step < _this.canvasHistory.length - 1) {\n        _this.canvasHistory.length = _this.step;\n      }\n      _this.canvasHistory.push(_this.canvas.toDataURL());\n      this.$emit('getPointArr', _this.finalArr);\n    },\n    // 绘制矩形\n    createR: function createR(e, status, r1, r2) {\n      var _this = this;\n      var r;\n      if (status == 'begin') {\n        // console.log('onmousemove')\n        _this.canvas.onmousemove = function (e) {\n          _this.reset();\n          var rx = e.layerX - r1;\n          var ry = e.layerY - r2;\n\n          // 保留之前绘画的图形\n          if (_this.step !== 0) {\n            var canvasPic = new Image();\n            canvasPic.src = _this.canvasHistory[_this.step];\n            _this.ctx.drawImage(canvasPic, 0, 0);\n          }\n          _this.ctx.beginPath();\n          _this.ctx.strokeRect(r1, r2, rx, ry);\n          _this.ctx.strokeStyle = _this.strokeStyle;\n          _this.ctx.lineWidth = _this.lineWidth;\n          _this.ctx.closePath();\n          _this.ctx.stroke();\n        };\n      } else if (status == 'end') {\n        _this.rebroadcast();\n        var interval = setInterval(function () {\n          if (_this.loading) {\n            clearInterval(interval);\n            _this.loading = false;\n          } else {\n            return;\n          }\n          var rx = e.layerX - r1;\n          var ry = e.layerY - r2;\n          _this.ctx.beginPath();\n          _this.ctx.rect(r1, r2, rx, ry);\n          _this.ctx.strokeStyle = _this.strokeStyle;\n          _this.ctx.lineWidth = _this.lineWidth;\n          _this.ctx.closePath();\n          _this.ctx.stroke();\n          _this.step = _this.step + 1;\n          if (_this.step < _this.canvasHistory.length - 1) {\n            _this.canvasHistory.length = _this.step; // 截断数组\n          }\n          _this.canvasHistory.push(_this.canvas.toDataURL());\n          _this.canvas.onmousemove = null;\n        }, 1);\n      }\n    },\n    // 绘制箭头\n    drawArrow: function drawArrow(e, status) {\n      var _this = this;\n      if (status == 'begin') {\n        // 获取起始位置\n        _this.arrowFromX = e.layerX;\n        _this.arrowFromY = e.layerY;\n        _this.ctx.beginPath();\n        _this.ctx.moveTo(e.layerX, e.layerY);\n      } else if (status == 'end') {\n        // 计算箭头及画线\n        var toX = e.layerX;\n        var toY = e.layerY;\n        var theta = 30;\n        var headlen = 10;\n        var _this9 = this;\n        var fromX = this.arrowFromX;\n        var fromY = this.arrowFromY;\n        // 计算各角度和对应的P2,P3坐标\n        var angle = Math.atan2(fromY - toY, fromX - toX) * 180 / Math.PI;\n        var angle1 = (angle + theta) * Math.PI / 180;\n        var angle2 = (angle - theta) * Math.PI / 180;\n        var topX = headlen * Math.cos(angle1);\n        var topY = headlen * Math.sin(angle1);\n        var botX = headlen * Math.cos(angle2);\n        var botY = headlen * Math.sin(angle2);\n        var arrowX = fromX - topX;\n        var arrowY = fromY - topY;\n        _this9.ctx.moveTo(arrowX, arrowY);\n        _this9.ctx.moveTo(fromX, fromY);\n        _this9.ctx.lineTo(toX, toY);\n        arrowX = toX + topX;\n        arrowY = toY + topY;\n        _this9.ctx.moveTo(arrowX, arrowY);\n        _this9.ctx.lineTo(toX, toY);\n        arrowX = toX + botX;\n        arrowY = toY + botY;\n        _this9.ctx.lineTo(arrowX, arrowY);\n        _this9.ctx.strokeStyle = _this9.strokeStyle;\n        _this9.ctx.lineWidth = _this9.lineWidth;\n        _this9.ctx.stroke();\n        _this9.ctx.closePath();\n        _this9.step = _this9.step + 1;\n        if (_this9.step < _this9.canvasHistory.length - 1) {\n          _this9.canvasHistory.length = _this9.step; // 截断数组\n        }\n        _this9.canvasHistory.push(_this9.canvas.toDataURL());\n        _this9.canvas.onmousemove = null;\n      }\n    },\n    // 文字输入\n    createT: function createT(e, status) {\n      var _this = this;\n      if (status == 'begin') {\n        // 初始化文字输入相关参数\n        _this.isTextInputMode = true;\n      } else if (status == 'end') {\n        var offset = 0;\n        if (_this.fontSize >= 28) {\n          offset = _this.fontSize / 2 - 3;\n        } else {\n          offset = _this.fontSize / 2 - 2;\n        }\n        _this.ctxX = e.layerX + 2;\n        _this.ctxY = e.layerY + offset;\n        var index = this.getPointOnCanvas(e);\n        _this.$refs.txt.style.left = index.x + 'px';\n        _this.$refs.txt.style.top = index.y - _this.fontSize / 2 + 'px';\n        _this.$refs.txt.value = '';\n        _this.$refs.txt.style.height = _this.fontSize + 'px';\n        _this.$refs.txt.style.width = _this.canvas.width - e.layerX - 1 + 'px', _this.$refs.txt.style.fontSize = _this.fontSize + 'px';\n        _this.$refs.txt.style.fontFamily = _this.fontFamily;\n        _this.$refs.txt.style.color = _this.fontColor;\n        _this.$refs.txt.style.maxlength = Math.floor((_this.canvas.width - e.layerX) / _this.fontSize);\n        _this.isShow = true;\n        setTimeout(function () {\n          _this.$refs.txt.focus();\n        });\n      }\n    },\n    // 文字输入框失去光标时在画布上生成文字\n    txtBlue: function txtBlue() {\n      var _this = this;\n      var txt = _this.$refs.txt.value;\n      if (txt) {\n        _this.ctx.font = _this.$refs.txt.style.fontSize + ' ' + _this.$refs.txt.style.fontFamily;\n        _this.ctx.fillStyle = _this.$refs.txt.style.color;\n        _this.ctx.fillText(txt, _this.ctxX, _this.ctxY);\n        _this.step = _this.step + 1;\n        if (_this.step < _this.canvasHistory.length - 1) {\n          _this.canvasHistory.length = _this.step; // 截断数组\n        }\n        _this.canvasHistory.push(_this.canvas.toDataURL());\n        _this.canvas.onmousemove = null;\n      }\n    },\n    // 计算文字框定位位置\n    getPointOnCanvas: function getPointOnCanvas(e) {\n      var cs = this.canvas;\n      var content = document.getElementsByClassName('content')[0];\n      return {\n        x: e.layerX + (content.clientWidth - cs.width) / 2,\n        y: e.layerY\n      };\n    },\n    // 清空文字\n    resetTxt: function resetTxt() {\n      var _this = this;\n      _this.$refs.txt.value = '';\n      _this.isShow = false;\n    },\n    exportJson: function exportJson() {\n      var _this10 = this;\n      // 直接使用已经准备好的相对坐标数组\n      var exportArr = JSON.parse(JSON.stringify(this.finalArrRelative));\n\n      // 获取URL中的drawingId参数\n      var drawingId = this.currentDrawingId || this.$route.query.drawingId;\n\n      // 准备导出数据，使用正确的字段名\n      var maintenanceRegionList = this.roomNameList.map(function (room, index) {\n        return {\n          id: room.id || null,\n          regionName: room.roomName,\n          // 使用regionName作为区域名称字段\n          pointJson: JSON.stringify(exportArr[index]),\n          // 使用pointJson作为区域坐标JSON字段\n          remark: room.remark || '',\n          // 备注字段\n          drawingId: drawingId // 从URL参数获取的图纸ID\n        };\n      });\n\n      // 如果没有区域数据，提示用户\n      if (maintenanceRegionList.length === 0) {\n        this.$message.warning('尚未绘制任何区域，无法保存');\n        return;\n      }\n\n      // 二次确认保存操作\n      this.$confirm('确认保存当前绘制的所有区域？', '保存确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(function () {\n        // 用户确认，调用保存接口\n        console.log('保存的区域数据:', maintenanceRegionList);\n\n        // 调用保存接口，传入整合后的区域列表\n        _this10.$api['areaManagement/maintenanceRegion-save']({\n          maintenanceRegionList: maintenanceRegionList\n        }).then(function (data) {\n          _this10.$message.success('保存成功');\n        }).catch(function (error) {\n          _this10.$message.error('保存失败，请重试');\n        });\n      });\n    },\n    ToBase64: function ToBase64() {\n      var that = this;\n      var img = document.getElementById('imgfile');\n      var imgFile = new FileReader();\n      imgFile.readAsDataURL(img.files[0]);\n      imgFile.onload = function () {\n        var imgData = this.result; // base64数据\n        that.imgPath = imgData;\n        that.init();\n      };\n    },\n    handleMultiUpload: function handleMultiUpload(v) {\n      this.imgUrl = v[0];\n    },\n    handleMultiUpload2: function handleMultiUpload2(v) {\n      this.imgForm.imgUrl = v[0];\n    },\n    changeImgUrl: function changeImgUrl() {\n      this.imgForm = {\n        floorNo: '',\n        imgUrl: ''\n      };\n      this.imgModal = true;\n    },\n    imgOk: function imgOk() {\n      var _this11 = this;\n      this.$refs.imgForm.validate(function (valid) {\n        if (valid) {\n          _this11.imgOkLoading = true;\n          _this11.$api['iotHome/changeFloorMap'](_this11.imgForm).then(function (data) {\n            _this11.$message.success('更换成功');\n            _this11.imgModal = false;\n          }).finally(function () {\n            _this11.imgOkLoading = false;\n          });\n        }\n      });\n    },\n    imgCancel: function imgCancel() {\n      // 取消关闭\n      this.imgModal = false;\n      this.$refs.imgForm.resetFields();\n    },\n    // 将绝对坐标转换为相对坐标（0-1之间的比例值）\n    toRelativeCoordinates: function toRelativeCoordinates(point) {\n      if (!this.canvas) {\n        return point;\n      }\n      return {\n        x: parseFloat((point.x / this.canvas.width).toFixed(4)),\n        y: parseFloat((point.y / this.canvas.height).toFixed(4))\n      };\n    },\n    // 将相对坐标转换为绝对坐标（实际像素值）\n    toAbsoluteCoordinates: function toAbsoluteCoordinates(point) {\n      if (!this.canvas) {\n        return point;\n      }\n      return {\n        x: Math.round(point.x * this.canvas.width),\n        y: Math.round(point.y * this.canvas.height)\n      };\n    },\n    // 判断一个点是否为相对坐标（值在0-1之间）\n    isRelativeCoordinate: function isRelativeCoordinate(point) {\n      return point.x >= 0 && point.x <= 1 && point.y >= 0 && point.y <= 1;\n    },\n    // 确保点使用绝对坐标\n    ensureAbsoluteCoordinate: function ensureAbsoluteCoordinate(point) {\n      if (this.isRelativeCoordinate(point)) {\n        return this.toAbsoluteCoordinates(point);\n      }\n      return point;\n    },\n    // 确保点使用相对坐标\n    ensureRelativeCoordinate: function ensureRelativeCoordinate(point) {\n      if (!this.isRelativeCoordinate(point)) {\n        return this.toRelativeCoordinates(point);\n      }\n      return point;\n    },\n    // 处理输入的区域数据，确保使用正确的坐标类型\n    processAreaData: function processAreaData(areaData) {\n      var _this12 = this;\n      if (!areaData || !areaData.length) {\n        return [];\n      }\n\n      // 深拷贝避免修改原始数据\n      var processedData = JSON.parse(JSON.stringify(areaData));\n\n      // 检查第一个点的第一个坐标，判断是否为相对坐标\n      var firstArea = processedData[0];\n      if (firstArea && firstArea.length > 0) {\n        var firstPoint = firstArea[0];\n        var isRelative = this.isRelativeCoordinate(firstPoint);\n\n        // 如果是相对坐标，转换为绝对坐标用于绘制\n        if (isRelative) {\n          // 同时保存相对坐标版本\n          this.finalArrRelative = JSON.parse(JSON.stringify(processedData));\n          processedData.forEach(function (area) {\n            area.forEach(function (point, index) {\n              area[index] = _this12.toAbsoluteCoordinates(point);\n            });\n          });\n        } else {\n          // 如果是绝对坐标，生成相对坐标版本\n          this.finalArrRelative = processedData.map(function (area) {\n            return area.map(function (point) {\n              return _this12.toRelativeCoordinates(point);\n            });\n          });\n        }\n      }\n      return processedData;\n    },\n    deleteAllAreas: function deleteAllAreas() {\n      var _this13 = this;\n      if (this.finalArr.length > 0) {\n        // 二次确认删除所有区域操作，强调可能影响已绑定设备\n        this.$confirm('警告：删除所有区域可能会影响已绑定的设备！确定要删除吗？', '批量删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: true,\n          message: \"<div>\\n            <p><strong style=\\\"color: red;\\\">\\u4E25\\u91CD\\u8B66\\u544A\\uFF1A</strong>\\u5220\\u9664\\u6240\\u6709\\u533A\\u57DF\\u5C06\\u4F1A\\uFF1A</p>\\n            <ul>\\n              <li>\\u6C38\\u4E45\\u79FB\\u9664\\u6240\\u6709\\u533A\\u57DF\\u7684\\u7ED8\\u5236\\u4FE1\\u606F</li>\\n              <li>\\u5BFC\\u81F4\\u6240\\u6709\\u4E0E\\u8FD9\\u4E9B\\u533A\\u57DF\\u5173\\u8054\\u7684\\u8BBE\\u5907\\u5931\\u53BB\\u4F4D\\u7F6E\\u4FE1\\u606F</li>\\n              <li>\\u9700\\u8981\\u91CD\\u65B0\\u7ED8\\u5236\\u533A\\u57DF\\u5E76\\u91CD\\u65B0\\u7ED1\\u5B9A\\u6240\\u6709\\u8BBE\\u5907</li>\\n            </ul>\\n            <p>\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF0C\\u786E\\u5B9A\\u8981\\u7EE7\\u7EED\\u5220\\u9664\\u6240\\u6709\\u533A\\u57DF\\u5417\\uFF1F</p>\\n          </div>\"\n        }).then(function () {\n          // 收集所有区域的ID\n          var regionIds = _this13.roomNameList.filter(function (room) {\n            return room.id;\n          }) // 只收集有ID的区域\n          .map(function (room) {\n            return room.id;\n          });\n          if (regionIds.length > 0) {\n            // 有保存过的区域，调用API删除\n            _this13.$api['areaManagement/maintenanceRegion-batch-delete']({\n              ids: regionIds\n            }).then(function (data) {\n              _this13.$message.success('已删除所有区域');\n\n              // 清空前端数据\n              _this13.finalArr = [];\n              _this13.finalArrRelative = [];\n              _this13.roomNameList = [];\n\n              // 重绘画布\n              _this13.redrawCanvas();\n\n              // 通知父组件区域变化\n              _this13.$emit('getPointArr', _this13.finalArr);\n            }).catch(function (error) {\n              _this13.$message.error('删除失败，请重试');\n              console.error('批量删除区域失败:', error);\n            });\n          } else {\n            // 没有保存过的区域，直接清空前端数据\n            _this13.finalArr = [];\n            _this13.finalArrRelative = [];\n            _this13.roomNameList = [];\n\n            // 重绘画布\n            _this13.redrawCanvas();\n\n            // 通知父组件区域变化\n            _this13.$emit('getPointArr', _this13.finalArr);\n            _this13.$message.success('已删除所有区域');\n          }\n        });\n      } else {\n        this.$message.info('没有可删除的区域');\n      }\n    },\n    // 检查并删除点击的区域\n    checkAndDeleteArea: function checkAndDeleteArea(coords) {\n      var _this14 = this;\n      var mousePoint = {\n        x: coords.x,\n        y: coords.y\n      };\n\n      // 检查点击是否在任何一个区域内\n      var _loop = function _loop(i) {\n        if (_this14.isPointInPolygon(mousePoint, _this14.finalArr[i])) {\n          // 弹出确认对话框\n          _this14.$confirm(\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u7B2C\".concat(i + 1, \"\\u4E2A\\u533A\\u57DF\\u5417?\"), '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(function () {\n            // 删除该区域\n            _this14.finalArr.splice(i, 1);\n            _this14.finalArrRelative.splice(i, 1);\n\n            // 如果有对应的名称，也要删除\n            if (_this14.roomNameList.length > i) {\n              _this14.roomNameList.splice(i, 1);\n            }\n\n            // 重绘画布\n            _this14.hoveredAreaIndex = -1;\n            _this14.redrawCanvas();\n\n            // 通知父组件区域变化\n            _this14.$emit('getPointArr', _this14.finalArr);\n            _this14.$message.success(\"\\u5DF2\\u5220\\u9664\\u7B2C\".concat(i + 1, \"\\u4E2A\\u533A\\u57DF\"));\n          });\n          return 1; // break\n        }\n      };\n      for (var i = 0; i < this.finalArr.length; i++) {\n        if (_loop(i)) break;\n      }\n    },\n    // 判断点是否在多边形内（射线法）\n    isPointInPolygon: function isPointInPolygon(point, polygon) {\n      if (!polygon || polygon.length < 3) {\n        return false;\n      }\n      var inside = false;\n      for (var i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n        var xi = polygon[i].x,\n          yi = polygon[i].y;\n        var xj = polygon[j].x,\n          yj = polygon[j].y;\n        var intersect = yi > point.y !== yj > point.y && point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi;\n        if (intersect) {\n          inside = !inside;\n        }\n      }\n      return inside;\n    },\n    // 通过表格行点击高亮并选择区域\n    highlightArea: function highlightArea(row, column, event) {\n      var index = this.roomNameList.indexOf(row);\n      if (index >= 0 && index < this.finalArr.length) {\n        this.hoveredAreaIndex = index;\n        this.redrawWithHighlight();\n      }\n    },\n    // 通过表格行悬停高亮区域\n    mouseoverArea: function mouseoverArea(row, column, event) {\n      var index = this.roomNameList.indexOf(row);\n      if (index >= 0 && index < this.finalArr.length) {\n        this.lastHoveredAreaIndex = this.hoveredAreaIndex;\n        this.hoveredAreaIndex = index;\n        this.redrawWithHighlight();\n      }\n    },\n    // 表格行鼠标移出恢复之前的高亮状态\n    mouseoutArea: function mouseoutArea(row, column, event) {\n      this.hoveredAreaIndex = this.lastHoveredAreaIndex;\n      this.redrawCanvas();\n    },\n    // 通过索引删除区域\n    deleteAreaByIndex: function deleteAreaByIndex(index) {\n      var _this15 = this;\n      if (index >= 0 && index < this.finalArr.length) {\n        // 二次确认删除操作，强调可能影响已绑定设备\n        this.$confirm(\"\\u8B66\\u544A\\uFF1A\\u5220\\u9664\\\"\".concat(this.roomNameList[index].roomName, \"\\\"\\u533A\\u57DF\\u53EF\\u80FD\\u4F1A\\u5F71\\u54CD\\u5DF2\\u7ED1\\u5B9A\\u7684\\u8BBE\\u5907\\uFF01\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u5417\\uFF1F\"), '删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: true,\n          message: \"<div>\\n            <p><strong style=\\\"color: red;\\\">\\u8B66\\u544A\\uFF1A</strong>\\u5220\\u9664\\\"\".concat(this.roomNameList[index].roomName, \"\\\"\\u533A\\u57DF\\u5C06\\u4F1A\\uFF1A</p>\\n            <ul>\\n              <li>\\u6C38\\u4E45\\u79FB\\u9664\\u8BE5\\u533A\\u57DF\\u7684\\u7ED8\\u5236\\u4FE1\\u606F</li>\\n              <li>\\u53EF\\u80FD\\u5BFC\\u81F4\\u4E0E\\u8BE5\\u533A\\u57DF\\u5173\\u8054\\u7684\\u8BBE\\u5907\\u5931\\u53BB\\u4F4D\\u7F6E\\u4FE1\\u606F</li>\\n              <li>\\u9700\\u8981\\u91CD\\u65B0\\u7ED1\\u5B9A\\u76F8\\u5173\\u8BBE\\u5907\\u5230\\u5176\\u4ED6\\u533A\\u57DF</li>\\n            </ul>\\n            <p>\\u786E\\u5B9A\\u8981\\u7EE7\\u7EED\\u5220\\u9664\\u64CD\\u4F5C\\u5417\\uFF1F</p>\\n          </div>\")\n        }).then(function () {\n          // 获取要删除的区域ID\n          var regionId = _this15.roomNameList[index].id;\n\n          // 如果有ID，调用API删除\n          if (regionId) {\n            _this15.$api['areaManagement/maintenanceRegion-batch-delete']({\n              ids: [regionId]\n            }).then(function (data) {\n              _this15.$message.success(\"\\u5DF2\\u5220\\u9664\\u533A\\u57DF\");\n\n              // 删除前端数据\n              _this15.finalArr.splice(index, 1);\n              _this15.finalArrRelative.splice(index, 1);\n              _this15.roomNameList.splice(index, 1);\n\n              // 重绘画布\n              _this15.hoveredAreaIndex = -1;\n              _this15.redrawCanvas();\n\n              // 通知父组件区域变化\n              _this15.$emit('getPointArr', _this15.finalArr);\n            });\n          } else {\n            // 新绘制的区域没有ID，直接从前端删除\n            _this15.finalArr.splice(index, 1);\n            _this15.finalArrRelative.splice(index, 1);\n            _this15.roomNameList.splice(index, 1);\n\n            // 重绘画布\n            _this15.hoveredAreaIndex = -1;\n            _this15.redrawCanvas();\n\n            // 通知父组件区域变化\n            _this15.$emit('getPointArr', _this15.finalArr);\n            _this15.$message.success(\"\\u5DF2\\u5220\\u9664\\u533A\\u57DF\");\n          }\n        });\n      }\n    },\n    // 检查两个多边形是否重叠\n    checkPolygonsOverlap: function checkPolygonsOverlap(poly1, poly2) {\n      // 简单实现：检查每个多边形的点是否在另一个多边形内\n      // 或者检查多边形的边是否相交\n\n      // 检查poly1的点是否在poly2内\n      for (var i = 0; i < poly1.length; i++) {\n        if (this.isPointInPolygon(poly1[i], poly2)) {\n          return true;\n        }\n      }\n\n      // 检查poly2的点是否在poly1内\n      for (var _i4 = 0; _i4 < poly2.length; _i4++) {\n        if (this.isPointInPolygon(poly2[_i4], poly1)) {\n          return true;\n        }\n      }\n\n      // 检查边是否相交\n      for (var _i6 = 0; _i6 < poly1.length; _i6++) {\n        var a1 = poly1[_i6];\n        var a2 = poly1[(_i6 + 1) % poly1.length];\n        for (var j = 0; j < poly2.length; j++) {\n          var b1 = poly2[j];\n          var b2 = poly2[(j + 1) % poly2.length];\n          if (this.doLinesIntersect(a1, a2, b1, b2)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    },\n    // 检查两条线段是否相交\n    doLinesIntersect: function doLinesIntersect(p1, p2, p3, p4) {\n      // 线段1的方向\n      var d1x = p2.x - p1.x;\n      var d1y = p2.y - p1.y;\n\n      // 线段2的方向\n      var d2x = p4.x - p3.x;\n      var d2y = p4.y - p3.y;\n\n      // 行列式\n      var denominator = d2y * d1x - d2x * d1y;\n\n      // 如果行列式为0，则线段平行或共线\n      if (denominator === 0) {\n        return false;\n      }\n\n      // 参数t和u\n      var u_a = (d2x * (p1.y - p3.y) - d2y * (p1.x - p3.x)) / denominator;\n      var u_b = (d1x * (p1.y - p3.y) - d1y * (p1.x - p3.x)) / denominator;\n\n      // 如果t和u都在[0,1]范围内，则线段相交\n      return u_a >= 0 && u_a <= 1 && u_b >= 0 && u_b <= 1;\n    },\n    // 放大画布\n    zoomIn: function zoomIn() {\n      this.zoomLevel *= 1.1;\n      this.applyZoom();\n    },\n    // 缩小画布\n    zoomOut: function zoomOut() {\n      this.zoomLevel *= 0.9;\n      this.applyZoom();\n    },\n    // 重置缩放\n    resetZoom: function resetZoom() {\n      this.zoomLevel = this.initialZoomLevel || 1;\n      this.applyZoom();\n    },\n    // 应用缩放\n    applyZoom: function applyZoom() {\n      if (!this.canvas) {\n        return;\n      }\n\n      // 应用缩放变换\n      this.canvas.style.transform = \"scale(\".concat(this.zoomLevel, \")\");\n      this.canvas.style.transformOrigin = 'top left';\n\n      // 重绘以更新区域名称大小\n      this.redrawCanvas();\n\n      // 检查是否需要滚动按钮\n      this.checkScrollNeeded();\n\n      // 不再需要调整容器高度\n      // this.adjustContainerHeight();\n    },\n    // 自适应屏幕\n    autoFit: function autoFit() {\n      var _this16 = this;\n      if (!this.canvas || !this.$refs.canvasContainer) {\n        return;\n      }\n      var container = this.$refs.canvasContainer;\n      var containerWidth = container.clientWidth;\n      var containerHeight = window.innerHeight - 100; // 减去头部和边距\n\n      var imgRatio = this.canvas.width / this.canvas.height;\n      var containerRatio = containerWidth / containerHeight;\n      var newZoomLevel;\n      if (imgRatio > containerRatio) {\n        // 宽度适配\n        newZoomLevel = containerWidth / this.canvas.width;\n      } else {\n        // 高度适配\n        newZoomLevel = containerHeight / this.canvas.height;\n      }\n\n      // 确保缩放系数不会过小\n      newZoomLevel = Math.max(newZoomLevel, 0.1);\n\n      // 应用默认缩放系数，使图片显示更大\n      // 限制最大缩放为2倍，避免图片过大导致性能问题\n      newZoomLevel = Math.min(newZoomLevel * this.defaultZoomFactor, 2.5);\n      this.zoomLevel = newZoomLevel;\n      this.initialZoomLevel = newZoomLevel;\n      this.applyZoom();\n\n      // 确保画布在容器中居中\n      this.$nextTick(function () {\n        var canvasContainer = _this16.$refs.canvasContainer;\n        if (canvasContainer) {\n          // 如果画布宽度小于容器宽度，添加水平居中样式\n          if (_this16.canvas.offsetWidth * _this16.zoomLevel < canvasContainer.offsetWidth) {\n            _this16.canvas.style.marginLeft = 'auto';\n            _this16.canvas.style.marginRight = 'auto';\n            _this16.canvas.style.display = 'block';\n          }\n        }\n      });\n\n      // this.$message.success('已自动调整图片大小');\n    },\n    // 更新事件坐标计算，考虑缩放因素\n    getAdjustedCoordinates: function getAdjustedCoordinates(e) {\n      if (!this.canvas) {\n        return {\n          x: e.layerX,\n          y: e.layerY\n        };\n      }\n\n      // 考虑缩放因素\n      var rect = this.canvas.getBoundingClientRect();\n      var scaleX = this.canvas.width / rect.width;\n      var scaleY = this.canvas.height / rect.height;\n\n      // 相对于画布的坐标\n      var x = (e.clientX - rect.left) * scaleX;\n      var y = (e.clientY - rect.top) * scaleY;\n      return {\n        x: x,\n        y: y\n      };\n    },\n    // 切换显示区域名称\n    toggleAreaNames: function toggleAreaNames() {\n      this.showAreaNames = !this.showAreaNames;\n      this.redrawCanvas();\n      this.$message.success(this.showAreaNames ? '已显示区域名称' : '已隐藏区域名称');\n    },\n    // 处理默认缩放变化\n    handleDefaultZoomChange: function handleDefaultZoomChange(command) {\n      var newFactor = parseFloat(command);\n      if (!isNaN(newFactor)) {\n        this.defaultZoomFactor = newFactor;\n        this.$message.success(\"\\u9ED8\\u8BA4\\u7F29\\u653E\\u5DF2\\u8BBE\\u7F6E\\u4E3A\".concat(Math.round(newFactor * 100), \"%\"));\n        this.autoFit(); // 立即应用新的缩放设置\n      }\n    },\n    // 处理区域表单提交\n    handleAreaFormSubmit: function handleAreaFormSubmit() {\n      var _this17 = this;\n      this.$refs.areaForm.validate(function (valid) {\n        if (valid && _this17.tempAreaData) {\n          _this17.areaFormLoading = true;\n\n          // 将区域数据添加到相应的数组\n          _this17.finalArr.push(_this17.tempAreaData.area);\n          _this17.finalArrRelative.push(_this17.tempAreaData.relativeArea);\n\n          // 添加信息\n          _this17.roomNameList.push({\n            roomName: _this17.areaForm.roomName,\n            roomType: _this17.areaForm.roomType,\n            remark: _this17.areaForm.remark\n          });\n\n          // 清除临时数据\n          _this17.tempAreaData = null;\n\n          // 关闭表单对话框\n          setTimeout(function () {\n            _this17.areaFormLoading = false;\n            _this17.areaFormVisible = false;\n            _this17.$message.success('区域信息已保存');\n            // 重绘画布以显示新添加的区域名称\n            _this17.redrawCanvas();\n          }, 300);\n        }\n      });\n    },\n    // 处理区域表单取消\n    handleAreaFormCancel: function handleAreaFormCancel() {\n      if (this.tempAreaData) {\n        // 如果取消表单，需要撤销绘制的区域\n        this._repeal();\n        this.tempAreaData = null;\n      }\n      this.areaFormVisible = false;\n    }\n  }\n};", {"version": 3, "names": ["name", "components", "props", "areaArr", "Array", "data", "imgForm", "imgModal", "imgOkLoading", "imgFormValidate", "isOpenLike", "imgUrl", "roomNameList", "spinShow", "imgPath", "finalArr", "finalArrRelative", "dataArr", "isShow", "canvas", "ctx", "ctxX", "ctxY", "lineWidth", "type", "typeOption", "label", "value", "canvasHistory", "step", "loading", "fillStyle", "strokeStyle", "lineNum", "linePeak", "lineStep", "ellipseR", "dialogVisible", "isUnfold", "fontSize", "fontColor", "fontFamily", "img", "Image", "hoveredAreaIndex", "highlightColor", "deleteMode", "lastHoveredAreaIndex", "zoomLevel", "initialZoomLevel", "defaultZoomFactor", "showAreaNames", "areaFormVisible", "areaFormLoading", "areaForm", "roomName", "roomType", "remark", "areaFormRules", "required", "message", "trigger", "tempAreaData", "deboun<PERSON><PERSON><PERSON>r", "scrollLeftBtn", "scrollRightBtn", "currentDrawingId", "computed", "defaultZoomText", "concat", "Math", "round", "showScrollButtons", "$refs", "canvasContainer", "width", "clientWidth", "created", "drawingId", "$route", "query", "localStorage", "getItem", "setItem", "$router", "replace", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON>", "window", "removeEventListener", "checkScrollNeeded", "mounted", "_this2", "addEventListener", "getList", "$message", "warning", "setTimeout", "goBack", "methods", "_this3", "$api", "then", "console", "log", "drawingData", "drawingInfo", "init", "getRegionList", "error", "catch", "_this4", "length", "map", "region", "regionName", "id", "areaCoordinates", "JSON", "parse", "point<PERSON><PERSON>", "e", "filter", "coords", "_toConsumableArray", "processAreaData", "redrawCanvas", "push", "editAreaByIndex", "index", "handleFileChange", "file", "_this5", "rawFile", "raw", "reader", "FileReader", "onload", "target", "result", "readAsDataURL", "$forceUpdate", "scrollLeft", "container", "scrollAmount", "min", "scrollRight", "_this", "image", "setAttribute", "src", "complete", "content", "document", "createElement", "height", "getContext", "globalAlpha", "drawImage", "toDataURL", "globalCompositeOperation", "innerHTML", "append<PERSON><PERSON><PERSON>", "max", "$nextTick", "autoFit", "bindEventLisner", "for<PERSON>ach", "i", "createL2", "radioClick", "item", "txtBlue", "resetTxt", "downLoad", "url", "fileName", "elink", "download", "style", "display", "href", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "resetAll", "$emit", "clearRect", "applyZoom", "save", "reset", "repeal", "_repeal", "_this6", "canvasPic", "canvasRedo", "_this7", "rebroadcast", "_this8", "r1", "r2", "button", "getAdjustedCoordinates", "checkAndDeleteArea", "createL", "oncontextmenu", "clearTimeout", "isOverArea", "hoveredIndex", "isPointInPolygon", "redrawWithHighlight", "cursor", "judge", "dot", "coordinates", "status", "mousePoint", "x", "y", "beginPath", "moveTo", "lineTo", "stroke", "closePath", "info", "newArea", "hasOverlap", "checkPolygonsOverlap", "area", "relativeArea", "point", "toRelativeCoordinates", "<PERSON><PERSON><PERSON><PERSON>", "drawArea", "isHighlighted", "arguments", "undefined", "fill", "areaIndex", "indexOf", "drawAreaName", "centerX", "centerY", "canvasWidth", "canvasHeight", "baseFontSize", "font", "textAlign", "textBaseline", "textWidth", "measureText", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "fillText", "restore", "roundRect", "radius", "fillColor", "strokeColor", "tl", "tr", "br", "bl", "defaultRadius", "side", "quadraticCurveTo", "createR", "r", "rx", "layerX", "ry", "layerY", "strokeRect", "interval", "setInterval", "clearInterval", "rect", "drawArrow", "arrowFromX", "arrowFromY", "toX", "toY", "theta", "headlen", "fromX", "fromY", "angle", "atan2", "PI", "angle1", "angle2", "topX", "cos", "topY", "sin", "botX", "botY", "arrowX", "arrowY", "createT", "isTextInputMode", "offset", "getPointOnCanvas", "txt", "left", "top", "color", "maxlength", "floor", "focus", "cs", "getElementsByClassName", "exportJson", "_this10", "exportArr", "stringify", "maintenanceRegionList", "room", "$confirm", "confirmButtonText", "cancelButtonText", "success", "ToBase64", "that", "getElementById", "imgFile", "files", "imgData", "handleMultiUpload", "v", "handleMultiUpload2", "changeImgUrl", "floorNo", "imgOk", "_this11", "validate", "valid", "finally", "imgCancel", "resetFields", "parseFloat", "toFixed", "toAbsoluteCoordinates", "isRelativeCoordinate", "ensureAbsoluteCoordinate", "ensureRelativeCoordinate", "areaData", "_this12", "processedData", "firstArea", "firstPoint", "isRelative", "deleteAllAreas", "_this13", "dangerouslyUseHTMLString", "regionIds", "ids", "_this14", "_loop", "splice", "polygon", "inside", "j", "xi", "yi", "xj", "yj", "intersect", "highlightArea", "row", "column", "event", "mouseoverArea", "mouseoutArea", "deleteAreaByIndex", "_this15", "regionId", "poly1", "poly2", "a1", "a2", "b1", "b2", "doLinesIntersect", "p1", "p2", "p3", "p4", "d1x", "d1y", "d2x", "d2y", "denominator", "u_a", "u_b", "zoomIn", "zoomOut", "resetZoom", "transform", "transform<PERSON><PERSON>in", "_this16", "containerWidth", "containerHeight", "innerHeight", "imgRatio", "containerRatio", "newZoomLevel", "offsetWidth", "marginLeft", "marginRight", "getBoundingClientRect", "scaleX", "scaleY", "clientX", "clientY", "toggleAreaNames", "handleDefaultZoomChange", "command", "newFactor", "isNaN", "handleAreaFormSubmit", "_this17", "handleAreaFormCancel"], "sources": ["src/bysc_system/views/visualOpsManagement/areaManagement/index.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"spinShow\" class=\"draw\">\r\n    <!-- <el-loading :fullscreen=\"false\" v-if=\"spinShow\">\r\n      <div>正在加载图片</div>\r\n    </el-loading> -->\r\n    <div class=\"drawTop\" ref=\"drawTop\" v-if=\"lineStep == lineNum\">\r\n      <!-- <div>\r\n        <el-upload\r\n          class=\"upload-demo\"\r\n          action=\"\"\r\n          :auto-upload=\"false\"\r\n          :show-file-list=\"false\"\r\n          :on-change=\"handleFileChange\">\r\n          <el-button type=\"primary\" size=\"small\">选择文件</el-button>\r\n        </el-upload>\r\n      </div> -->\r\n      <div>\r\n        <el-button @click=\"goBack\" size=\"small\">返回</el-button>\r\n      </div>\r\n      <!-- <div>\r\n        是否开启模糊查询（标准层开启，其它关闭）：<el-switch v-model=\"isOpenLike\"/>\r\n      </div> -->\r\n      <!-- <div>\r\n        <el-button @click=\"resetAll\" size=\"small\">重新绘制</el-button>\r\n      </div> -->\r\n      <div>\r\n        <el-button @click=\"deleteAllAreas\" size=\"small\" type=\"danger\">删除全部区域</el-button>\r\n      </div>\r\n      <div>\r\n        <el-button @click=\"toggleAreaNames\" size=\"small\" :type=\"showAreaNames ? 'success' : 'info'\">\r\n          {{ showAreaNames ? '隐藏区域名称' : '显示区域名称' }}\r\n        </el-button>\r\n      </div>\r\n\r\n      <div>\r\n        <el-button-group>\r\n          <el-button @click=\"zoomIn\" icon=\"el-icon-zoom-in\" size=\"small\">放大</el-button>\r\n          <el-button @click=\"zoomOut\" icon=\"el-icon-zoom-out\" size=\"small\">缩小</el-button>\r\n          <!-- <el-button @click=\"resetZoom\" size=\"small\">重置缩放</el-button>\r\n          <el-button @click=\"autoFit\" type=\"primary\" size=\"small\">自适应屏幕</el-button> -->\r\n        </el-button-group>\r\n      </div>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"exportJson\" size=\"small\">保存区域</el-button>\r\n      </div>\r\n      <!-- <div>\r\n        <el-dropdown @command=\"handleDefaultZoomChange\">\r\n          <el-button size=\"small\">\r\n            默认缩放: {{ defaultZoomText }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n          </el-button>\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <el-dropdown-item command=\"1.0\">100%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.3\">130%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.5\">150%</el-dropdown-item>\r\n            <el-dropdown-item command=\"1.8\">180%</el-dropdown-item>\r\n            <el-dropdown-item command=\"2.0\">200%</el-dropdown-item>\r\n            <el-dropdown-item command=\"2.2\">220%</el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div> -->\r\n    </div>\r\n    <div>\r\n      （鼠标左键点击图片绘制区域，任意位置点击右键自动连接起点和终点完成区域绘制，可重复此步骤绘制多个区域）\r\n    </div>\r\n    <div>\r\n      （鼠标悬停在已绘制区域上可高亮显示，点击区域内任意位置即可删除该区域）\r\n    </div>\r\n    <div style=\"display: flex;\">\r\n      <div class=\"canvas-container\" ref=\"canvasContainer\">\r\n        <!-- 固定在容器右上角的滚动控制按钮，只在需要时显示 -->\r\n        <div v-if=\"showScrollButtons\" class=\"scroll-controls\">\r\n          <el-button-group>\r\n            <el-button @click=\"scrollLeft\" icon=\"el-icon-arrow-left\" size=\"mini\" title=\"向左滚动\"></el-button>\r\n            <el-button @click=\"scrollRight\" icon=\"el-icon-arrow-right\" size=\"mini\" title=\"向右滚动\"></el-button>\r\n          </el-button-group>\r\n        </div>\r\n        <div class=\"content\" ref=\"content\"></div>\r\n        <input\r\n          v-show=\"isShow\"\r\n          type=\"text\"\r\n          @blur=\"txtBlue\"\r\n          ref=\"txt\"\r\n          id=\"txt\"\r\n          style=\"\r\n            z-index: 9999;\r\n            position: absolute;\r\n            border: 0;\r\n            background: none;\r\n            outline: none;\r\n          \"\r\n        />\r\n      </div>\r\n      <!-- 区域列表显示面板 -->\r\n      <div style=\"width: 25%; padding-left: 10px; max-height: 600px; overflow-y: auto;\">\r\n        <h4>已绘制区域列表</h4>\r\n        <el-empty v-if=\"roomNameList.length === 0\" description=\"暂无区域\"></el-empty>\r\n        <el-table\r\n          v-else\r\n          :data=\"roomNameList\"\r\n          style=\"width: 100%\"\r\n          size=\"small\"\r\n          :max-height=\"550\"\r\n          @row-click=\"highlightArea\"\r\n          @row-mouseover=\"mouseoverArea\"\r\n          @row-mouseout=\"mouseoutArea\">\r\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\r\n          <el-table-column prop=\"roomName\" label=\"名称\" align=\"center\"></el-table-column>\r\n          <el-table-column label=\"操作\" width=\"150\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <a\r\n                style=\"margin-left: 10px;\"\r\n                @click.stop=\"deleteAreaByIndex(scope.$index)\">\r\n                删除\r\n              </a>\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      :visible.sync=\"areaFormVisible\"\r\n      title=\"区域信息\"\r\n      width=\"400px\"\r\n      :close-on-click-modal=\"false\"\r\n      @closed=\"handleAreaFormCancel\">\r\n      <el-form\r\n        ref=\"areaForm\"\r\n        :model=\"areaForm\"\r\n        :rules=\"areaFormRules\"\r\n        label-width=\"100px\"\r\n        size=\"small\">\r\n        <el-form-item label=\"名称\" prop=\"roomName\">\r\n          <el-input v-model=\"areaForm.roomName\" placeholder=\"请输入名称\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleAreaFormCancel\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleAreaFormSubmit\" :loading=\"areaFormLoading\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"imgModal\"\r\n      title=\"更换图片\"\r\n      width=\"400px\"\r\n      @closed=\"imgCancel\">\r\n      <el-form\r\n        ref=\"imgForm\"\r\n        :model=\"imgForm\"\r\n        :rules=\"imgFormValidate\"\r\n        label-width=\"110px\">\r\n        <el-form-item label=\"楼层号\" prop=\"floorNo\">\r\n          <el-input\r\n            v-model.trim=\"imgForm.floorNo\"\r\n            maxlength=\"32\"\r\n            placeholder=\"楼层号\">\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"图片地址\" prop=\"imgUrl\">\r\n          <el-input\r\n            v-model.trim=\"imgForm.imgUrl\"\r\n            maxlength=\"256\"\r\n            placeholder=\"图片地址\">\r\n          </el-input>\r\n          <!-- <multi-upload-pic-input :maxCount=\"1\" :maxSize=\"10240\" @on-change=\"handleMultiUpload2\" width=\"235px\" ref=\"multiUploadImage\"></multi-upload-pic-input> -->\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"imgCancel\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"imgOkLoading\" @click=\"imgOk\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'callout',\r\n  components: {\r\n  },\r\n  props: {\r\n    areaArr: Array,\r\n  },\r\n  data() {\r\n    return {\r\n      imgForm: {},\r\n      imgModal: false,\r\n      imgOkLoading: false,\r\n      imgFormValidate: {},\r\n\r\n      isOpenLike: false,\r\n      imgUrl: '',\r\n      roomNameList: [],\r\n      spinShow: false,\r\n      imgPath: '',\r\n      finalArr: [], // 绝对坐标数组，用于绘制\r\n      finalArrRelative: [], // 相对坐标数组，用于保存\r\n      dataArr: [],\r\n      isShow: false,\r\n      canvas: '',\r\n      ctx: '',\r\n      ctxX: 0,\r\n      ctxY: 0,\r\n      lineWidth: 3,\r\n      type: 'L',\r\n      typeOption: [\r\n        {label: '线', value: 'L'},\r\n      // { label: '矩形', value: 'R' }\r\n      //   {label: \"箭头\", value: \"A\"},\r\n      //   {label: \"文字\", value: \"T\"},\r\n      ],\r\n      canvasHistory: [],\r\n      step: 0,\r\n      loading: false,\r\n      fillStyle: '#CB0707',\r\n      strokeStyle: '#1E90FF',\r\n      lineNum: 2,\r\n      linePeak: [],\r\n      lineStep: 2,\r\n      ellipseR: 0.5,\r\n      dialogVisible: false,\r\n      isUnfold: true,\r\n      fontSize: 18,\r\n      fontColor: '#333333',\r\n      fontFamily: 'Microsoft YaHei, Arial, sans-serif',\r\n      img: new Image(),\r\n      hoveredAreaIndex: -1, // 当前鼠标悬停的区域索引\r\n      highlightColor: '#2E8AE6',\r\n      deleteMode: false, // 是否处于删除模式\r\n      lastHoveredAreaIndex: -1, // 用于表格行悬停时记录之前高亮的区域索引\r\n      zoomLevel: 1,\r\n      initialZoomLevel: 1,\r\n      defaultZoomFactor: 1, // 增大默认缩放系数到1.5\r\n      showAreaNames: true,\r\n      // 区域表单相关\r\n      areaFormVisible: false,\r\n      areaFormLoading: false,\r\n      areaForm: {\r\n        roomName: '',\r\n        roomType: '',\r\n        remark: ''\r\n      },\r\n      areaFormRules: {\r\n        roomName: [\r\n          {required: true, message: '请输入名称', trigger: 'blur'}\r\n        ]\r\n      },\r\n      tempAreaData: null, // 临时存储新绘制的区域数据\r\n      debounceTimer: null, // 添加防抖定时器\r\n      // 滚动控制相关\r\n      scrollLeftBtn: null,\r\n      scrollRightBtn: null,\r\n      // 保存当前drawingId，用于刷新页面时恢复\r\n      currentDrawingId: ''\r\n    };\r\n  },\r\n  computed: {\r\n    defaultZoomText() {\r\n      return `${Math.round(this.defaultZoomFactor * 100)}%`;\r\n    },\r\n    // 判断是否需要显示滚动按钮\r\n    showScrollButtons() {\r\n      if (!this.$refs.canvasContainer || !this.canvas) {\r\n        return false;\r\n      }\r\n      // 当画布宽度大于容器宽度时显示滚动按钮\r\n      return this.canvas.width * this.zoomLevel > this.$refs.canvasContainer.clientWidth;\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL参数或localStorage获取drawingId\r\n    const drawingId = this.$route.query.drawingId || localStorage.getItem('currentDrawingId');\r\n\r\n    // 如果有drawingId，保存到组件状态和localStorage\r\n    if (drawingId) {\r\n      this.currentDrawingId = drawingId;\r\n      localStorage.setItem('currentDrawingId', drawingId);\r\n\r\n      // 如果URL中没有drawingId但localStorage有，则更新URL\r\n      if (!this.$route.query.drawingId) {\r\n        this.$router.replace({\r\n          query: {...this.$route.query, drawingId}\r\n        });\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化监听器\r\n    window.removeEventListener('resize', this.checkScrollNeeded);\r\n  },\r\n  mounted() {\r\n    // 添加窗口大小变化监听器\r\n    window.addEventListener('resize', this.checkScrollNeeded);\r\n\r\n    // 获取drawingId并加载数据\r\n    const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n    if (drawingId) {\r\n      this.getList();\r\n    } else {\r\n      this.$message.warning('缺少图纸ID参数，请返回列表重新选择');\r\n      setTimeout(() => {\r\n        this.goBack();\r\n      }, 2000);\r\n    }\r\n  },\r\n  methods: {\r\n    getList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        this.$message.warning('缺少图纸ID参数');\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceDrawingManagement-get']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        console.log(data, 'data');\r\n        if (data) {\r\n          // 获取base64图片数据\r\n          const drawingData = data;\r\n          if (drawingData.drawingInfo) {\r\n            // 设置图片路径为base64数据\r\n            this.imgPath = drawingData.drawingInfo;\r\n            // 图纸基本信息\r\n            this.imgUrl = drawingData.drawingInfo;\r\n\r\n            // 初始化画布，渲染图片\r\n            this.init();\r\n\r\n            // 图片渲染完成后，获取已有区域数据\r\n            this.getRegionList();\r\n          } else {\r\n            this.$message.error('图片数据不存在');\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取图纸数据失败:', error);\r\n        this.$message.error('获取图纸数据失败');\r\n      });\r\n    },\r\n\r\n    // 获取已有区域数据\r\n    getRegionList() {\r\n      // 优先使用组件状态中的drawingId，其次是URL参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      if (!drawingId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['areaManagement/maintenanceRegion-list']({\r\n        drawingId: drawingId\r\n      }).then(data => {\r\n        if (data && data.length > 0) {\r\n          // 将区域信息转换为需要的格式，并保存原始数据中的ID\r\n          this.roomNameList = data.map(region => ({\r\n            roomName: region.regionName,\r\n            roomType: '',\r\n            remark: region.remark || '',\r\n            id: region.id || null // 保存原始区域ID，用于后续删除操作\r\n          }));\r\n\r\n          // 解析区域坐标数据\r\n          const areaCoordinates = data.map(region => {\r\n            try {\r\n              return JSON.parse(region.pointJson || '[]');\r\n            } catch (e) {\r\n              console.error('Error parsing pointJson:', e);\r\n              return [];\r\n            }\r\n          }).filter(coords => coords.length > 0);\r\n\r\n          // 保存相对坐标数据\r\n          this.finalArrRelative = [...areaCoordinates];\r\n\r\n          // 处理区域数据并绘制到画布上\r\n          if (areaCoordinates.length > 0) {\r\n            // 转换成绝对坐标并保存\r\n            this.finalArr = this.processAreaData(areaCoordinates);\r\n\r\n            // 绘制所有区域\r\n            this.redrawCanvas();\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取区域数据失败:', error);\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.push('/system/visualOpsManagement/drawingManagement');\r\n    },\r\n    editAreaByIndex(index) {\r\n\r\n    },\r\n    handleFileChange(file) {\r\n      const rawFile = file.raw;\r\n      if (!rawFile) {\r\n        return;\r\n      }\r\n      const reader = new FileReader();\r\n      reader.onload = e => {\r\n        this.imgPath = e.target.result;\r\n        this.spinShow = true;\r\n        this.init();\r\n      };\r\n      reader.readAsDataURL(rawFile);\r\n    },\r\n    // 检查是否需要滚动按钮\r\n    checkScrollNeeded() {\r\n      if (this.$refs.canvasContainer && this.canvas) {\r\n      // 通过Vue的响应式系统触发计算属性重新计算\r\n        this.$forceUpdate();\r\n      // 不再需要调整容器高度\r\n      // this.adjustContainerHeight();\r\n      }\r\n    },\r\n    // 不再需要调整容器高度的方法\r\n    /*\r\n  adjustContainerHeight() {\r\n    if (this.$refs.canvasContainer && this.canvas) {\r\n      const canvasHeight = this.canvas.height * this.zoomLevel;\r\n      // 设置容器高度为画布高度加上一些边距\r\n      this.$refs.canvasContainer.style.height = `${canvasHeight + 20}px`;\r\n    }\r\n  },\r\n  */\r\n    // 向左滚动画布\r\n    scrollLeft() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft -= scrollAmount;\r\n      }\r\n    },\r\n    // 向右滚动画布\r\n    scrollRight() {\r\n      if (this.$refs.canvasContainer) {\r\n        const container = this.$refs.canvasContainer;\r\n        const scrollAmount = Math.min(300, container.clientWidth / 2);\r\n        container.scrollLeft += scrollAmount;\r\n      }\r\n    },\r\n    init() {\r\n      let _this = this;\r\n      let image = new Image();\r\n      image.setAttribute('crossOrigin', 'anonymous');\r\n      image.src = this.imgPath;\r\n      image.onload = function () {\r\n      // 图片加载完，再draw 和 toDataURL\r\n        if (image.complete) {\r\n          _this.spinShow = false;\r\n          _this.img = image;\r\n          let content = _this.$refs.content;\r\n          _this.canvas = document.createElement('canvas');\r\n          _this.canvas.height = _this.img.height;\r\n          _this.canvas.width = _this.img.width;\r\n          _this.canvas.setAttribute('style', 'border:2px solid red;');\r\n          _this.canvas.setAttribute('id', 'myCanvas');\r\n          _this.ctx = _this.canvas.getContext('2d');\r\n          _this.ctx.globalAlpha = 1;\r\n          _this.ctx.drawImage(_this.img, 0, 0);\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.ctx.globalCompositeOperation = _this.type;\r\n\r\n          // 清空之前的内容\r\n          content.innerHTML = '';\r\n          content.appendChild(_this.canvas);\r\n\r\n          // 重置交互状态\r\n          _this.deleteMode = false;\r\n          _this.hoveredAreaIndex = -1;\r\n\r\n          // 预设最小缩放比例，防止图片太小\r\n          _this.defaultZoomFactor = Math.max(_this.defaultZoomFactor, 1.2);\r\n\r\n          // 自动适应屏幕\r\n          _this.$nextTick(() => {\r\n            _this.autoFit();\r\n            // 不再需要调整容器高度\r\n            // _this.adjustContainerHeight();\r\n            // 检查是否需要滚动按钮\r\n            _this.checkScrollNeeded();\r\n          });\r\n\r\n          _this.bindEventLisner();\r\n\r\n          if (_this.areaArr) {\r\n            _this.finalArr = _this.processAreaData(_this.areaArr);\r\n            _this.finalArr.forEach(i => {\r\n              _this.createL2(i);\r\n            });\r\n          }\r\n        }\r\n      };\r\n    },\r\n    radioClick(item) {\r\n      if (item != 'T') {\r\n        this.txtBlue();\r\n        this.resetTxt();\r\n      }\r\n    },\r\n    // 下载画布\r\n    downLoad() {\r\n      let _this = this;\r\n      let url = _this.canvas.toDataURL('image/png');\r\n      let fileName = 'canvas.png';\r\n      if ('download' in document.createElement('a')) {\r\n      // 非IE下载\r\n        const elink = document.createElement('a');\r\n        elink.download = fileName;\r\n        elink.style.display = 'none';\r\n        elink.href = url;\r\n        document.body.appendChild(elink);\r\n        elink.click();\r\n        document.body.removeChild(elink);\r\n      } else {\r\n      // IE10+下载\r\n        navigator.msSaveBlob(url, fileName);\r\n      }\r\n    },\r\n    // 重置所有内容\r\n    resetAll() {\r\n      this.dataArr = [];\r\n      this.finalArr = [];\r\n      this.finalArrRelative = []; // 同时清空相对坐标数组\r\n      this.roomNameList = [];\r\n      this.$emit('getPointArr', this.finalArr);\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.canvasHistory = [];\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.canvasHistory.push(this.canvas.toDataURL());\r\n      this.step = 0;\r\n      this.resetTxt();\r\n      this.hoveredAreaIndex = -1;\r\n      this.deleteMode = false;\r\n      // 应用缩放\r\n      this.applyZoom();\r\n    },\r\n    // 保存区域规划\r\n    save() {\r\n    // this.finalArr\r\n    },\r\n    // 清空当前画布\r\n    reset() {\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n      this.resetTxt();\r\n    },\r\n    // 撤销方法\r\n    repeal() {\r\n      let _this = this;\r\n      if (this.isShow) {\r\n        _this.resetTxt();\r\n        _this._repeal();\r\n      } else {\r\n        _this._repeal();\r\n      }\r\n    },\r\n    _repeal() {\r\n      if (this.step >= 1) {\r\n        this.step = this.step - 1;\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n          this.loading = true;\r\n        });\r\n      } else {\r\n        this.$message.warning('不能再继续撤销了');\r\n      }\r\n    },\r\n    // 恢复方法\r\n    canvasRedo() {\r\n      if (this.step < this.canvasHistory.length - 1) {\r\n        if (this.step == 0) {\r\n          this.step = 1;\r\n        } else {\r\n          this.step++;\r\n        }\r\n        let canvasPic = new Image();\r\n        canvasPic.src = this.canvasHistory[this.step];\r\n        canvasPic.addEventListener('load', () => {\r\n          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n          this.ctx.drawImage(canvasPic, 0, 0);\r\n        });\r\n      } else {\r\n        this.$message.warning('已经是最新的记录了');\r\n      }\r\n    },\r\n    // 绘制历史数组中的最后一个\r\n    rebroadcast() {\r\n      let canvasPic = new Image();\r\n      canvasPic.src = this.canvasHistory[this.step];\r\n      canvasPic.addEventListener('load', () => {\r\n        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n        this.ctx.drawImage(canvasPic, 0, 0);\r\n        this.loading = true;\r\n      });\r\n    },\r\n    // 绑定事件,判断分支\r\n    bindEventLisner() {\r\n      let _this = this;\r\n      let r1, r2; // 绘制圆形，矩形需要\r\n      this.canvas.addEventListener('click', function (e) {\r\n        if (_this.type == 'L' && e.button == 0) {\r\n          const coords = _this.getAdjustedCoordinates(e);\r\n          // 如果处于删除模式，检查是否点击在区域内\r\n          if (_this.deleteMode) {\r\n            _this.checkAndDeleteArea(coords);\r\n          } else {\r\n            _this.createL(coords, 'begin');\r\n          }\r\n        }\r\n      });\r\n      this.canvas.oncontextmenu = function (e) {\r\n        const coords = _this.getAdjustedCoordinates(e);\r\n        _this.createL(coords, 'end');\r\n        return false;\r\n      };\r\n\r\n      // 添加鼠标移动事件监听器，用于区域高亮，使用防抖处理\r\n      this.canvas.addEventListener('mousemove', function (e) {\r\n      // 清除之前的定时器\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        // 创建新的定时器，延迟执行高亮逻辑\r\n        _this.debounceTimer = setTimeout(() => {\r\n          if (_this.finalArr && _this.finalArr.length > 0) {\r\n            const coords = _this.getAdjustedCoordinates(e);\r\n            let isOverArea = false;\r\n            let hoveredIndex = -1;\r\n\r\n            // 检查鼠标是否在任何一个区域内\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.isPointInPolygon(coords, _this.finalArr[i])) {\r\n                hoveredIndex = i;\r\n                isOverArea = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            // 只有当悬停状态发生变化时才重绘\r\n            if (_this.hoveredAreaIndex !== hoveredIndex) {\r\n              _this.hoveredAreaIndex = hoveredIndex;\r\n              _this.redrawWithHighlight();\r\n              document.body.style.cursor = isOverArea ? 'pointer' : 'default';\r\n            }\r\n          }\r\n        }, 10); // 10毫秒的防抖延迟，平衡响应性和性能\r\n      });\r\n\r\n      // 添加鼠标离开画布事件\r\n      this.canvas.addEventListener('mouseleave', function () {\r\n        if (_this.debounceTimer) {\r\n          clearTimeout(_this.debounceTimer);\r\n        }\r\n\r\n        if (_this.hoveredAreaIndex !== -1) {\r\n          _this.hoveredAreaIndex = -1;\r\n          _this.redrawCanvas();\r\n          document.body.style.cursor = 'default';\r\n        }\r\n      });\r\n    },\r\n    // 判断点是否在多边形内（原有方法，保留但不使用）\r\n    judge(dot, coordinates) {\r\n      return this.isPointInPolygon(dot, coordinates);\r\n    },\r\n    // 绘制线条\r\n    createL(coords, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 点击开始绘制前，检查点击位置是否在已有区域内\r\n        const mousePoint = {x: coords.x, y: coords.y};\r\n        for (let i = 0; i < this.finalArr.length; i++) {\r\n          if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n            this.$message.warning('您点击的位置已在其他区域内，请选择其他位置绘制');\r\n            return; // 阻止继续执行\r\n          }\r\n        }\r\n\r\n        if (_this.dataArr && _this.dataArr.length === 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.beginPath();\r\n          _this.ctx.moveTo(coords.x, coords.y);\r\n          _this.ctx.lineTo(coords.x + 1, coords.y + 1);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        } else if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.dataArr.push({x: coords.x, y: coords.y});\r\n          _this.ctx.lineTo(coords.x, coords.y);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.stroke();\r\n        }\r\n      } else if (status == 'end') {\r\n        if (_this.dataArr && _this.dataArr.length !== 0) {\r\n          _this.ctx.moveTo(\r\n            _this.dataArr[_this.dataArr.length - 1].x,\r\n            _this.dataArr[_this.dataArr.length - 1].y\r\n          );\r\n          _this.ctx.lineTo(_this.dataArr[0].x, _this.dataArr[0].y);\r\n          _this.ctx.stroke();\r\n          _this.ctx.closePath();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step;\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          if (_this.dataArr && _this.dataArr.length < 3) {\r\n            _this.$message.info('该区域点数少于三个，不保存');\r\n            _this._repeal();\r\n          } else {\r\n          // 检查新绘制的区域是否与现有区域有重叠\r\n            const newArea = _this.dataArr;\r\n            let hasOverlap = false;\r\n\r\n            for (let i = 0; i < _this.finalArr.length; i++) {\r\n              if (_this.checkPolygonsOverlap(newArea, _this.finalArr[i])) {\r\n                hasOverlap = true;\r\n                break;\r\n              }\r\n            }\r\n\r\n            if (hasOverlap) {\r\n              _this.$message.warning('新绘制的区域与现有区域重叠，请重新绘制');\r\n              _this._repeal();\r\n            } else {\r\n            // 存储新绘制的区域数据，等待表单提交后再保存\r\n              _this.tempAreaData = {\r\n                area: _this.dataArr,\r\n                relativeArea: _this.dataArr.map(point => _this.toRelativeCoordinates(point))\r\n              };\r\n\r\n              // 打开区域信息表单\r\n              _this.areaForm = {\r\n                roomName: `区域${_this.roomNameList.length + 1}`,\r\n                roomType: '',\r\n                remark: ''\r\n              };\r\n              _this.areaFormVisible = true;\r\n            }\r\n          }\r\n          this.$emit('getPointArr', _this.finalArr);\r\n          _this.dataArr = [];\r\n          _this.canvas.onmousemove = null;\r\n        }\r\n      }\r\n    },\r\n    // 重绘画布并高亮显示悬停区域\r\n    redrawWithHighlight() {\r\n    // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 重绘背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i], i === this.hoveredAreaIndex);\r\n      }\r\n    },\r\n\r\n    // 统一的区域绘制函数\r\n    drawArea(area, isHighlighted = false) {\r\n      if (!area || area.length < 3) {\r\n        return;\r\n      }\r\n\r\n      this.ctx.beginPath();\r\n      this.ctx.moveTo(area[0].x, area[0].y);\r\n\r\n      // 绘制区域轮廓\r\n      for (let i = 1; i < area.length; i++) {\r\n        this.ctx.lineTo(area[i].x, area[i].y);\r\n      }\r\n\r\n      // 闭合路径\r\n      this.ctx.lineTo(area[0].x, area[0].y);\r\n\r\n      // 设置样式\r\n      if (isHighlighted) {\r\n      // 高亮显示 - 增加线宽，使用更深的蓝色\r\n        this.ctx.strokeStyle = this.highlightColor;\r\n        this.ctx.lineWidth = this.lineWidth + 2;\r\n\r\n        // 填充半透明颜色\r\n        this.ctx.fillStyle = this.highlightColor + '22'; // 添加22作为透明度 (13%)\r\n        this.ctx.fill();\r\n      } else {\r\n        this.ctx.strokeStyle = this.strokeStyle;\r\n        this.ctx.lineWidth = this.lineWidth;\r\n      }\r\n\r\n      this.ctx.stroke();\r\n      this.ctx.closePath();\r\n\r\n      // 如果启用了显示区域名称，并且能找到对应的名\r\n      const areaIndex = this.finalArr.indexOf(area);\r\n      if (this.showAreaNames && areaIndex >= 0 && this.roomNameList[areaIndex]) {\r\n        this.drawAreaName(area, this.roomNameList[areaIndex].roomName);\r\n      }\r\n    },\r\n\r\n    // 绘制区域名称\r\n    drawAreaName(area, name) {\r\n      if (!area || area.length < 3 || !name) {\r\n        return;\r\n      }\r\n\r\n      // 计算区域的中心点\r\n      let centerX = 0, centerY = 0;\r\n      for (let i = 0; i < area.length; i++) {\r\n        centerX += area[i].x;\r\n        centerY += area[i].y;\r\n      }\r\n      centerX /= area.length;\r\n      centerY /= area.length;\r\n\r\n      // 保存当前上下文状态\r\n      this.ctx.save();\r\n\r\n      // 设置固定的字体大小，不受图片尺寸和缩放影响\r\n      // 首先获取画布的尺寸\r\n      const canvasWidth = this.canvas.width;\r\n      const canvasHeight = this.canvas.height;\r\n\r\n      // 计算固定的字体大小，基于画布尺寸的一个合适比例\r\n      // 这样可以确保在不同大小的图片上，字体大小相对于图片尺寸的比例是一致的\r\n      const baseFontSize = 36; // 固定基础字体大小\r\n\r\n      // 应用字体设置\r\n      this.ctx.font = `bold ${baseFontSize}px ${this.fontFamily}`;\r\n      this.ctx.textAlign = 'center';\r\n      this.ctx.textBaseline = 'middle';\r\n\r\n      // 测量文本宽度\r\n      const textWidth = this.ctx.measureText(name).width;\r\n\r\n      // 增强文字阴影以提高可读性\r\n      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.9)';\r\n      this.ctx.shadowBlur = 4;\r\n      this.ctx.shadowOffsetX = 1;\r\n      this.ctx.shadowOffsetY = 1;\r\n\r\n      // 使用更亮的蓝色，但与线条颜色(#1E90FF)有区别\r\n      this.ctx.fillStyle = '#38B0DE'; // 天蓝色，比线条颜色略微偏青一些\r\n      this.ctx.fillText(name, centerX, centerY);\r\n\r\n      // 恢复上下文状态\r\n      this.ctx.restore();\r\n    },\r\n\r\n    // 绘制圆角矩形\r\n    roundRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {\r\n      if (typeof radius === 'undefined') {\r\n        radius = 5;\r\n      }\r\n      if (typeof radius === 'number') {\r\n        radius = {tl: radius, tr: radius, br: radius, bl: radius};\r\n      } else {\r\n        const defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};\r\n        for (const side in defaultRadius) {\r\n          radius[side] = radius[side] || defaultRadius[side];\r\n        }\r\n      }\r\n\r\n      ctx.beginPath();\r\n      ctx.moveTo(x + radius.tl, y);\r\n      ctx.lineTo(x + width - radius.tr, y);\r\n      ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);\r\n      ctx.lineTo(x + width, y + height - radius.br);\r\n      ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);\r\n      ctx.lineTo(x + radius.bl, y + height);\r\n      ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);\r\n      ctx.lineTo(x, y + radius.tl);\r\n      ctx.quadraticCurveTo(x, y, x + radius.tl, y);\r\n      ctx.closePath();\r\n\r\n      if (fillColor) {\r\n        ctx.fillStyle = fillColor;\r\n        ctx.fill();\r\n      }\r\n\r\n      if (strokeColor) {\r\n        ctx.strokeStyle = strokeColor;\r\n        ctx.lineWidth = 1;\r\n        ctx.stroke();\r\n      }\r\n    },\r\n\r\n    // 重绘画布\r\n    redrawCanvas() {\r\n      if (!this.canvas || !this.ctx) {\r\n        return;\r\n      }\r\n\r\n      // 清空画布\r\n      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\r\n      // 绘制背景图\r\n      this.ctx.drawImage(this.img, 0, 0);\r\n\r\n      // 重绘所有保留的区域\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        this.drawArea(this.finalArr[i]);\r\n      }\r\n    },\r\n\r\n    // 创建区域2（读取已有区域）\r\n    createL2(e) {\r\n      let _this = this;\r\n\r\n      // 使用统一的绘制函数\r\n      this.drawArea(e);\r\n\r\n      _this.step = _this.step + 1;\r\n      if (_this.step < _this.canvasHistory.length - 1) {\r\n        _this.canvasHistory.length = _this.step;\r\n      }\r\n      _this.canvasHistory.push(_this.canvas.toDataURL());\r\n      this.$emit('getPointArr', _this.finalArr);\r\n    },\r\n    // 绘制矩形\r\n    createR(e, status, r1, r2) {\r\n      let _this = this;\r\n      let r;\r\n      if (status == 'begin') {\r\n      // console.log('onmousemove')\r\n        _this.canvas.onmousemove = function (e) {\r\n          _this.reset();\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n\r\n          // 保留之前绘画的图形\r\n          if (_this.step !== 0) {\r\n            let canvasPic = new Image();\r\n            canvasPic.src = _this.canvasHistory[_this.step];\r\n            _this.ctx.drawImage(canvasPic, 0, 0);\r\n          }\r\n\r\n          _this.ctx.beginPath();\r\n          _this.ctx.strokeRect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n        };\r\n      } else if (status == 'end') {\r\n        _this.rebroadcast();\r\n        let interval = setInterval(() => {\r\n          if (_this.loading) {\r\n            clearInterval(interval);\r\n            _this.loading = false;\r\n          } else {\r\n            return;\r\n          }\r\n          let rx = e.layerX - r1;\r\n          let ry = e.layerY - r2;\r\n          _this.ctx.beginPath();\r\n          _this.ctx.rect(r1, r2, rx, ry);\r\n          _this.ctx.strokeStyle = _this.strokeStyle;\r\n          _this.ctx.lineWidth = _this.lineWidth;\r\n          _this.ctx.closePath();\r\n          _this.ctx.stroke();\r\n          _this.step = _this.step + 1;\r\n          if (_this.step < _this.canvasHistory.length - 1) {\r\n            _this.canvasHistory.length = _this.step; // 截断数组\r\n          }\r\n          _this.canvasHistory.push(_this.canvas.toDataURL());\r\n          _this.canvas.onmousemove = null;\r\n        }, 1);\r\n      }\r\n    },\r\n\r\n    // 绘制箭头\r\n    drawArrow(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 获取起始位置\r\n        _this.arrowFromX = e.layerX;\r\n        _this.arrowFromY = e.layerY;\r\n        _this.ctx.beginPath();\r\n        _this.ctx.moveTo(e.layerX, e.layerY);\r\n      } else if (status == 'end') {\r\n      // 计算箭头及画线\r\n        let toX = e.layerX;\r\n        let toY = e.layerY;\r\n        let theta = 30;\r\n        let headlen = 10;\r\n        let _this = this;\r\n        let fromX = this.arrowFromX;\r\n        let fromY = this.arrowFromY;\r\n        // 计算各角度和对应的P2,P3坐标\r\n        let angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;\r\n        let angle1 = ((angle + theta) * Math.PI) / 180;\r\n        let angle2 = ((angle - theta) * Math.PI) / 180;\r\n        let topX = headlen * Math.cos(angle1);\r\n        let topY = headlen * Math.sin(angle1);\r\n        let botX = headlen * Math.cos(angle2);\r\n        let botY = headlen * Math.sin(angle2);\r\n        let arrowX = fromX - topX;\r\n        let arrowY = fromY - topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.moveTo(fromX, fromY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + topX;\r\n        arrowY = toY + topY;\r\n        _this.ctx.moveTo(arrowX, arrowY);\r\n        _this.ctx.lineTo(toX, toY);\r\n        arrowX = toX + botX;\r\n        arrowY = toY + botY;\r\n        _this.ctx.lineTo(arrowX, arrowY);\r\n        _this.ctx.strokeStyle = _this.strokeStyle;\r\n        _this.ctx.lineWidth = _this.lineWidth;\r\n        _this.ctx.stroke();\r\n\r\n        _this.ctx.closePath();\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n\r\n    // 文字输入\r\n    createT(e, status) {\r\n      let _this = this;\r\n      if (status == 'begin') {\r\n      // 初始化文字输入相关参数\r\n        _this.isTextInputMode = true;\r\n      } else if (status == 'end') {\r\n        let offset = 0;\r\n        if (_this.fontSize >= 28) {\r\n          offset = _this.fontSize / 2 - 3;\r\n        } else {\r\n          offset = _this.fontSize / 2 - 2;\r\n        }\r\n\r\n        _this.ctxX = e.layerX + 2;\r\n        _this.ctxY = e.layerY + offset;\r\n\r\n        let index = this.getPointOnCanvas(e);\r\n        _this.$refs.txt.style.left = index.x + 'px';\r\n        _this.$refs.txt.style.top = index.y - _this.fontSize / 2 + 'px';\r\n        _this.$refs.txt.value = '';\r\n        _this.$refs.txt.style.height = _this.fontSize + 'px';\r\n        (_this.$refs.txt.style.width\r\n          = _this.canvas.width - e.layerX - 1 + 'px'),\r\n        (_this.$refs.txt.style.fontSize = _this.fontSize + 'px');\r\n        _this.$refs.txt.style.fontFamily = _this.fontFamily;\r\n        _this.$refs.txt.style.color = _this.fontColor;\r\n        _this.$refs.txt.style.maxlength = Math.floor(\r\n          (_this.canvas.width - e.layerX) / _this.fontSize\r\n        );\r\n        _this.isShow = true;\r\n        setTimeout(() => {\r\n          _this.$refs.txt.focus();\r\n        });\r\n      }\r\n    },\r\n    // 文字输入框失去光标时在画布上生成文字\r\n    txtBlue() {\r\n      let _this = this;\r\n      let txt = _this.$refs.txt.value;\r\n      if (txt) {\r\n        _this.ctx.font\r\n          = _this.$refs.txt.style.fontSize\r\n          + ' '\r\n          + _this.$refs.txt.style.fontFamily;\r\n        _this.ctx.fillStyle = _this.$refs.txt.style.color;\r\n        _this.ctx.fillText(txt, _this.ctxX, _this.ctxY);\r\n        _this.step = _this.step + 1;\r\n        if (_this.step < _this.canvasHistory.length - 1) {\r\n          _this.canvasHistory.length = _this.step; // 截断数组\r\n        }\r\n        _this.canvasHistory.push(_this.canvas.toDataURL());\r\n        _this.canvas.onmousemove = null;\r\n      }\r\n    },\r\n    // 计算文字框定位位置\r\n    getPointOnCanvas(e) {\r\n      let cs = this.canvas;\r\n      let content = document.getElementsByClassName('content')[0];\r\n      return {\r\n        x: e.layerX + (content.clientWidth - cs.width) / 2,\r\n        y: e.layerY,\r\n      };\r\n    },\r\n    // 清空文字\r\n    resetTxt() {\r\n      let _this = this;\r\n      _this.$refs.txt.value = '';\r\n      _this.isShow = false;\r\n    },\r\n    exportJson() {\r\n      // 直接使用已经准备好的相对坐标数组\r\n      let exportArr = JSON.parse(JSON.stringify(this.finalArrRelative));\r\n\r\n      // 获取URL中的drawingId参数\r\n      const drawingId = this.currentDrawingId || this.$route.query.drawingId;\r\n\r\n      // 准备导出数据，使用正确的字段名\r\n      let maintenanceRegionList = this.roomNameList.map((room, index) => {\r\n        return {\r\n          id: room.id || null,\r\n          regionName: room.roomName, // 使用regionName作为区域名称字段\r\n          pointJson: JSON.stringify(exportArr[index]), // 使用pointJson作为区域坐标JSON字段\r\n          remark: room.remark || '', // 备注字段\r\n          drawingId: drawingId // 从URL参数获取的图纸ID\r\n        };\r\n      });\r\n\r\n      // 如果没有区域数据，提示用户\r\n      if (maintenanceRegionList.length === 0) {\r\n        this.$message.warning('尚未绘制任何区域，无法保存');\r\n        return;\r\n      }\r\n\r\n      // 二次确认保存操作\r\n      this.$confirm('确认保存当前绘制的所有区域？', '保存确认', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'info'\r\n      }).then(() => {\r\n        // 用户确认，调用保存接口\r\n        console.log('保存的区域数据:', maintenanceRegionList);\r\n\r\n        // 调用保存接口，传入整合后的区域列表\r\n        this.$api['areaManagement/maintenanceRegion-save']({\r\n          maintenanceRegionList: maintenanceRegionList\r\n        }).then(data => {\r\n          this.$message.success('保存成功');\r\n        }).catch(error => {\r\n          this.$message.error('保存失败，请重试');\r\n        });\r\n      });\r\n    },\r\n    ToBase64() {\r\n      let that = this;\r\n      var img = document.getElementById('imgfile');\r\n      var imgFile = new FileReader();\r\n      imgFile.readAsDataURL(img.files[0]);\r\n\r\n      imgFile.onload = function () {\r\n        var imgData = this.result; // base64数据\r\n        that.imgPath = imgData;\r\n        that.init();\r\n      };\r\n    },\r\n    handleMultiUpload(v) {\r\n      this.imgUrl = v[0];\r\n    },\r\n    handleMultiUpload2(v) {\r\n      this.imgForm.imgUrl = v[0];\r\n    },\r\n    changeImgUrl() {\r\n      this.imgForm = {\r\n        floorNo: '',\r\n        imgUrl: ''\r\n      };\r\n      this.imgModal = true;\r\n    },\r\n    imgOk() {\r\n      this.$refs.imgForm.validate(valid => {\r\n        if (valid) {\r\n          this.imgOkLoading = true;\r\n          this.$api['iotHome/changeFloorMap'](this.imgForm)\r\n            .then(data => {\r\n              this.$message.success('更换成功');\r\n              this.imgModal = false;\r\n            })\r\n            .finally(() => {\r\n              this.imgOkLoading = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n    imgCancel() {\r\n    // 取消关闭\r\n      this.imgModal = false;\r\n      this.$refs.imgForm.resetFields();\r\n    },\r\n    // 将绝对坐标转换为相对坐标（0-1之间的比例值）\r\n    toRelativeCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: parseFloat((point.x / this.canvas.width).toFixed(4)),\r\n        y: parseFloat((point.y / this.canvas.height).toFixed(4))\r\n      };\r\n    },\r\n\r\n    // 将相对坐标转换为绝对坐标（实际像素值）\r\n    toAbsoluteCoordinates(point) {\r\n      if (!this.canvas) {\r\n        return point;\r\n      }\r\n      return {\r\n        x: Math.round(point.x * this.canvas.width),\r\n        y: Math.round(point.y * this.canvas.height)\r\n      };\r\n    },\r\n\r\n    // 判断一个点是否为相对坐标（值在0-1之间）\r\n    isRelativeCoordinate(point) {\r\n      return point.x >= 0 && point.x <= 1 && point.y >= 0 && point.y <= 1;\r\n    },\r\n\r\n    // 确保点使用绝对坐标\r\n    ensureAbsoluteCoordinate(point) {\r\n      if (this.isRelativeCoordinate(point)) {\r\n        return this.toAbsoluteCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 确保点使用相对坐标\r\n    ensureRelativeCoordinate(point) {\r\n      if (!this.isRelativeCoordinate(point)) {\r\n        return this.toRelativeCoordinates(point);\r\n      }\r\n      return point;\r\n    },\r\n\r\n    // 处理输入的区域数据，确保使用正确的坐标类型\r\n    processAreaData(areaData) {\r\n      if (!areaData || !areaData.length) {\r\n        return [];\r\n      }\r\n\r\n      // 深拷贝避免修改原始数据\r\n      const processedData = JSON.parse(JSON.stringify(areaData));\r\n\r\n      // 检查第一个点的第一个坐标，判断是否为相对坐标\r\n      const firstArea = processedData[0];\r\n      if (firstArea && firstArea.length > 0) {\r\n        const firstPoint = firstArea[0];\r\n        const isRelative = this.isRelativeCoordinate(firstPoint);\r\n\r\n        // 如果是相对坐标，转换为绝对坐标用于绘制\r\n        if (isRelative) {\r\n        // 同时保存相对坐标版本\r\n          this.finalArrRelative = JSON.parse(JSON.stringify(processedData));\r\n\r\n          processedData.forEach(area => {\r\n            area.forEach((point, index) => {\r\n              area[index] = this.toAbsoluteCoordinates(point);\r\n            });\r\n          });\r\n        } else {\r\n        // 如果是绝对坐标，生成相对坐标版本\r\n          this.finalArrRelative = processedData.map(area =>\r\n            area.map(point => this.toRelativeCoordinates(point))\r\n          );\r\n        }\r\n      }\r\n\r\n      return processedData;\r\n    },\r\n    deleteAllAreas() {\r\n      if (this.finalArr.length > 0) {\r\n        // 二次确认删除所有区域操作，强调可能影响已绑定设备\r\n        this.$confirm('警告：删除所有区域可能会影响已绑定的设备！确定要删除吗？', '批量删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">严重警告：</strong>删除所有区域将会：</p>\r\n            <ul>\r\n              <li>永久移除所有区域的绘制信息</li>\r\n              <li>导致所有与这些区域关联的设备失去位置信息</li>\r\n              <li>需要重新绘制区域并重新绑定所有设备</li>\r\n            </ul>\r\n            <p>此操作不可恢复，确定要继续删除所有区域吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 收集所有区域的ID\r\n          const regionIds = this.roomNameList\r\n            .filter(room => room.id) // 只收集有ID的区域\r\n            .map(room => room.id);\r\n\r\n          if (regionIds.length > 0) {\r\n            // 有保存过的区域，调用API删除\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: regionIds\r\n            }).then(data => {\r\n              this.$message.success('已删除所有区域');\r\n\r\n              // 清空前端数据\r\n              this.finalArr = [];\r\n              this.finalArrRelative = [];\r\n              this.roomNameList = [];\r\n\r\n              // 重绘画布\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            }).catch(error => {\r\n              this.$message.error('删除失败，请重试');\r\n              console.error('批量删除区域失败:', error);\r\n            });\r\n          } else {\r\n            // 没有保存过的区域，直接清空前端数据\r\n            this.finalArr = [];\r\n            this.finalArrRelative = [];\r\n            this.roomNameList = [];\r\n\r\n            // 重绘画布\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success('已删除所有区域');\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.info('没有可删除的区域');\r\n      }\r\n    },\r\n    // 检查并删除点击的区域\r\n    checkAndDeleteArea(coords) {\r\n      const mousePoint = {x: coords.x, y: coords.y};\r\n\r\n      // 检查点击是否在任何一个区域内\r\n      for (let i = 0; i < this.finalArr.length; i++) {\r\n        if (this.isPointInPolygon(mousePoint, this.finalArr[i])) {\r\n        // 弹出确认对话框\r\n          this.$confirm(`确定要删除第${i + 1}个区域吗?`, '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n          // 删除该区域\r\n            this.finalArr.splice(i, 1);\r\n            this.finalArrRelative.splice(i, 1);\r\n\r\n            // 如果有对应的名称，也要删除\r\n            if (this.roomNameList.length > i) {\r\n              this.roomNameList.splice(i, 1);\r\n            }\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除第${i + 1}个区域`);\r\n          });\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 判断点是否在多边形内（射线法）\r\n    isPointInPolygon(point, polygon) {\r\n      if (!polygon || polygon.length < 3) {\r\n        return false;\r\n      }\r\n\r\n      let inside = false;\r\n      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\r\n        const xi = polygon[i].x, yi = polygon[i].y;\r\n        const xj = polygon[j].x, yj = polygon[j].y;\r\n\r\n        const intersect = ((yi > point.y) !== (yj > point.y))\r\n        && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);\r\n\r\n        if (intersect) {\r\n          inside = !inside;\r\n        }\r\n      }\r\n\r\n      return inside;\r\n    },\r\n\r\n\r\n    // 通过表格行点击高亮并选择区域\r\n    highlightArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 通过表格行悬停高亮区域\r\n    mouseoverArea(row, column, event) {\r\n      const index = this.roomNameList.indexOf(row);\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        this.lastHoveredAreaIndex = this.hoveredAreaIndex;\r\n        this.hoveredAreaIndex = index;\r\n        this.redrawWithHighlight();\r\n      }\r\n    },\r\n\r\n    // 表格行鼠标移出恢复之前的高亮状态\r\n    mouseoutArea(row, column, event) {\r\n      this.hoveredAreaIndex = this.lastHoveredAreaIndex;\r\n      this.redrawCanvas();\r\n    },\r\n\r\n    // 通过索引删除区域\r\n    deleteAreaByIndex(index) {\r\n      if (index >= 0 && index < this.finalArr.length) {\r\n        // 二次确认删除操作，强调可能影响已绑定设备\r\n        this.$confirm(`警告：删除\"${this.roomNameList[index].roomName}\"区域可能会影响已绑定的设备！确定要删除吗？`, '删除确认', {\r\n          confirmButtonText: '确定删除',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: true,\r\n          message: `<div>\r\n            <p><strong style=\"color: red;\">警告：</strong>删除\"${this.roomNameList[index].roomName}\"区域将会：</p>\r\n            <ul>\r\n              <li>永久移除该区域的绘制信息</li>\r\n              <li>可能导致与该区域关联的设备失去位置信息</li>\r\n              <li>需要重新绑定相关设备到其他区域</li>\r\n            </ul>\r\n            <p>确定要继续删除操作吗？</p>\r\n          </div>`\r\n        }).then(() => {\r\n          // 获取要删除的区域ID\r\n          const regionId = this.roomNameList[index].id;\r\n\r\n          // 如果有ID，调用API删除\r\n          if (regionId) {\r\n            this.$api['areaManagement/maintenanceRegion-batch-delete']({\r\n              ids: [regionId]\r\n            }).then(data => {\r\n              this.$message.success(`已删除区域`);\r\n\r\n              // 删除前端数据\r\n              this.finalArr.splice(index, 1);\r\n              this.finalArrRelative.splice(index, 1);\r\n              this.roomNameList.splice(index, 1);\r\n\r\n              // 重绘画布\r\n              this.hoveredAreaIndex = -1;\r\n              this.redrawCanvas();\r\n\r\n              // 通知父组件区域变化\r\n              this.$emit('getPointArr', this.finalArr);\r\n            });\r\n          } else {\r\n            // 新绘制的区域没有ID，直接从前端删除\r\n            this.finalArr.splice(index, 1);\r\n            this.finalArrRelative.splice(index, 1);\r\n            this.roomNameList.splice(index, 1);\r\n\r\n            // 重绘画布\r\n            this.hoveredAreaIndex = -1;\r\n            this.redrawCanvas();\r\n\r\n            // 通知父组件区域变化\r\n            this.$emit('getPointArr', this.finalArr);\r\n\r\n            this.$message.success(`已删除区域`);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 检查两个多边形是否重叠\r\n    checkPolygonsOverlap(poly1, poly2) {\r\n    // 简单实现：检查每个多边形的点是否在另一个多边形内\r\n    // 或者检查多边形的边是否相交\r\n\r\n      // 检查poly1的点是否在poly2内\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        if (this.isPointInPolygon(poly1[i], poly2)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查poly2的点是否在poly1内\r\n      for (let i = 0; i < poly2.length; i++) {\r\n        if (this.isPointInPolygon(poly2[i], poly1)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 检查边是否相交\r\n      for (let i = 0; i < poly1.length; i++) {\r\n        const a1 = poly1[i];\r\n        const a2 = poly1[(i + 1) % poly1.length];\r\n\r\n        for (let j = 0; j < poly2.length; j++) {\r\n          const b1 = poly2[j];\r\n          const b2 = poly2[(j + 1) % poly2.length];\r\n\r\n          if (this.doLinesIntersect(a1, a2, b1, b2)) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 检查两条线段是否相交\r\n    doLinesIntersect(p1, p2, p3, p4) {\r\n    // 线段1的方向\r\n      const d1x = p2.x - p1.x;\r\n      const d1y = p2.y - p1.y;\r\n\r\n      // 线段2的方向\r\n      const d2x = p4.x - p3.x;\r\n      const d2y = p4.y - p3.y;\r\n\r\n      // 行列式\r\n      const denominator = d2y * d1x - d2x * d1y;\r\n\r\n      // 如果行列式为0，则线段平行或共线\r\n      if (denominator === 0) {\r\n        return false;\r\n      }\r\n\r\n      // 参数t和u\r\n      const u_a = (d2x * (p1.y - p3.y) - d2y * (p1.x - p3.x)) / denominator;\r\n      const u_b = (d1x * (p1.y - p3.y) - d1y * (p1.x - p3.x)) / denominator;\r\n\r\n      // 如果t和u都在[0,1]范围内，则线段相交\r\n      return (u_a >= 0 && u_a <= 1 && u_b >= 0 && u_b <= 1);\r\n    },\r\n\r\n    // 放大画布\r\n    zoomIn() {\r\n      this.zoomLevel *= 1.1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 缩小画布\r\n    zoomOut() {\r\n      this.zoomLevel *= 0.9;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 重置缩放\r\n    resetZoom() {\r\n      this.zoomLevel = this.initialZoomLevel || 1;\r\n      this.applyZoom();\r\n    },\r\n\r\n    // 应用缩放\r\n    applyZoom() {\r\n      if (!this.canvas) {\r\n        return;\r\n      }\r\n\r\n      // 应用缩放变换\r\n      this.canvas.style.transform = `scale(${this.zoomLevel})`;\r\n      this.canvas.style.transformOrigin = 'top left';\r\n\r\n      // 重绘以更新区域名称大小\r\n      this.redrawCanvas();\r\n\r\n      // 检查是否需要滚动按钮\r\n      this.checkScrollNeeded();\r\n\r\n    // 不再需要调整容器高度\r\n    // this.adjustContainerHeight();\r\n    },\r\n\r\n    // 自适应屏幕\r\n    autoFit() {\r\n      if (!this.canvas || !this.$refs.canvasContainer) {\r\n        return;\r\n      }\r\n\r\n      const container = this.$refs.canvasContainer;\r\n      const containerWidth = container.clientWidth;\r\n      const containerHeight = window.innerHeight - 100; // 减去头部和边距\r\n\r\n      const imgRatio = this.canvas.width / this.canvas.height;\r\n      const containerRatio = containerWidth / containerHeight;\r\n\r\n      let newZoomLevel;\r\n\r\n      if (imgRatio > containerRatio) {\r\n      // 宽度适配\r\n        newZoomLevel = containerWidth / this.canvas.width;\r\n      } else {\r\n      // 高度适配\r\n        newZoomLevel = containerHeight / this.canvas.height;\r\n      }\r\n\r\n      // 确保缩放系数不会过小\r\n      newZoomLevel = Math.max(newZoomLevel, 0.1);\r\n\r\n      // 应用默认缩放系数，使图片显示更大\r\n      // 限制最大缩放为2倍，避免图片过大导致性能问题\r\n      newZoomLevel = Math.min(newZoomLevel * this.defaultZoomFactor, 2.5);\r\n\r\n      this.zoomLevel = newZoomLevel;\r\n      this.initialZoomLevel = newZoomLevel;\r\n      this.applyZoom();\r\n\r\n      // 确保画布在容器中居中\r\n      this.$nextTick(() => {\r\n        const canvasContainer = this.$refs.canvasContainer;\r\n        if (canvasContainer) {\r\n        // 如果画布宽度小于容器宽度，添加水平居中样式\r\n          if (this.canvas.offsetWidth * this.zoomLevel < canvasContainer.offsetWidth) {\r\n            this.canvas.style.marginLeft = 'auto';\r\n            this.canvas.style.marginRight = 'auto';\r\n            this.canvas.style.display = 'block';\r\n          }\r\n        }\r\n      });\r\n\r\n      // this.$message.success('已自动调整图片大小');\r\n    },\r\n\r\n    // 更新事件坐标计算，考虑缩放因素\r\n    getAdjustedCoordinates(e) {\r\n      if (!this.canvas) {\r\n        return {x: e.layerX, y: e.layerY};\r\n      }\r\n\r\n      // 考虑缩放因素\r\n      const rect = this.canvas.getBoundingClientRect();\r\n      const scaleX = this.canvas.width / rect.width;\r\n      const scaleY = this.canvas.height / rect.height;\r\n\r\n      // 相对于画布的坐标\r\n      const x = (e.clientX - rect.left) * scaleX;\r\n      const y = (e.clientY - rect.top) * scaleY;\r\n\r\n      return {x, y};\r\n    },\r\n\r\n    // 切换显示区域名称\r\n    toggleAreaNames() {\r\n      this.showAreaNames = !this.showAreaNames;\r\n      this.redrawCanvas();\r\n      this.$message.success(this.showAreaNames ? '已显示区域名称' : '已隐藏区域名称');\r\n    },\r\n\r\n    // 处理默认缩放变化\r\n    handleDefaultZoomChange(command) {\r\n      const newFactor = parseFloat(command);\r\n      if (!isNaN(newFactor)) {\r\n        this.defaultZoomFactor = newFactor;\r\n        this.$message.success(`默认缩放已设置为${Math.round(newFactor * 100)}%`);\r\n        this.autoFit(); // 立即应用新的缩放设置\r\n      }\r\n    },\r\n\r\n    // 处理区域表单提交\r\n    handleAreaFormSubmit() {\r\n      this.$refs.areaForm.validate(valid => {\r\n        if (valid && this.tempAreaData) {\r\n          this.areaFormLoading = true;\r\n\r\n          // 将区域数据添加到相应的数组\r\n          this.finalArr.push(this.tempAreaData.area);\r\n          this.finalArrRelative.push(this.tempAreaData.relativeArea);\r\n\r\n          // 添加信息\r\n          this.roomNameList.push({\r\n            roomName: this.areaForm.roomName,\r\n            roomType: this.areaForm.roomType,\r\n            remark: this.areaForm.remark\r\n          });\r\n\r\n          // 清除临时数据\r\n          this.tempAreaData = null;\r\n\r\n          // 关闭表单对话框\r\n          setTimeout(() => {\r\n            this.areaFormLoading = false;\r\n            this.areaFormVisible = false;\r\n            this.$message.success('区域信息已保存');\r\n            // 重绘画布以显示新添加的区域名称\r\n            this.redrawCanvas();\r\n          }, 300);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 处理区域表单取消\r\n    handleAreaFormCancel() {\r\n      if (this.tempAreaData) {\r\n      // 如果取消表单，需要撤销绘制的区域\r\n        this._repeal();\r\n        this.tempAreaData = null;\r\n      }\r\n      this.areaFormVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scope>\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.draw {\r\n  min-width: 420px;\r\n  overflow-y: hidden;\r\n}\r\n\r\n.content {\r\n  flex-grow: 1;\r\n  width: 100%;\r\n  display: block; /* 修改为block，避免居中导致的偏移 */\r\n}\r\n\r\n.canvas-container {\r\n  width: 75%;\r\n  position: relative;\r\n  overflow: auto; /* 保持滚动功能 */\r\n  border: 1px solid #ebeef5;\r\n  height: 600px; /* 固定高度 */\r\n}\r\n\r\n/* 滚动控制按钮样式 */\r\n.scroll-controls {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  z-index: 100;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  border-radius: 4px;\r\n  padding: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.drawTop {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 5px;\r\n}\r\n\r\n.drawTop > div {\r\n  display: flex;\r\n  padding: 5px 5px;\r\n}\r\n\r\ndiv.drawTopContrllor {\r\n  // display: none;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .drawTop {\r\n    position: absolute;\r\n    background-color: white;\r\n    width: 100%;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .drawTopContrllor {\r\n    display: flex !important;\r\n    width: 100%;\r\n    padding: 0 !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAiLA;EACAA,IAAA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,OAAA,EAAAC;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,YAAA;MACAC,eAAA;MAEAC,UAAA;MACAC,MAAA;MACAC,YAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,OAAA;MACAC,MAAA;MACAC,MAAA;MACAC,GAAA;MACAC,IAAA;MACAC,IAAA;MACAC,SAAA;MACAC,IAAA;MACAC,UAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA;MACA;MACA;MACA;MAAA,CACA;MACAC,aAAA;MACAC,IAAA;MACAC,OAAA;MACAC,SAAA;MACAC,WAAA;MACAC,OAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,GAAA,MAAAC,KAAA;MACAC,gBAAA;MAAA;MACAC,cAAA;MACAC,UAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,SAAA;MACAC,gBAAA;MACAC,iBAAA;MAAA;MACAC,aAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAH,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,YAAA;MAAA;MACAC,aAAA;MAAA;MACA;MACAC,aAAA;MACAC,cAAA;MACA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,MAAArB,iBAAA;IACA;IACA;IACAsB,iBAAA,WAAAA,kBAAA;MACA,UAAAC,KAAA,CAAAC,eAAA,UAAAvD,MAAA;QACA;MACA;MACA;MACA,YAAAA,MAAA,CAAAwD,KAAA,QAAA3B,SAAA,QAAAyB,KAAA,CAAAC,eAAA,CAAAE,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,SAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA,IAAAG,YAAA,CAAAC,OAAA;;IAEA;IACA,IAAAJ,SAAA;MACA,KAAAZ,gBAAA,GAAAY,SAAA;MACAG,YAAA,CAAAE,OAAA,qBAAAL,SAAA;;MAEA;MACA,UAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;QACA,KAAAM,OAAA,CAAAC,OAAA;UACAL,KAAA,EAAAM,aAAA,CAAAA,aAAA,UAAAP,MAAA,CAAAC,KAAA;YAAAF,SAAA,EAAAA;UAAA;QACA;MACA;IACA;EACA;EACAS,aAAA,WAAAA,cAAA;IACA;IACAC,MAAA,CAAAC,mBAAA,gBAAAC,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACAJ,MAAA,CAAAK,gBAAA,gBAAAH,iBAAA;;IAEA;IACA,IAAAZ,SAAA,QAAAZ,gBAAA,SAAAa,MAAA,CAAAC,KAAA,CAAAF,SAAA;IACA,IAAAA,SAAA;MACA,KAAAgB,OAAA;IACA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACAC,UAAA;QACAL,MAAA,CAAAM,MAAA;MACA;IACA;EACA;EACAC,OAAA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,MAAA;MACA;MACA,IAAAtB,SAAA,QAAAZ,gBAAA,SAAAa,MAAA,CAAAC,KAAA,CAAAF,SAAA;MAEA,KAAAA,SAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAK,IAAA;QACAvB,SAAA,EAAAA;MACA,GAAAwB,IAAA,WAAAjG,IAAA;QACAkG,OAAA,CAAAC,GAAA,CAAAnG,IAAA;QACA,IAAAA,IAAA;UACA;UACA,IAAAoG,WAAA,GAAApG,IAAA;UACA,IAAAoG,WAAA,CAAAC,WAAA;YACA;YACAN,MAAA,CAAAtF,OAAA,GAAA2F,WAAA,CAAAC,WAAA;YACA;YACAN,MAAA,CAAAzF,MAAA,GAAA8F,WAAA,CAAAC,WAAA;;YAEA;YACAN,MAAA,CAAAO,IAAA;;YAEA;YACAP,MAAA,CAAAQ,aAAA;UACA;YACAR,MAAA,CAAAL,QAAA,CAAAc,KAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAD,KAAA;QACAN,OAAA,CAAAM,KAAA,cAAAA,KAAA;QACAT,MAAA,CAAAL,QAAA,CAAAc,KAAA;MACA;IACA;IAEA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAG,MAAA;MACA;MACA,IAAAjC,SAAA,QAAAZ,gBAAA,SAAAa,MAAA,CAAAC,KAAA,CAAAF,SAAA;MAEA,KAAAA,SAAA;QACA;MACA;MAEA,KAAAuB,IAAA;QACAvB,SAAA,EAAAA;MACA,GAAAwB,IAAA,WAAAjG,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2G,MAAA;UACA;UACAD,MAAA,CAAAnG,YAAA,GAAAP,IAAA,CAAA4G,GAAA,WAAAC,MAAA;YAAA;cACA3D,QAAA,EAAA2D,MAAA,CAAAC,UAAA;cACA3D,QAAA;cACAC,MAAA,EAAAyD,MAAA,CAAAzD,MAAA;cACA2D,EAAA,EAAAF,MAAA,CAAAE,EAAA;YACA;UAAA;;UAEA;UACA,IAAAC,eAAA,GAAAhH,IAAA,CAAA4G,GAAA,WAAAC,MAAA;YACA;cACA,OAAAI,IAAA,CAAAC,KAAA,CAAAL,MAAA,CAAAM,SAAA;YACA,SAAAC,CAAA;cACAlB,OAAA,CAAAM,KAAA,6BAAAY,CAAA;cACA;YACA;UACA,GAAAC,MAAA,WAAAC,MAAA;YAAA,OAAAA,MAAA,CAAAX,MAAA;UAAA;;UAEA;UACAD,MAAA,CAAA/F,gBAAA,GAAA4G,kBAAA,CAAAP,eAAA;;UAEA;UACA,IAAAA,eAAA,CAAAL,MAAA;YACA;YACAD,MAAA,CAAAhG,QAAA,GAAAgG,MAAA,CAAAc,eAAA,CAAAR,eAAA;;YAEA;YACAN,MAAA,CAAAe,YAAA;UACA;QACA;MACA,GAAAhB,KAAA,WAAAD,KAAA;QACAN,OAAA,CAAAM,KAAA,cAAAA,KAAA;MACA;IACA;IACAX,MAAA,WAAAA,OAAA;MACA,KAAAd,OAAA,CAAA2C,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,GAEA;IACAC,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,IAAA,CAAAG,GAAA;MACA,KAAAD,OAAA;QACA;MACA;MACA,IAAAE,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAhB,CAAA;QACAW,MAAA,CAAAtH,OAAA,GAAA2G,CAAA,CAAAiB,MAAA,CAAAC,MAAA;QACAP,MAAA,CAAAvH,QAAA;QACAuH,MAAA,CAAAzB,IAAA;MACA;MACA4B,MAAA,CAAAK,aAAA,CAAAP,OAAA;IACA;IACA;IACA3C,iBAAA,WAAAA,kBAAA;MACA,SAAAjB,KAAA,CAAAC,eAAA,SAAAvD,MAAA;QACA;QACA,KAAA0H,YAAA;QACA;QACA;MACA;IACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAArE,KAAA,CAAAC,eAAA;QACA,IAAAqE,SAAA,QAAAtE,KAAA,CAAAC,eAAA;QACA,IAAAsE,YAAA,GAAA1E,IAAA,CAAA2E,GAAA,MAAAF,SAAA,CAAAnE,WAAA;QACAmE,SAAA,CAAAD,UAAA,IAAAE,YAAA;MACA;IACA;IACA;IACAE,WAAA,WAAAA,YAAA;MACA,SAAAzE,KAAA,CAAAC,eAAA;QACA,IAAAqE,SAAA,QAAAtE,KAAA,CAAAC,eAAA;QACA,IAAAsE,YAAA,GAAA1E,IAAA,CAAA2E,GAAA,MAAAF,SAAA,CAAAnE,WAAA;QACAmE,SAAA,CAAAD,UAAA,IAAAE,YAAA;MACA;IACA;IACArC,IAAA,WAAAA,KAAA;MACA,IAAAwC,KAAA;MACA,IAAAC,KAAA,OAAAzG,KAAA;MACAyG,KAAA,CAAAC,YAAA;MACAD,KAAA,CAAAE,GAAA,QAAAxI,OAAA;MACAsI,KAAA,CAAAX,MAAA;QACA;QACA,IAAAW,KAAA,CAAAG,QAAA;UACAJ,KAAA,CAAAtI,QAAA;UACAsI,KAAA,CAAAzG,GAAA,GAAA0G,KAAA;UACA,IAAAI,OAAA,GAAAL,KAAA,CAAA1E,KAAA,CAAA+E,OAAA;UACAL,KAAA,CAAAhI,MAAA,GAAAsI,QAAA,CAAAC,aAAA;UACAP,KAAA,CAAAhI,MAAA,CAAAwI,MAAA,GAAAR,KAAA,CAAAzG,GAAA,CAAAiH,MAAA;UACAR,KAAA,CAAAhI,MAAA,CAAAwD,KAAA,GAAAwE,KAAA,CAAAzG,GAAA,CAAAiC,KAAA;UACAwE,KAAA,CAAAhI,MAAA,CAAAkI,YAAA;UACAF,KAAA,CAAAhI,MAAA,CAAAkI,YAAA;UACAF,KAAA,CAAA/H,GAAA,GAAA+H,KAAA,CAAAhI,MAAA,CAAAyI,UAAA;UACAT,KAAA,CAAA/H,GAAA,CAAAyI,WAAA;UACAV,KAAA,CAAA/H,GAAA,CAAA0I,SAAA,CAAAX,KAAA,CAAAzG,GAAA;UACAyG,KAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;UACAZ,KAAA,CAAA/H,GAAA,CAAA4I,wBAAA,GAAAb,KAAA,CAAA3H,IAAA;;UAEA;UACAgI,OAAA,CAAAS,SAAA;UACAT,OAAA,CAAAU,WAAA,CAAAf,KAAA,CAAAhI,MAAA;;UAEA;UACAgI,KAAA,CAAArG,UAAA;UACAqG,KAAA,CAAAvG,gBAAA;;UAEA;UACAuG,KAAA,CAAAjG,iBAAA,GAAAoB,IAAA,CAAA6F,GAAA,CAAAhB,KAAA,CAAAjG,iBAAA;;UAEA;UACAiG,KAAA,CAAAiB,SAAA;YACAjB,KAAA,CAAAkB,OAAA;YACA;YACA;YACA;YACAlB,KAAA,CAAAzD,iBAAA;UACA;UAEAyD,KAAA,CAAAmB,eAAA;UAEA,IAAAnB,KAAA,CAAAhJ,OAAA;YACAgJ,KAAA,CAAApI,QAAA,GAAAoI,KAAA,CAAAtB,eAAA,CAAAsB,KAAA,CAAAhJ,OAAA;YACAgJ,KAAA,CAAApI,QAAA,CAAAwJ,OAAA,WAAAC,CAAA;cACArB,KAAA,CAAAsB,QAAA,CAAAD,CAAA;YACA;UACA;QACA;MACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAC,OAAA;QACA,KAAAC,QAAA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,IAAA3B,KAAA;MACA,IAAA4B,GAAA,GAAA5B,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;MACA,IAAAiB,QAAA;MACA,kBAAAvB,QAAA,CAAAC,aAAA;QACA;QACA,IAAAuB,KAAA,GAAAxB,QAAA,CAAAC,aAAA;QACAuB,KAAA,CAAAC,QAAA,GAAAF,QAAA;QACAC,KAAA,CAAAE,KAAA,CAAAC,OAAA;QACAH,KAAA,CAAAI,IAAA,GAAAN,GAAA;QACAtB,QAAA,CAAA6B,IAAA,CAAApB,WAAA,CAAAe,KAAA;QACAA,KAAA,CAAAM,KAAA;QACA9B,QAAA,CAAA6B,IAAA,CAAAE,WAAA,CAAAP,KAAA;MACA;QACA;QACAQ,SAAA,CAAAC,UAAA,CAAAX,GAAA,EAAAC,QAAA;MACA;IACA;IACA;IACAW,QAAA,WAAAA,SAAA;MACA,KAAA1K,OAAA;MACA,KAAAF,QAAA;MACA,KAAAC,gBAAA;MACA,KAAAJ,YAAA;MACA,KAAAgL,KAAA,qBAAA7K,QAAA;MACA,KAAAK,GAAA,CAAAyK,SAAA,YAAA1K,MAAA,CAAAwD,KAAA,OAAAxD,MAAA,CAAAwI,MAAA;MACA,KAAA/H,aAAA;MACA,KAAAR,GAAA,CAAA0I,SAAA,MAAApH,GAAA;MACA,KAAAd,aAAA,CAAAmG,IAAA,MAAA5G,MAAA,CAAA4I,SAAA;MACA,KAAAlI,IAAA;MACA,KAAAgJ,QAAA;MACA,KAAAjI,gBAAA;MACA,KAAAE,UAAA;MACA;MACA,KAAAgJ,SAAA;IACA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA;IAAA,CACA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAA5K,GAAA,CAAAyK,SAAA,YAAA1K,MAAA,CAAAwD,KAAA,OAAAxD,MAAA,CAAAwI,MAAA;MACA,KAAAvI,GAAA,CAAA0I,SAAA,MAAApH,GAAA;MACA,KAAAmI,QAAA;IACA;IACA;IACAoB,MAAA,WAAAA,OAAA;MACA,IAAA9C,KAAA;MACA,SAAAjI,MAAA;QACAiI,KAAA,CAAA0B,QAAA;QACA1B,KAAA,CAAA+C,OAAA;MACA;QACA/C,KAAA,CAAA+C,OAAA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACA,SAAAtK,IAAA;QACA,KAAAA,IAAA,QAAAA,IAAA;QACA,IAAAuK,SAAA,OAAAzJ,KAAA;QACAyJ,SAAA,CAAA9C,GAAA,QAAA1H,aAAA,MAAAC,IAAA;QACAuK,SAAA,CAAAvG,gBAAA;UACAsG,MAAA,CAAA/K,GAAA,CAAAyK,SAAA,OAAAM,MAAA,CAAAhL,MAAA,CAAAwD,KAAA,EAAAwH,MAAA,CAAAhL,MAAA,CAAAwI,MAAA;UACAwC,MAAA,CAAA/K,GAAA,CAAA0I,SAAA,CAAAsC,SAAA;UACAD,MAAA,CAAArK,OAAA;QACA;MACA;QACA,KAAAiE,QAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAqG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAzK,IAAA,QAAAD,aAAA,CAAAoF,MAAA;QACA,SAAAnF,IAAA;UACA,KAAAA,IAAA;QACA;UACA,KAAAA,IAAA;QACA;QACA,IAAAuK,SAAA,OAAAzJ,KAAA;QACAyJ,SAAA,CAAA9C,GAAA,QAAA1H,aAAA,MAAAC,IAAA;QACAuK,SAAA,CAAAvG,gBAAA;UACAyG,MAAA,CAAAlL,GAAA,CAAAyK,SAAA,OAAAS,MAAA,CAAAnL,MAAA,CAAAwD,KAAA,EAAA2H,MAAA,CAAAnL,MAAA,CAAAwI,MAAA;UACA2C,MAAA,CAAAlL,GAAA,CAAA0I,SAAA,CAAAsC,SAAA;QACA;MACA;QACA,KAAArG,QAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAuG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAJ,SAAA,OAAAzJ,KAAA;MACAyJ,SAAA,CAAA9C,GAAA,QAAA1H,aAAA,MAAAC,IAAA;MACAuK,SAAA,CAAAvG,gBAAA;QACA2G,MAAA,CAAApL,GAAA,CAAAyK,SAAA,OAAAW,MAAA,CAAArL,MAAA,CAAAwD,KAAA,EAAA6H,MAAA,CAAArL,MAAA,CAAAwI,MAAA;QACA6C,MAAA,CAAApL,GAAA,CAAA0I,SAAA,CAAAsC,SAAA;QACAI,MAAA,CAAA1K,OAAA;MACA;IACA;IACA;IACAwI,eAAA,WAAAA,gBAAA;MACA,IAAAnB,KAAA;MACA,IAAAsD,EAAA,EAAAC,EAAA;MACA,KAAAvL,MAAA,CAAA0E,gBAAA,oBAAA4B,CAAA;QACA,IAAA0B,KAAA,CAAA3H,IAAA,WAAAiG,CAAA,CAAAkF,MAAA;UACA,IAAAhF,MAAA,GAAAwB,KAAA,CAAAyD,sBAAA,CAAAnF,CAAA;UACA;UACA,IAAA0B,KAAA,CAAArG,UAAA;YACAqG,KAAA,CAAA0D,kBAAA,CAAAlF,MAAA;UACA;YACAwB,KAAA,CAAA2D,OAAA,CAAAnF,MAAA;UACA;QACA;MACA;MACA,KAAAxG,MAAA,CAAA4L,aAAA,aAAAtF,CAAA;QACA,IAAAE,MAAA,GAAAwB,KAAA,CAAAyD,sBAAA,CAAAnF,CAAA;QACA0B,KAAA,CAAA2D,OAAA,CAAAnF,MAAA;QACA;MACA;;MAEA;MACA,KAAAxG,MAAA,CAAA0E,gBAAA,wBAAA4B,CAAA;QACA;QACA,IAAA0B,KAAA,CAAApF,aAAA;UACAiJ,YAAA,CAAA7D,KAAA,CAAApF,aAAA;QACA;;QAEA;QACAoF,KAAA,CAAApF,aAAA,GAAAkC,UAAA;UACA,IAAAkD,KAAA,CAAApI,QAAA,IAAAoI,KAAA,CAAApI,QAAA,CAAAiG,MAAA;YACA,IAAAW,MAAA,GAAAwB,KAAA,CAAAyD,sBAAA,CAAAnF,CAAA;YACA,IAAAwF,UAAA;YACA,IAAAC,YAAA;;YAEA;YACA,SAAA1C,CAAA,MAAAA,CAAA,GAAArB,KAAA,CAAApI,QAAA,CAAAiG,MAAA,EAAAwD,CAAA;cACA,IAAArB,KAAA,CAAAgE,gBAAA,CAAAxF,MAAA,EAAAwB,KAAA,CAAApI,QAAA,CAAAyJ,CAAA;gBACA0C,YAAA,GAAA1C,CAAA;gBACAyC,UAAA;gBACA;cACA;YACA;;YAEA;YACA,IAAA9D,KAAA,CAAAvG,gBAAA,KAAAsK,YAAA;cACA/D,KAAA,CAAAvG,gBAAA,GAAAsK,YAAA;cACA/D,KAAA,CAAAiE,mBAAA;cACA3D,QAAA,CAAA6B,IAAA,CAAAH,KAAA,CAAAkC,MAAA,GAAAJ,UAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,KAAA9L,MAAA,CAAA0E,gBAAA;QACA,IAAAsD,KAAA,CAAApF,aAAA;UACAiJ,YAAA,CAAA7D,KAAA,CAAApF,aAAA;QACA;QAEA,IAAAoF,KAAA,CAAAvG,gBAAA;UACAuG,KAAA,CAAAvG,gBAAA;UACAuG,KAAA,CAAArB,YAAA;UACA2B,QAAA,CAAA6B,IAAA,CAAAH,KAAA,CAAAkC,MAAA;QACA;MACA;IACA;IACA;IACAC,KAAA,WAAAA,MAAAC,GAAA,EAAAC,WAAA;MACA,YAAAL,gBAAA,CAAAI,GAAA,EAAAC,WAAA;IACA;IACA;IACAV,OAAA,WAAAA,QAAAnF,MAAA,EAAA8F,MAAA;MACA,IAAAtE,KAAA;MACA,IAAAsE,MAAA;QACA;QACA,IAAAC,UAAA;UAAAC,CAAA,EAAAhG,MAAA,CAAAgG,CAAA;UAAAC,CAAA,EAAAjG,MAAA,CAAAiG;QAAA;QACA,SAAApD,CAAA,MAAAA,CAAA,QAAAzJ,QAAA,CAAAiG,MAAA,EAAAwD,CAAA;UACA,SAAA2C,gBAAA,CAAAO,UAAA,OAAA3M,QAAA,CAAAyJ,CAAA;YACA,KAAAzE,QAAA,CAAAC,OAAA;YACA;UACA;QACA;QAEA,IAAAmD,KAAA,CAAAlI,OAAA,IAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA;UACAmC,KAAA,CAAAlI,OAAA,CAAA8G,IAAA;YAAA4F,CAAA,EAAAhG,MAAA,CAAAgG,CAAA;YAAAC,CAAA,EAAAjG,MAAA,CAAAiG;UAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAAyM,SAAA;UACA1E,KAAA,CAAA/H,GAAA,CAAA0M,MAAA,CAAAnG,MAAA,CAAAgG,CAAA,EAAAhG,MAAA,CAAAiG,CAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAApG,MAAA,CAAAgG,CAAA,MAAAhG,MAAA,CAAAiG,CAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAAY,WAAA,GAAAmH,KAAA,CAAAnH,WAAA;UACAmH,KAAA,CAAA/H,GAAA,CAAAG,SAAA,GAAA4H,KAAA,CAAA5H,SAAA;UACA4H,KAAA,CAAA/H,GAAA,CAAA4M,MAAA;QACA,WAAA7E,KAAA,CAAAlI,OAAA,IAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA;UACAmC,KAAA,CAAAlI,OAAA,CAAA8G,IAAA;YAAA4F,CAAA,EAAAhG,MAAA,CAAAgG,CAAA;YAAAC,CAAA,EAAAjG,MAAA,CAAAiG;UAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAApG,MAAA,CAAAgG,CAAA,EAAAhG,MAAA,CAAAiG,CAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAAY,WAAA,GAAAmH,KAAA,CAAAnH,WAAA;UACAmH,KAAA,CAAA/H,GAAA,CAAAG,SAAA,GAAA4H,KAAA,CAAA5H,SAAA;UACA4H,KAAA,CAAA/H,GAAA,CAAA4M,MAAA;QACA;MACA,WAAAP,MAAA;QACA,IAAAtE,KAAA,CAAAlI,OAAA,IAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA;UACAmC,KAAA,CAAA/H,GAAA,CAAA0M,MAAA,CACA3E,KAAA,CAAAlI,OAAA,CAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA,MAAA2G,CAAA,EACAxE,KAAA,CAAAlI,OAAA,CAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA,MAAA4G,CACA;UACAzE,KAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAA5E,KAAA,CAAAlI,OAAA,IAAA0M,CAAA,EAAAxE,KAAA,CAAAlI,OAAA,IAAA2M,CAAA;UACAzE,KAAA,CAAA/H,GAAA,CAAA4M,MAAA;UACA7E,KAAA,CAAA/H,GAAA,CAAA6M,SAAA;UACA9E,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAtH,IAAA;UACA,IAAAsH,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAvH,aAAA,CAAAoF,MAAA;YACAmC,KAAA,CAAAvH,aAAA,CAAAoF,MAAA,GAAAmC,KAAA,CAAAtH,IAAA;UACA;UACAsH,KAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;UACA,IAAAZ,KAAA,CAAAlI,OAAA,IAAAkI,KAAA,CAAAlI,OAAA,CAAA+F,MAAA;YACAmC,KAAA,CAAApD,QAAA,CAAAmI,IAAA;YACA/E,KAAA,CAAA+C,OAAA;UACA;YACA;YACA,IAAAiC,OAAA,GAAAhF,KAAA,CAAAlI,OAAA;YACA,IAAAmN,UAAA;YAEA,SAAA5D,GAAA,MAAAA,GAAA,GAAArB,KAAA,CAAApI,QAAA,CAAAiG,MAAA,EAAAwD,GAAA;cACA,IAAArB,KAAA,CAAAkF,oBAAA,CAAAF,OAAA,EAAAhF,KAAA,CAAApI,QAAA,CAAAyJ,GAAA;gBACA4D,UAAA;gBACA;cACA;YACA;YAEA,IAAAA,UAAA;cACAjF,KAAA,CAAApD,QAAA,CAAAC,OAAA;cACAmD,KAAA,CAAA+C,OAAA;YACA;cACA;cACA/C,KAAA,CAAArF,YAAA;gBACAwK,IAAA,EAAAnF,KAAA,CAAAlI,OAAA;gBACAsN,YAAA,EAAApF,KAAA,CAAAlI,OAAA,CAAAgG,GAAA,WAAAuH,KAAA;kBAAA,OAAArF,KAAA,CAAAsF,qBAAA,CAAAD,KAAA;gBAAA;cACA;;cAEA;cACArF,KAAA,CAAA7F,QAAA;gBACAC,QAAA,iBAAAc,MAAA,CAAA8E,KAAA,CAAAvI,YAAA,CAAAoG,MAAA;gBACAxD,QAAA;gBACAC,MAAA;cACA;cACA0F,KAAA,CAAA/F,eAAA;YACA;UACA;UACA,KAAAwI,KAAA,gBAAAzC,KAAA,CAAApI,QAAA;UACAoI,KAAA,CAAAlI,OAAA;UACAkI,KAAA,CAAAhI,MAAA,CAAAuN,WAAA;QACA;MACA;IACA;IACA;IACAtB,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAhM,GAAA,CAAAyK,SAAA,YAAA1K,MAAA,CAAAwD,KAAA,OAAAxD,MAAA,CAAAwI,MAAA;;MAEA;MACA,KAAAvI,GAAA,CAAA0I,SAAA,MAAApH,GAAA;;MAEA;MACA,SAAA8H,CAAA,MAAAA,CAAA,QAAAzJ,QAAA,CAAAiG,MAAA,EAAAwD,CAAA;QACA,KAAAmE,QAAA,MAAA5N,QAAA,CAAAyJ,CAAA,GAAAA,CAAA,UAAA5H,gBAAA;MACA;IACA;IAEA;IACA+L,QAAA,WAAAA,SAAAL,IAAA;MAAA,IAAAM,aAAA,GAAAC,SAAA,CAAA7H,MAAA,QAAA6H,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAP,IAAA,IAAAA,IAAA,CAAAtH,MAAA;QACA;MACA;MAEA,KAAA5F,GAAA,CAAAyM,SAAA;MACA,KAAAzM,GAAA,CAAA0M,MAAA,CAAAQ,IAAA,IAAAX,CAAA,EAAAW,IAAA,IAAAV,CAAA;;MAEA;MACA,SAAApD,CAAA,MAAAA,CAAA,GAAA8D,IAAA,CAAAtH,MAAA,EAAAwD,CAAA;QACA,KAAApJ,GAAA,CAAA2M,MAAA,CAAAO,IAAA,CAAA9D,CAAA,EAAAmD,CAAA,EAAAW,IAAA,CAAA9D,CAAA,EAAAoD,CAAA;MACA;;MAEA;MACA,KAAAxM,GAAA,CAAA2M,MAAA,CAAAO,IAAA,IAAAX,CAAA,EAAAW,IAAA,IAAAV,CAAA;;MAEA;MACA,IAAAgB,aAAA;QACA;QACA,KAAAxN,GAAA,CAAAY,WAAA,QAAAa,cAAA;QACA,KAAAzB,GAAA,CAAAG,SAAA,QAAAA,SAAA;;QAEA;QACA,KAAAH,GAAA,CAAAW,SAAA,QAAAc,cAAA;QACA,KAAAzB,GAAA,CAAA2N,IAAA;MACA;QACA,KAAA3N,GAAA,CAAAY,WAAA,QAAAA,WAAA;QACA,KAAAZ,GAAA,CAAAG,SAAA,QAAAA,SAAA;MACA;MAEA,KAAAH,GAAA,CAAA4M,MAAA;MACA,KAAA5M,GAAA,CAAA6M,SAAA;;MAEA;MACA,IAAAe,SAAA,QAAAjO,QAAA,CAAAkO,OAAA,CAAAX,IAAA;MACA,SAAAnL,aAAA,IAAA6L,SAAA,cAAApO,YAAA,CAAAoO,SAAA;QACA,KAAAE,YAAA,CAAAZ,IAAA,OAAA1N,YAAA,CAAAoO,SAAA,EAAAzL,QAAA;MACA;IACA;IAEA;IACA2L,YAAA,WAAAA,aAAAZ,IAAA,EAAAtO,IAAA;MACA,KAAAsO,IAAA,IAAAA,IAAA,CAAAtH,MAAA,SAAAhH,IAAA;QACA;MACA;;MAEA;MACA,IAAAmP,OAAA;QAAAC,OAAA;MACA,SAAA5E,CAAA,MAAAA,CAAA,GAAA8D,IAAA,CAAAtH,MAAA,EAAAwD,CAAA;QACA2E,OAAA,IAAAb,IAAA,CAAA9D,CAAA,EAAAmD,CAAA;QACAyB,OAAA,IAAAd,IAAA,CAAA9D,CAAA,EAAAoD,CAAA;MACA;MACAuB,OAAA,IAAAb,IAAA,CAAAtH,MAAA;MACAoI,OAAA,IAAAd,IAAA,CAAAtH,MAAA;;MAEA;MACA,KAAA5F,GAAA,CAAA2K,IAAA;;MAEA;MACA;MACA,IAAAsD,WAAA,QAAAlO,MAAA,CAAAwD,KAAA;MACA,IAAA2K,YAAA,QAAAnO,MAAA,CAAAwI,MAAA;;MAEA;MACA;MACA,IAAA4F,YAAA;;MAEA;MACA,KAAAnO,GAAA,CAAAoO,IAAA,WAAAnL,MAAA,CAAAkL,YAAA,SAAAlL,MAAA,MAAA5B,UAAA;MACA,KAAArB,GAAA,CAAAqO,SAAA;MACA,KAAArO,GAAA,CAAAsO,YAAA;;MAEA;MACA,IAAAC,SAAA,QAAAvO,GAAA,CAAAwO,WAAA,CAAA5P,IAAA,EAAA2E,KAAA;;MAEA;MACA,KAAAvD,GAAA,CAAAyO,WAAA;MACA,KAAAzO,GAAA,CAAA0O,UAAA;MACA,KAAA1O,GAAA,CAAA2O,aAAA;MACA,KAAA3O,GAAA,CAAA4O,aAAA;;MAEA;MACA,KAAA5O,GAAA,CAAAW,SAAA;MACA,KAAAX,GAAA,CAAA6O,QAAA,CAAAjQ,IAAA,EAAAmP,OAAA,EAAAC,OAAA;;MAEA;MACA,KAAAhO,GAAA,CAAA8O,OAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA/O,GAAA,EAAAuM,CAAA,EAAAC,CAAA,EAAAjJ,KAAA,EAAAgF,MAAA,EAAAyG,MAAA,EAAAC,SAAA,EAAAC,WAAA;MACA,WAAAF,MAAA;QACAA,MAAA;MACA;MACA,WAAAA,MAAA;QACAA,MAAA;UAAAG,EAAA,EAAAH,MAAA;UAAAI,EAAA,EAAAJ,MAAA;UAAAK,EAAA,EAAAL,MAAA;UAAAM,EAAA,EAAAN;QAAA;MACA;QACA,IAAAO,aAAA;UAAAJ,EAAA;UAAAC,EAAA;UAAAC,EAAA;UAAAC,EAAA;QAAA;QACA,SAAAE,IAAA,IAAAD,aAAA;UACAP,MAAA,CAAAQ,IAAA,IAAAR,MAAA,CAAAQ,IAAA,KAAAD,aAAA,CAAAC,IAAA;QACA;MACA;MAEAxP,GAAA,CAAAyM,SAAA;MACAzM,GAAA,CAAA0M,MAAA,CAAAH,CAAA,GAAAyC,MAAA,CAAAG,EAAA,EAAA3C,CAAA;MACAxM,GAAA,CAAA2M,MAAA,CAAAJ,CAAA,GAAAhJ,KAAA,GAAAyL,MAAA,CAAAI,EAAA,EAAA5C,CAAA;MACAxM,GAAA,CAAAyP,gBAAA,CAAAlD,CAAA,GAAAhJ,KAAA,EAAAiJ,CAAA,EAAAD,CAAA,GAAAhJ,KAAA,EAAAiJ,CAAA,GAAAwC,MAAA,CAAAI,EAAA;MACApP,GAAA,CAAA2M,MAAA,CAAAJ,CAAA,GAAAhJ,KAAA,EAAAiJ,CAAA,GAAAjE,MAAA,GAAAyG,MAAA,CAAAK,EAAA;MACArP,GAAA,CAAAyP,gBAAA,CAAAlD,CAAA,GAAAhJ,KAAA,EAAAiJ,CAAA,GAAAjE,MAAA,EAAAgE,CAAA,GAAAhJ,KAAA,GAAAyL,MAAA,CAAAK,EAAA,EAAA7C,CAAA,GAAAjE,MAAA;MACAvI,GAAA,CAAA2M,MAAA,CAAAJ,CAAA,GAAAyC,MAAA,CAAAM,EAAA,EAAA9C,CAAA,GAAAjE,MAAA;MACAvI,GAAA,CAAAyP,gBAAA,CAAAlD,CAAA,EAAAC,CAAA,GAAAjE,MAAA,EAAAgE,CAAA,EAAAC,CAAA,GAAAjE,MAAA,GAAAyG,MAAA,CAAAM,EAAA;MACAtP,GAAA,CAAA2M,MAAA,CAAAJ,CAAA,EAAAC,CAAA,GAAAwC,MAAA,CAAAG,EAAA;MACAnP,GAAA,CAAAyP,gBAAA,CAAAlD,CAAA,EAAAC,CAAA,EAAAD,CAAA,GAAAyC,MAAA,CAAAG,EAAA,EAAA3C,CAAA;MACAxM,GAAA,CAAA6M,SAAA;MAEA,IAAAoC,SAAA;QACAjP,GAAA,CAAAW,SAAA,GAAAsO,SAAA;QACAjP,GAAA,CAAA2N,IAAA;MACA;MAEA,IAAAuB,WAAA;QACAlP,GAAA,CAAAY,WAAA,GAAAsO,WAAA;QACAlP,GAAA,CAAAG,SAAA;QACAH,GAAA,CAAA4M,MAAA;MACA;IACA;IAEA;IACAlG,YAAA,WAAAA,aAAA;MACA,UAAA3G,MAAA,UAAAC,GAAA;QACA;MACA;;MAEA;MACA,KAAAA,GAAA,CAAAyK,SAAA,YAAA1K,MAAA,CAAAwD,KAAA,OAAAxD,MAAA,CAAAwI,MAAA;;MAEA;MACA,KAAAvI,GAAA,CAAA0I,SAAA,MAAApH,GAAA;;MAEA;MACA,SAAA8H,CAAA,MAAAA,CAAA,QAAAzJ,QAAA,CAAAiG,MAAA,EAAAwD,CAAA;QACA,KAAAmE,QAAA,MAAA5N,QAAA,CAAAyJ,CAAA;MACA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAAhD,CAAA;MACA,IAAA0B,KAAA;;MAEA;MACA,KAAAwF,QAAA,CAAAlH,CAAA;MAEA0B,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAtH,IAAA;MACA,IAAAsH,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAvH,aAAA,CAAAoF,MAAA;QACAmC,KAAA,CAAAvH,aAAA,CAAAoF,MAAA,GAAAmC,KAAA,CAAAtH,IAAA;MACA;MACAsH,KAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;MACA,KAAA6B,KAAA,gBAAAzC,KAAA,CAAApI,QAAA;IACA;IACA;IACA+P,OAAA,WAAAA,QAAArJ,CAAA,EAAAgG,MAAA,EAAAhB,EAAA,EAAAC,EAAA;MACA,IAAAvD,KAAA;MACA,IAAA4H,CAAA;MACA,IAAAtD,MAAA;QACA;QACAtE,KAAA,CAAAhI,MAAA,CAAAuN,WAAA,aAAAjH,CAAA;UACA0B,KAAA,CAAA6C,KAAA;UACA,IAAAgF,EAAA,GAAAvJ,CAAA,CAAAwJ,MAAA,GAAAxE,EAAA;UACA,IAAAyE,EAAA,GAAAzJ,CAAA,CAAA0J,MAAA,GAAAzE,EAAA;;UAEA;UACA,IAAAvD,KAAA,CAAAtH,IAAA;YACA,IAAAuK,SAAA,OAAAzJ,KAAA;YACAyJ,SAAA,CAAA9C,GAAA,GAAAH,KAAA,CAAAvH,aAAA,CAAAuH,KAAA,CAAAtH,IAAA;YACAsH,KAAA,CAAA/H,GAAA,CAAA0I,SAAA,CAAAsC,SAAA;UACA;UAEAjD,KAAA,CAAA/H,GAAA,CAAAyM,SAAA;UACA1E,KAAA,CAAA/H,GAAA,CAAAgQ,UAAA,CAAA3E,EAAA,EAAAC,EAAA,EAAAsE,EAAA,EAAAE,EAAA;UACA/H,KAAA,CAAA/H,GAAA,CAAAY,WAAA,GAAAmH,KAAA,CAAAnH,WAAA;UACAmH,KAAA,CAAA/H,GAAA,CAAAG,SAAA,GAAA4H,KAAA,CAAA5H,SAAA;UACA4H,KAAA,CAAA/H,GAAA,CAAA6M,SAAA;UACA9E,KAAA,CAAA/H,GAAA,CAAA4M,MAAA;QACA;MACA,WAAAP,MAAA;QACAtE,KAAA,CAAAoD,WAAA;QACA,IAAA8E,QAAA,GAAAC,WAAA;UACA,IAAAnI,KAAA,CAAArH,OAAA;YACAyP,aAAA,CAAAF,QAAA;YACAlI,KAAA,CAAArH,OAAA;UACA;YACA;UACA;UACA,IAAAkP,EAAA,GAAAvJ,CAAA,CAAAwJ,MAAA,GAAAxE,EAAA;UACA,IAAAyE,EAAA,GAAAzJ,CAAA,CAAA0J,MAAA,GAAAzE,EAAA;UACAvD,KAAA,CAAA/H,GAAA,CAAAyM,SAAA;UACA1E,KAAA,CAAA/H,GAAA,CAAAoQ,IAAA,CAAA/E,EAAA,EAAAC,EAAA,EAAAsE,EAAA,EAAAE,EAAA;UACA/H,KAAA,CAAA/H,GAAA,CAAAY,WAAA,GAAAmH,KAAA,CAAAnH,WAAA;UACAmH,KAAA,CAAA/H,GAAA,CAAAG,SAAA,GAAA4H,KAAA,CAAA5H,SAAA;UACA4H,KAAA,CAAA/H,GAAA,CAAA6M,SAAA;UACA9E,KAAA,CAAA/H,GAAA,CAAA4M,MAAA;UACA7E,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAtH,IAAA;UACA,IAAAsH,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAvH,aAAA,CAAAoF,MAAA;YACAmC,KAAA,CAAAvH,aAAA,CAAAoF,MAAA,GAAAmC,KAAA,CAAAtH,IAAA;UACA;UACAsH,KAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;UACAZ,KAAA,CAAAhI,MAAA,CAAAuN,WAAA;QACA;MACA;IACA;IAEA;IACA+C,SAAA,WAAAA,UAAAhK,CAAA,EAAAgG,MAAA;MACA,IAAAtE,KAAA;MACA,IAAAsE,MAAA;QACA;QACAtE,KAAA,CAAAuI,UAAA,GAAAjK,CAAA,CAAAwJ,MAAA;QACA9H,KAAA,CAAAwI,UAAA,GAAAlK,CAAA,CAAA0J,MAAA;QACAhI,KAAA,CAAA/H,GAAA,CAAAyM,SAAA;QACA1E,KAAA,CAAA/H,GAAA,CAAA0M,MAAA,CAAArG,CAAA,CAAAwJ,MAAA,EAAAxJ,CAAA,CAAA0J,MAAA;MACA,WAAA1D,MAAA;QACA;QACA,IAAAmE,GAAA,GAAAnK,CAAA,CAAAwJ,MAAA;QACA,IAAAY,GAAA,GAAApK,CAAA,CAAA0J,MAAA;QACA,IAAAW,KAAA;QACA,IAAAC,OAAA;QACA,IAAA5I,MAAA;QACA,IAAA6I,KAAA,QAAAN,UAAA;QACA,IAAAO,KAAA,QAAAN,UAAA;QACA;QACA,IAAAO,KAAA,GAAA5N,IAAA,CAAA6N,KAAA,CAAAF,KAAA,GAAAJ,GAAA,EAAAG,KAAA,GAAAJ,GAAA,UAAAtN,IAAA,CAAA8N,EAAA;QACA,IAAAC,MAAA,IAAAH,KAAA,GAAAJ,KAAA,IAAAxN,IAAA,CAAA8N,EAAA;QACA,IAAAE,MAAA,IAAAJ,KAAA,GAAAJ,KAAA,IAAAxN,IAAA,CAAA8N,EAAA;QACA,IAAAG,IAAA,GAAAR,OAAA,GAAAzN,IAAA,CAAAkO,GAAA,CAAAH,MAAA;QACA,IAAAI,IAAA,GAAAV,OAAA,GAAAzN,IAAA,CAAAoO,GAAA,CAAAL,MAAA;QACA,IAAAM,IAAA,GAAAZ,OAAA,GAAAzN,IAAA,CAAAkO,GAAA,CAAAF,MAAA;QACA,IAAAM,IAAA,GAAAb,OAAA,GAAAzN,IAAA,CAAAoO,GAAA,CAAAJ,MAAA;QACA,IAAAO,MAAA,GAAAb,KAAA,GAAAO,IAAA;QACA,IAAAO,MAAA,GAAAb,KAAA,GAAAQ,IAAA;QACAtJ,MAAA,CAAA/H,GAAA,CAAA0M,MAAA,CAAA+E,MAAA,EAAAC,MAAA;QACA3J,MAAA,CAAA/H,GAAA,CAAA0M,MAAA,CAAAkE,KAAA,EAAAC,KAAA;QACA9I,MAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAA6D,GAAA,EAAAC,GAAA;QACAgB,MAAA,GAAAjB,GAAA,GAAAW,IAAA;QACAO,MAAA,GAAAjB,GAAA,GAAAY,IAAA;QACAtJ,MAAA,CAAA/H,GAAA,CAAA0M,MAAA,CAAA+E,MAAA,EAAAC,MAAA;QACA3J,MAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAA6D,GAAA,EAAAC,GAAA;QACAgB,MAAA,GAAAjB,GAAA,GAAAe,IAAA;QACAG,MAAA,GAAAjB,GAAA,GAAAe,IAAA;QACAzJ,MAAA,CAAA/H,GAAA,CAAA2M,MAAA,CAAA8E,MAAA,EAAAC,MAAA;QACA3J,MAAA,CAAA/H,GAAA,CAAAY,WAAA,GAAAmH,MAAA,CAAAnH,WAAA;QACAmH,MAAA,CAAA/H,GAAA,CAAAG,SAAA,GAAA4H,MAAA,CAAA5H,SAAA;QACA4H,MAAA,CAAA/H,GAAA,CAAA4M,MAAA;QAEA7E,MAAA,CAAA/H,GAAA,CAAA6M,SAAA;QACA9E,MAAA,CAAAtH,IAAA,GAAAsH,MAAA,CAAAtH,IAAA;QACA,IAAAsH,MAAA,CAAAtH,IAAA,GAAAsH,MAAA,CAAAvH,aAAA,CAAAoF,MAAA;UACAmC,MAAA,CAAAvH,aAAA,CAAAoF,MAAA,GAAAmC,MAAA,CAAAtH,IAAA;QACA;QACAsH,MAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,MAAA,CAAAhI,MAAA,CAAA4I,SAAA;QACAZ,MAAA,CAAAhI,MAAA,CAAAuN,WAAA;MACA;IACA;IAEA;IACAqE,OAAA,WAAAA,QAAAtL,CAAA,EAAAgG,MAAA;MACA,IAAAtE,KAAA;MACA,IAAAsE,MAAA;QACA;QACAtE,KAAA,CAAA6J,eAAA;MACA,WAAAvF,MAAA;QACA,IAAAwF,MAAA;QACA,IAAA9J,KAAA,CAAA5G,QAAA;UACA0Q,MAAA,GAAA9J,KAAA,CAAA5G,QAAA;QACA;UACA0Q,MAAA,GAAA9J,KAAA,CAAA5G,QAAA;QACA;QAEA4G,KAAA,CAAA9H,IAAA,GAAAoG,CAAA,CAAAwJ,MAAA;QACA9H,KAAA,CAAA7H,IAAA,GAAAmG,CAAA,CAAA0J,MAAA,GAAA8B,MAAA;QAEA,IAAAhL,KAAA,QAAAiL,gBAAA,CAAAzL,CAAA;QACA0B,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAiI,IAAA,GAAAnL,KAAA,CAAA0F,CAAA;QACAxE,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAkI,GAAA,GAAApL,KAAA,CAAA2F,CAAA,GAAAzE,KAAA,CAAA5G,QAAA;QACA4G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAxR,KAAA;QACAwH,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAxB,MAAA,GAAAR,KAAA,CAAA5G,QAAA;QACA4G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAxG,KAAA,GACAwE,KAAA,CAAAhI,MAAA,CAAAwD,KAAA,GAAA8C,CAAA,CAAAwJ,MAAA,aACA9H,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAA5I,QAAA,GAAA4G,KAAA,CAAA5G,QAAA;QACA4G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAA1I,UAAA,GAAA0G,KAAA,CAAA1G,UAAA;QACA0G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAmI,KAAA,GAAAnK,KAAA,CAAA3G,SAAA;QACA2G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAoI,SAAA,GAAAjP,IAAA,CAAAkP,KAAA,CACA,CAAArK,KAAA,CAAAhI,MAAA,CAAAwD,KAAA,GAAA8C,CAAA,CAAAwJ,MAAA,IAAA9H,KAAA,CAAA5G,QACA;QACA4G,KAAA,CAAAjI,MAAA;QACA+E,UAAA;UACAkD,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAM,KAAA;QACA;MACA;IACA;IACA;IACA7I,OAAA,WAAAA,QAAA;MACA,IAAAzB,KAAA;MACA,IAAAgK,GAAA,GAAAhK,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAxR,KAAA;MACA,IAAAwR,GAAA;QACAhK,KAAA,CAAA/H,GAAA,CAAAoO,IAAA,GACArG,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAA5I,QAAA,GACA,MACA4G,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAA1I,UAAA;QACA0G,KAAA,CAAA/H,GAAA,CAAAW,SAAA,GAAAoH,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAhI,KAAA,CAAAmI,KAAA;QACAnK,KAAA,CAAA/H,GAAA,CAAA6O,QAAA,CAAAkD,GAAA,EAAAhK,KAAA,CAAA9H,IAAA,EAAA8H,KAAA,CAAA7H,IAAA;QACA6H,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAtH,IAAA;QACA,IAAAsH,KAAA,CAAAtH,IAAA,GAAAsH,KAAA,CAAAvH,aAAA,CAAAoF,MAAA;UACAmC,KAAA,CAAAvH,aAAA,CAAAoF,MAAA,GAAAmC,KAAA,CAAAtH,IAAA;QACA;QACAsH,KAAA,CAAAvH,aAAA,CAAAmG,IAAA,CAAAoB,KAAA,CAAAhI,MAAA,CAAA4I,SAAA;QACAZ,KAAA,CAAAhI,MAAA,CAAAuN,WAAA;MACA;IACA;IACA;IACAwE,gBAAA,WAAAA,iBAAAzL,CAAA;MACA,IAAAiM,EAAA,QAAAvS,MAAA;MACA,IAAAqI,OAAA,GAAAC,QAAA,CAAAkK,sBAAA;MACA;QACAhG,CAAA,EAAAlG,CAAA,CAAAwJ,MAAA,IAAAzH,OAAA,CAAA5E,WAAA,GAAA8O,EAAA,CAAA/O,KAAA;QACAiJ,CAAA,EAAAnG,CAAA,CAAA0J;MACA;IACA;IACA;IACAtG,QAAA,WAAAA,SAAA;MACA,IAAA1B,KAAA;MACAA,KAAA,CAAA1E,KAAA,CAAA0O,GAAA,CAAAxR,KAAA;MACAwH,KAAA,CAAAjI,MAAA;IACA;IACA0S,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,SAAA,GAAAxM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyM,SAAA,MAAA/S,gBAAA;;MAEA;MACA,IAAA8D,SAAA,QAAAZ,gBAAA,SAAAa,MAAA,CAAAC,KAAA,CAAAF,SAAA;;MAEA;MACA,IAAAkP,qBAAA,QAAApT,YAAA,CAAAqG,GAAA,WAAAgN,IAAA,EAAAhM,KAAA;QACA;UACAb,EAAA,EAAA6M,IAAA,CAAA7M,EAAA;UACAD,UAAA,EAAA8M,IAAA,CAAA1Q,QAAA;UAAA;UACAiE,SAAA,EAAAF,IAAA,CAAAyM,SAAA,CAAAD,SAAA,CAAA7L,KAAA;UAAA;UACAxE,MAAA,EAAAwQ,IAAA,CAAAxQ,MAAA;UAAA;UACAqB,SAAA,EAAAA,SAAA;QACA;MACA;;MAEA;MACA,IAAAkP,qBAAA,CAAAhN,MAAA;QACA,KAAAjB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAkO,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA5S,IAAA;MACA,GAAA8E,IAAA;QACA;QACAC,OAAA,CAAAC,GAAA,aAAAwN,qBAAA;;QAEA;QACAH,OAAA,CAAAxN,IAAA;UACA2N,qBAAA,EAAAA;QACA,GAAA1N,IAAA,WAAAjG,IAAA;UACAwT,OAAA,CAAA9N,QAAA,CAAAsO,OAAA;QACA,GAAAvN,KAAA,WAAAD,KAAA;UACAgN,OAAA,CAAA9N,QAAA,CAAAc,KAAA;QACA;MACA;IACA;IACAyN,QAAA,WAAAA,SAAA;MACA,IAAAC,IAAA;MACA,IAAA7R,GAAA,GAAA+G,QAAA,CAAA+K,cAAA;MACA,IAAAC,OAAA,OAAAjM,UAAA;MACAiM,OAAA,CAAA7L,aAAA,CAAAlG,GAAA,CAAAgS,KAAA;MAEAD,OAAA,CAAAhM,MAAA;QACA,IAAAkM,OAAA,QAAAhM,MAAA;QACA4L,IAAA,CAAAzT,OAAA,GAAA6T,OAAA;QACAJ,IAAA,CAAA5N,IAAA;MACA;IACA;IACAiO,iBAAA,WAAAA,kBAAAC,CAAA;MACA,KAAAlU,MAAA,GAAAkU,CAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAD,CAAA;MACA,KAAAvU,OAAA,CAAAK,MAAA,GAAAkU,CAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAzU,OAAA;QACA0U,OAAA;QACArU,MAAA;MACA;MACA,KAAAJ,QAAA;IACA;IACA0U,KAAA,WAAAA,MAAA;MAAA,IAAAC,OAAA;MACA,KAAAzQ,KAAA,CAAAnE,OAAA,CAAA6U,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAA1U,YAAA;UACA0U,OAAA,CAAA7O,IAAA,2BAAA6O,OAAA,CAAA5U,OAAA,EACAgG,IAAA,WAAAjG,IAAA;YACA6U,OAAA,CAAAnP,QAAA,CAAAsO,OAAA;YACAa,OAAA,CAAA3U,QAAA;UACA,GACA8U,OAAA;YACAH,OAAA,CAAA1U,YAAA;UACA;QACA;MACA;IACA;IACA8U,SAAA,WAAAA,UAAA;MACA;MACA,KAAA/U,QAAA;MACA,KAAAkE,KAAA,CAAAnE,OAAA,CAAAiV,WAAA;IACA;IACA;IACA9G,qBAAA,WAAAA,sBAAAD,KAAA;MACA,UAAArN,MAAA;QACA,OAAAqN,KAAA;MACA;MACA;QACAb,CAAA,EAAA6H,UAAA,EAAAhH,KAAA,CAAAb,CAAA,QAAAxM,MAAA,CAAAwD,KAAA,EAAA8Q,OAAA;QACA7H,CAAA,EAAA4H,UAAA,EAAAhH,KAAA,CAAAZ,CAAA,QAAAzM,MAAA,CAAAwI,MAAA,EAAA8L,OAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAlH,KAAA;MACA,UAAArN,MAAA;QACA,OAAAqN,KAAA;MACA;MACA;QACAb,CAAA,EAAArJ,IAAA,CAAAC,KAAA,CAAAiK,KAAA,CAAAb,CAAA,QAAAxM,MAAA,CAAAwD,KAAA;QACAiJ,CAAA,EAAAtJ,IAAA,CAAAC,KAAA,CAAAiK,KAAA,CAAAZ,CAAA,QAAAzM,MAAA,CAAAwI,MAAA;MACA;IACA;IAEA;IACAgM,oBAAA,WAAAA,qBAAAnH,KAAA;MACA,OAAAA,KAAA,CAAAb,CAAA,SAAAa,KAAA,CAAAb,CAAA,SAAAa,KAAA,CAAAZ,CAAA,SAAAY,KAAA,CAAAZ,CAAA;IACA;IAEA;IACAgI,wBAAA,WAAAA,yBAAApH,KAAA;MACA,SAAAmH,oBAAA,CAAAnH,KAAA;QACA,YAAAkH,qBAAA,CAAAlH,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IAEA;IACAqH,wBAAA,WAAAA,yBAAArH,KAAA;MACA,UAAAmH,oBAAA,CAAAnH,KAAA;QACA,YAAAC,qBAAA,CAAAD,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IAEA;IACA3G,eAAA,WAAAA,gBAAAiO,QAAA;MAAA,IAAAC,OAAA;MACA,KAAAD,QAAA,KAAAA,QAAA,CAAA9O,MAAA;QACA;MACA;;MAEA;MACA,IAAAgP,aAAA,GAAA1O,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyM,SAAA,CAAA+B,QAAA;;MAEA;MACA,IAAAG,SAAA,GAAAD,aAAA;MACA,IAAAC,SAAA,IAAAA,SAAA,CAAAjP,MAAA;QACA,IAAAkP,UAAA,GAAAD,SAAA;QACA,IAAAE,UAAA,QAAAR,oBAAA,CAAAO,UAAA;;QAEA;QACA,IAAAC,UAAA;UACA;UACA,KAAAnV,gBAAA,GAAAsG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyM,SAAA,CAAAiC,aAAA;UAEAA,aAAA,CAAAzL,OAAA,WAAA+D,IAAA;YACAA,IAAA,CAAA/D,OAAA,WAAAiE,KAAA,EAAAvG,KAAA;cACAqG,IAAA,CAAArG,KAAA,IAAA8N,OAAA,CAAAL,qBAAA,CAAAlH,KAAA;YACA;UACA;QACA;UACA;UACA,KAAAxN,gBAAA,GAAAgV,aAAA,CAAA/O,GAAA,WAAAqH,IAAA;YAAA,OACAA,IAAA,CAAArH,GAAA,WAAAuH,KAAA;cAAA,OAAAuH,OAAA,CAAAtH,qBAAA,CAAAD,KAAA;YAAA;UAAA,CACA;QACA;MACA;MAEA,OAAAwH,aAAA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,SAAAtV,QAAA,CAAAiG,MAAA;QACA;QACA,KAAAkN,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA5S,IAAA;UACA8U,wBAAA;UACA1S,OAAA;QASA,GAAA0C,IAAA;UACA;UACA,IAAAiQ,SAAA,GAAAF,OAAA,CAAAzV,YAAA,CACA8G,MAAA,WAAAuM,IAAA;YAAA,OAAAA,IAAA,CAAA7M,EAAA;UAAA;UAAA,CACAH,GAAA,WAAAgN,IAAA;YAAA,OAAAA,IAAA,CAAA7M,EAAA;UAAA;UAEA,IAAAmP,SAAA,CAAAvP,MAAA;YACA;YACAqP,OAAA,CAAAhQ,IAAA;cACAmQ,GAAA,EAAAD;YACA,GAAAjQ,IAAA,WAAAjG,IAAA;cACAgW,OAAA,CAAAtQ,QAAA,CAAAsO,OAAA;;cAEA;cACAgC,OAAA,CAAAtV,QAAA;cACAsV,OAAA,CAAArV,gBAAA;cACAqV,OAAA,CAAAzV,YAAA;;cAEA;cACAyV,OAAA,CAAAvO,YAAA;;cAEA;cACAuO,OAAA,CAAAzK,KAAA,gBAAAyK,OAAA,CAAAtV,QAAA;YACA,GAAA+F,KAAA,WAAAD,KAAA;cACAwP,OAAA,CAAAtQ,QAAA,CAAAc,KAAA;cACAN,OAAA,CAAAM,KAAA,cAAAA,KAAA;YACA;UACA;YACA;YACAwP,OAAA,CAAAtV,QAAA;YACAsV,OAAA,CAAArV,gBAAA;YACAqV,OAAA,CAAAzV,YAAA;;YAEA;YACAyV,OAAA,CAAAvO,YAAA;;YAEA;YACAuO,OAAA,CAAAzK,KAAA,gBAAAyK,OAAA,CAAAtV,QAAA;YAEAsV,OAAA,CAAAtQ,QAAA,CAAAsO,OAAA;UACA;QACA;MACA;QACA,KAAAtO,QAAA,CAAAmI,IAAA;MACA;IACA;IACA;IACArB,kBAAA,WAAAA,mBAAAlF,MAAA;MAAA,IAAA8O,OAAA;MACA,IAAA/I,UAAA;QAAAC,CAAA,EAAAhG,MAAA,CAAAgG,CAAA;QAAAC,CAAA,EAAAjG,MAAA,CAAAiG;MAAA;;MAEA;MAAA,IAAA8I,KAAA,YAAAA,MAAAlM,CAAA,EACA;QACA,IAAAiM,OAAA,CAAAtJ,gBAAA,CAAAO,UAAA,EAAA+I,OAAA,CAAA1V,QAAA,CAAAyJ,CAAA;UACA;UACAiM,OAAA,CAAAvC,QAAA,wCAAA7P,MAAA,CAAAmG,CAAA;YACA2J,iBAAA;YACAC,gBAAA;YACA5S,IAAA;UACA,GAAA8E,IAAA;YACA;YACAmQ,OAAA,CAAA1V,QAAA,CAAA4V,MAAA,CAAAnM,CAAA;YACAiM,OAAA,CAAAzV,gBAAA,CAAA2V,MAAA,CAAAnM,CAAA;;YAEA;YACA,IAAAiM,OAAA,CAAA7V,YAAA,CAAAoG,MAAA,GAAAwD,CAAA;cACAiM,OAAA,CAAA7V,YAAA,CAAA+V,MAAA,CAAAnM,CAAA;YACA;;YAEA;YACAiM,OAAA,CAAA7T,gBAAA;YACA6T,OAAA,CAAA3O,YAAA;;YAEA;YACA2O,OAAA,CAAA7K,KAAA,gBAAA6K,OAAA,CAAA1V,QAAA;YAEA0V,OAAA,CAAA1Q,QAAA,CAAAsO,OAAA,4BAAAhQ,MAAA,CAAAmG,CAAA;UACA;UAAA;QAEA;MACA;MA5BA,SAAAA,CAAA,MAAAA,CAAA,QAAAzJ,QAAA,CAAAiG,MAAA,EAAAwD,CAAA;QAAA,IAAAkM,KAAA,CAAAlM,CAAA,GA0BA;MAAA;IAGA;IAEA;IACA2C,gBAAA,WAAAA,iBAAAqB,KAAA,EAAAoI,OAAA;MACA,KAAAA,OAAA,IAAAA,OAAA,CAAA5P,MAAA;QACA;MACA;MAEA,IAAA6P,MAAA;MACA,SAAArM,CAAA,MAAAsM,CAAA,GAAAF,OAAA,CAAA5P,MAAA,MAAAwD,CAAA,GAAAoM,OAAA,CAAA5P,MAAA,EAAA8P,CAAA,GAAAtM,CAAA;QACA,IAAAuM,EAAA,GAAAH,OAAA,CAAApM,CAAA,EAAAmD,CAAA;UAAAqJ,EAAA,GAAAJ,OAAA,CAAApM,CAAA,EAAAoD,CAAA;QACA,IAAAqJ,EAAA,GAAAL,OAAA,CAAAE,CAAA,EAAAnJ,CAAA;UAAAuJ,EAAA,GAAAN,OAAA,CAAAE,CAAA,EAAAlJ,CAAA;QAEA,IAAAuJ,SAAA,GAAAH,EAAA,GAAAxI,KAAA,CAAAZ,CAAA,KAAAsJ,EAAA,GAAA1I,KAAA,CAAAZ,CAAA,IACAY,KAAA,CAAAb,CAAA,IAAAsJ,EAAA,GAAAF,EAAA,KAAAvI,KAAA,CAAAZ,CAAA,GAAAoJ,EAAA,KAAAE,EAAA,GAAAF,EAAA,IAAAD,EAAA;QAEA,IAAAI,SAAA;UACAN,MAAA,IAAAA,MAAA;QACA;MACA;MAEA,OAAAA,MAAA;IACA;IAGA;IACAO,aAAA,WAAAA,cAAAC,GAAA,EAAAC,MAAA,EAAAC,KAAA;MACA,IAAAtP,KAAA,QAAArH,YAAA,CAAAqO,OAAA,CAAAoI,GAAA;MACA,IAAApP,KAAA,SAAAA,KAAA,QAAAlH,QAAA,CAAAiG,MAAA;QACA,KAAApE,gBAAA,GAAAqF,KAAA;QACA,KAAAmF,mBAAA;MACA;IACA;IAEA;IACAoK,aAAA,WAAAA,cAAAH,GAAA,EAAAC,MAAA,EAAAC,KAAA;MACA,IAAAtP,KAAA,QAAArH,YAAA,CAAAqO,OAAA,CAAAoI,GAAA;MACA,IAAApP,KAAA,SAAAA,KAAA,QAAAlH,QAAA,CAAAiG,MAAA;QACA,KAAAjE,oBAAA,QAAAH,gBAAA;QACA,KAAAA,gBAAA,GAAAqF,KAAA;QACA,KAAAmF,mBAAA;MACA;IACA;IAEA;IACAqK,YAAA,WAAAA,aAAAJ,GAAA,EAAAC,MAAA,EAAAC,KAAA;MACA,KAAA3U,gBAAA,QAAAG,oBAAA;MACA,KAAA+E,YAAA;IACA;IAEA;IACA4P,iBAAA,WAAAA,kBAAAzP,KAAA;MAAA,IAAA0P,OAAA;MACA,IAAA1P,KAAA,SAAAA,KAAA,QAAAlH,QAAA,CAAAiG,MAAA;QACA;QACA,KAAAkN,QAAA,oCAAA7P,MAAA,MAAAzD,YAAA,CAAAqH,KAAA,EAAA1E,QAAA;UACA4Q,iBAAA;UACAC,gBAAA;UACA5S,IAAA;UACA8U,wBAAA;UACA1S,OAAA,kGAAAS,MAAA,CACA,KAAAzD,YAAA,CAAAqH,KAAA,EAAA1E,QAAA;QAQA,GAAA+C,IAAA;UACA;UACA,IAAAsR,QAAA,GAAAD,OAAA,CAAA/W,YAAA,CAAAqH,KAAA,EAAAb,EAAA;;UAEA;UACA,IAAAwQ,QAAA;YACAD,OAAA,CAAAtR,IAAA;cACAmQ,GAAA,GAAAoB,QAAA;YACA,GAAAtR,IAAA,WAAAjG,IAAA;cACAsX,OAAA,CAAA5R,QAAA,CAAAsO,OAAA;;cAEA;cACAsD,OAAA,CAAA5W,QAAA,CAAA4V,MAAA,CAAA1O,KAAA;cACA0P,OAAA,CAAA3W,gBAAA,CAAA2V,MAAA,CAAA1O,KAAA;cACA0P,OAAA,CAAA/W,YAAA,CAAA+V,MAAA,CAAA1O,KAAA;;cAEA;cACA0P,OAAA,CAAA/U,gBAAA;cACA+U,OAAA,CAAA7P,YAAA;;cAEA;cACA6P,OAAA,CAAA/L,KAAA,gBAAA+L,OAAA,CAAA5W,QAAA;YACA;UACA;YACA;YACA4W,OAAA,CAAA5W,QAAA,CAAA4V,MAAA,CAAA1O,KAAA;YACA0P,OAAA,CAAA3W,gBAAA,CAAA2V,MAAA,CAAA1O,KAAA;YACA0P,OAAA,CAAA/W,YAAA,CAAA+V,MAAA,CAAA1O,KAAA;;YAEA;YACA0P,OAAA,CAAA/U,gBAAA;YACA+U,OAAA,CAAA7P,YAAA;;YAEA;YACA6P,OAAA,CAAA/L,KAAA,gBAAA+L,OAAA,CAAA5W,QAAA;YAEA4W,OAAA,CAAA5R,QAAA,CAAAsO,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAhG,oBAAA,WAAAA,qBAAAwJ,KAAA,EAAAC,KAAA;MACA;MACA;;MAEA;MACA,SAAAtN,CAAA,MAAAA,CAAA,GAAAqN,KAAA,CAAA7Q,MAAA,EAAAwD,CAAA;QACA,SAAA2C,gBAAA,CAAA0K,KAAA,CAAArN,CAAA,GAAAsN,KAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAtN,GAAA,MAAAA,GAAA,GAAAsN,KAAA,CAAA9Q,MAAA,EAAAwD,GAAA;QACA,SAAA2C,gBAAA,CAAA2K,KAAA,CAAAtN,GAAA,GAAAqN,KAAA;UACA;QACA;MACA;;MAEA;MACA,SAAArN,GAAA,MAAAA,GAAA,GAAAqN,KAAA,CAAA7Q,MAAA,EAAAwD,GAAA;QACA,IAAAuN,EAAA,GAAAF,KAAA,CAAArN,GAAA;QACA,IAAAwN,EAAA,GAAAH,KAAA,EAAArN,GAAA,QAAAqN,KAAA,CAAA7Q,MAAA;QAEA,SAAA8P,CAAA,MAAAA,CAAA,GAAAgB,KAAA,CAAA9Q,MAAA,EAAA8P,CAAA;UACA,IAAAmB,EAAA,GAAAH,KAAA,CAAAhB,CAAA;UACA,IAAAoB,EAAA,GAAAJ,KAAA,EAAAhB,CAAA,QAAAgB,KAAA,CAAA9Q,MAAA;UAEA,SAAAmR,gBAAA,CAAAJ,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;YACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;MACA;MACA,IAAAC,GAAA,GAAAH,EAAA,CAAA1K,CAAA,GAAAyK,EAAA,CAAAzK,CAAA;MACA,IAAA8K,GAAA,GAAAJ,EAAA,CAAAzK,CAAA,GAAAwK,EAAA,CAAAxK,CAAA;;MAEA;MACA,IAAA8K,GAAA,GAAAH,EAAA,CAAA5K,CAAA,GAAA2K,EAAA,CAAA3K,CAAA;MACA,IAAAgL,GAAA,GAAAJ,EAAA,CAAA3K,CAAA,GAAA0K,EAAA,CAAA1K,CAAA;;MAEA;MACA,IAAAgL,WAAA,GAAAD,GAAA,GAAAH,GAAA,GAAAE,GAAA,GAAAD,GAAA;;MAEA;MACA,IAAAG,WAAA;QACA;MACA;;MAEA;MACA,IAAAC,GAAA,IAAAH,GAAA,IAAAN,EAAA,CAAAxK,CAAA,GAAA0K,EAAA,CAAA1K,CAAA,IAAA+K,GAAA,IAAAP,EAAA,CAAAzK,CAAA,GAAA2K,EAAA,CAAA3K,CAAA,KAAAiL,WAAA;MACA,IAAAE,GAAA,IAAAN,GAAA,IAAAJ,EAAA,CAAAxK,CAAA,GAAA0K,EAAA,CAAA1K,CAAA,IAAA6K,GAAA,IAAAL,EAAA,CAAAzK,CAAA,GAAA2K,EAAA,CAAA3K,CAAA,KAAAiL,WAAA;;MAEA;MACA,OAAAC,GAAA,SAAAA,GAAA,SAAAC,GAAA,SAAAA,GAAA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA/V,SAAA;MACA,KAAA8I,SAAA;IACA;IAEA;IACAkN,OAAA,WAAAA,QAAA;MACA,KAAAhW,SAAA;MACA,KAAA8I,SAAA;IACA;IAEA;IACAmN,SAAA,WAAAA,UAAA;MACA,KAAAjW,SAAA,QAAAC,gBAAA;MACA,KAAA6I,SAAA;IACA;IAEA;IACAA,SAAA,WAAAA,UAAA;MACA,UAAA3K,MAAA;QACA;MACA;;MAEA;MACA,KAAAA,MAAA,CAAAgK,KAAA,CAAA+N,SAAA,YAAA7U,MAAA,MAAArB,SAAA;MACA,KAAA7B,MAAA,CAAAgK,KAAA,CAAAgO,eAAA;;MAEA;MACA,KAAArR,YAAA;;MAEA;MACA,KAAApC,iBAAA;;MAEA;MACA;IACA;IAEA;IACA2E,OAAA,WAAAA,QAAA;MAAA,IAAA+O,OAAA;MACA,UAAAjY,MAAA,UAAAsD,KAAA,CAAAC,eAAA;QACA;MACA;MAEA,IAAAqE,SAAA,QAAAtE,KAAA,CAAAC,eAAA;MACA,IAAA2U,cAAA,GAAAtQ,SAAA,CAAAnE,WAAA;MACA,IAAA0U,eAAA,GAAA9T,MAAA,CAAA+T,WAAA;;MAEA,IAAAC,QAAA,QAAArY,MAAA,CAAAwD,KAAA,QAAAxD,MAAA,CAAAwI,MAAA;MACA,IAAA8P,cAAA,GAAAJ,cAAA,GAAAC,eAAA;MAEA,IAAAI,YAAA;MAEA,IAAAF,QAAA,GAAAC,cAAA;QACA;QACAC,YAAA,GAAAL,cAAA,QAAAlY,MAAA,CAAAwD,KAAA;MACA;QACA;QACA+U,YAAA,GAAAJ,eAAA,QAAAnY,MAAA,CAAAwI,MAAA;MACA;;MAEA;MACA+P,YAAA,GAAApV,IAAA,CAAA6F,GAAA,CAAAuP,YAAA;;MAEA;MACA;MACAA,YAAA,GAAApV,IAAA,CAAA2E,GAAA,CAAAyQ,YAAA,QAAAxW,iBAAA;MAEA,KAAAF,SAAA,GAAA0W,YAAA;MACA,KAAAzW,gBAAA,GAAAyW,YAAA;MACA,KAAA5N,SAAA;;MAEA;MACA,KAAA1B,SAAA;QACA,IAAA1F,eAAA,GAAA0U,OAAA,CAAA3U,KAAA,CAAAC,eAAA;QACA,IAAAA,eAAA;UACA;UACA,IAAA0U,OAAA,CAAAjY,MAAA,CAAAwY,WAAA,GAAAP,OAAA,CAAApW,SAAA,GAAA0B,eAAA,CAAAiV,WAAA;YACAP,OAAA,CAAAjY,MAAA,CAAAgK,KAAA,CAAAyO,UAAA;YACAR,OAAA,CAAAjY,MAAA,CAAAgK,KAAA,CAAA0O,WAAA;YACAT,OAAA,CAAAjY,MAAA,CAAAgK,KAAA,CAAAC,OAAA;UACA;QACA;MACA;;MAEA;IACA;IAEA;IACAwB,sBAAA,WAAAA,uBAAAnF,CAAA;MACA,UAAAtG,MAAA;QACA;UAAAwM,CAAA,EAAAlG,CAAA,CAAAwJ,MAAA;UAAArD,CAAA,EAAAnG,CAAA,CAAA0J;QAAA;MACA;;MAEA;MACA,IAAAK,IAAA,QAAArQ,MAAA,CAAA2Y,qBAAA;MACA,IAAAC,MAAA,QAAA5Y,MAAA,CAAAwD,KAAA,GAAA6M,IAAA,CAAA7M,KAAA;MACA,IAAAqV,MAAA,QAAA7Y,MAAA,CAAAwI,MAAA,GAAA6H,IAAA,CAAA7H,MAAA;;MAEA;MACA,IAAAgE,CAAA,IAAAlG,CAAA,CAAAwS,OAAA,GAAAzI,IAAA,CAAA4B,IAAA,IAAA2G,MAAA;MACA,IAAAnM,CAAA,IAAAnG,CAAA,CAAAyS,OAAA,GAAA1I,IAAA,CAAA6B,GAAA,IAAA2G,MAAA;MAEA;QAAArM,CAAA,EAAAA,CAAA;QAAAC,CAAA,EAAAA;MAAA;IACA;IAEA;IACAuM,eAAA,WAAAA,gBAAA;MACA,KAAAhX,aAAA,SAAAA,aAAA;MACA,KAAA2E,YAAA;MACA,KAAA/B,QAAA,CAAAsO,OAAA,MAAAlR,aAAA;IACA;IAEA;IACAiX,uBAAA,WAAAA,wBAAAC,OAAA;MACA,IAAAC,SAAA,GAAA9E,UAAA,CAAA6E,OAAA;MACA,KAAAE,KAAA,CAAAD,SAAA;QACA,KAAApX,iBAAA,GAAAoX,SAAA;QACA,KAAAvU,QAAA,CAAAsO,OAAA,oDAAAhQ,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAA+V,SAAA;QACA,KAAAjQ,OAAA;MACA;IACA;IAEA;IACAmQ,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhW,KAAA,CAAAnB,QAAA,CAAA6R,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,IAAAqF,OAAA,CAAA3W,YAAA;UACA2W,OAAA,CAAApX,eAAA;;UAEA;UACAoX,OAAA,CAAA1Z,QAAA,CAAAgH,IAAA,CAAA0S,OAAA,CAAA3W,YAAA,CAAAwK,IAAA;UACAmM,OAAA,CAAAzZ,gBAAA,CAAA+G,IAAA,CAAA0S,OAAA,CAAA3W,YAAA,CAAAyK,YAAA;;UAEA;UACAkM,OAAA,CAAA7Z,YAAA,CAAAmH,IAAA;YACAxE,QAAA,EAAAkX,OAAA,CAAAnX,QAAA,CAAAC,QAAA;YACAC,QAAA,EAAAiX,OAAA,CAAAnX,QAAA,CAAAE,QAAA;YACAC,MAAA,EAAAgX,OAAA,CAAAnX,QAAA,CAAAG;UACA;;UAEA;UACAgX,OAAA,CAAA3W,YAAA;;UAEA;UACAmC,UAAA;YACAwU,OAAA,CAAApX,eAAA;YACAoX,OAAA,CAAArX,eAAA;YACAqX,OAAA,CAAA1U,QAAA,CAAAsO,OAAA;YACA;YACAoG,OAAA,CAAA3S,YAAA;UACA;QACA;MACA;IACA;IAEA;IACA4S,oBAAA,WAAAA,qBAAA;MACA,SAAA5W,YAAA;QACA;QACA,KAAAoI,OAAA;QACA,KAAApI,YAAA;MACA;MACA,KAAAV,eAAA;IACA;EACA;AACA"}]}