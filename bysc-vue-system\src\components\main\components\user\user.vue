<template>
  <div>
    <el-badge style="float:right;margin-right:20px" :max="99" :value="msgCount" class="item">
      <i @click="goToMsgpage" style="font-size:28px;color:#f2f2f2;position: relative;top:5px;margin-left:18px;left:-6px" class="el-icon-bell"></i>
    </el-badge>
    <span style="color:#f2f2f2;margin-left: 16px;float:right">{{ userInfo.realName.substring(0,9) }}{{userInfo.realName.length>10?'...':''}}</span>
    <el-dialog :modal="false" title="重置密码" :visible.sync="resetSelfDialog" width="30%">
      <div>
        <el-form ref="pwdform" label-width="80px" :model="formInline">
          <el-form-item prop="oldPassword" label="旧密码" :rules="[{ required: true, message: '请输入旧密码', trigger: 'blur' }]">
            <el-input v-model="formInline.oldPassword" style="width:100%" placeholder="请输入旧密码"></el-input>
          </el-form-item>
          <el-form-item prop="newPassword" label="新密码" :rules="[{ required: true, message: '请输入新密码', trigger: 'blur' }]">
            <el-input v-model="formInline.newPassword" style="width:100%" placeholder="请输入新密码"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resetSelfPwd">确 定</el-button>
      </span>
    </el-dialog>
    <el-avatar style="position:relative;top:14px;left: 10px;float:right" v-if="userAvatar" :src="userAvatar" ></el-avatar>
    <el-avatar style="position:relative;top:14px;left: 10px;float:right" v-if="!userAvatar" >{{ userInfo.realName }}</el-avatar>
    <el-dropdown style="float:right">
      <span class="el-dropdown-link" style="color: #ffffff !important">
        <img src="../../../../assets/qhxt.png" style="width:24px;position:relative;top:8px;left:-6px">
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item,index) in sysLists" :key="index" @click.native="goToNewSystem(item)">
          <img :src="item.appIconUrl" style="width:20px;position:relative;top:5px;">
           {{item.name}}
        </el-dropdown-item>
        <div style="width:150px;height:30px" v-if="!sysLists.length"></div>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- <el-dropdown>
        <i style="font-size:28px;color:#f2f2f2;position: relative;top:5px;left:10px;margin-right:6px" class="el-icon-setting"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="changeSelfPwd">修改密码</el-dropdown-item>
          <el-dropdown-item @click.native="handleClickLogout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    <!-- <i style="font-size:28px;color:#f2f2f2;position: relative;top:5px;margin-left:8px;" @click="handleClickLogout" class="el-icon-switch-button"></i> -->
  </div>
</template>

<script>
import './user.less';
// eslint-disable-next-line no-unused-vars
import {mapState, mapActions} from 'vuex';
import notice from './notice.vue';
export default {
  name: 'User',
  components: {notice},
  props: {
    messageUnreadCount: {
      type: Number,
      default: 0
    },
    userAvatar: {
      type: String,
      default: ''
    },
    userName: {
      type: String,
      default: ''
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    },
    imgUrl() {
      return this.$href + (this.userInfo && this.userInfo.headImg);
    }
  },
  watch: {
    resetSelfDialog(val) {
      if (!val) {
        this.formInline = {
          oldPassword: '',
          newPassword: ''
        };
      }
    }
  },
  data() {
    return {
      sysUrlLists: {
        system: require("../../../../assets/xtgl.png"),
        workflow: require("../../../../assets/spzx.png"),
        message: require("../../../../assets/xxgl.png"),
        hr: require("../../../../assets/rlgl.png"),
        asset: require("../../../../assets/sbzcgl.png"),
        share: require("../../../../assets/sjfxzs.png"),
        finance: require("../../../../assets/cwxt.png"),
        material: require("../../../../assets/clgl.png"),
        contract: require("../../../../assets/htgl.png"),
        produce: require("../../../../assets/scyx.png"),
        quality: require("../../../../assets/zlaq.png"),
        workorder: require("../../../../assets/gdgl.png"),
        pdm: require("../../../../assets/wlw.png"),
        scr1: require("../../../../assets/sjdp.png"),
        scr2: require("../../../../assets/wlwdp.png"),
      },
      sysLists: [],
      msgCount: 0,
      formInline: {
        newPassword: '',
        oldPassword: ''
      },
      resetSelfDialog: false,
      showNoticeIcon: false,
      headImg: '',
      noticeTimer: null,
      noticeTimeout: null,
      showIcon: true,
      allkeys: {
        contract: 'contract',
        asset: 'asset',
        material: 'material',
        produce: 'produce',
        workorder: 'workorder',
        finance: 'finance',
        hr: 'hr',
        scr1: 'screenManage',
        pdm: 'pdm',
        scr2: 'InternetOfThingsScreen',
        share: 'share',
        workflow: 'workflow',
        message: 'msgCenter',
        system: 'system/cfg'
      }
    };
  },
  mounted() {
    this.getMsgCount();
  },
  created() {
    this.$api["systems/my-granted-apps"]({}).then(data => {
      this.sysLists = data.filter(e => {
        return e.key != "system";
      });
    });
  },
  methods: {
    ...mapActions(['handleLogOut', 'onLogout']),
    // 跳转人资源
    goToHumanResources() {
      this.$api["systems/getManpowerUrl"]({}).then(data => {
        window.open(data, 'manpower');
      });
    },
    gosimpasurl() {
      this.$api["systems/sim-pas-url"]({}).then(data => {
        window.open(data, 'finance');
      });
    },
    goToNewSystem(item) {
      this.$api['account/getToken']({grant_type: 'refresh_token', refresh_token: this.$cookies.get('refreshToken')}).then(data => {
        this.$cookies.set('userToken', data.token);
        this.$cookies.set('refreshToken', data.refreshToken);
        this.$cookies.set('userExpir', data.exp);
        let location = item.location;
        setTimeout(() => {
          if (item.key == 'hr') {
            this.goToHumanResources();
          } else if (item.key == 'share') {
            let protocol = item.protocol;
            let host = item.host;
            let port = item.port;
            let url = protocol + "://" + host + ":" + port + (location ? location : '') + "/emc/admin?tk="
          + this.$cookies.get("userToken");
            window.open(url, item.key);
          } else if (item.key == 'finance') {
            this.gosimpasurl();
          } else {
            let path = this.$store.state.common.menuList.filter(row => {
              return row.resourcePageName == this.allkeys[item.key];
            });
            var pathconcat = '';
            if (path.length) {
              pathconcat = path[0].children[0].resourcePath;
            } else {
              pathconcat = '/system/cfg';
            }
            let protocol = item.protocol;
            let host = item.host;
            let port = item.port;
            let url = protocol + "://" + host + ":" + port + (location ? location : '') + "/social?path=" + pathconcat + "&tk="
          + this.$cookies.get("userToken");
            window.open(url, item.key);
          }
        }, 100);
      });
    },
    getMsgCount() {
      this.$api['systems/get-unread-num']({}).then(data => {
        this.msgCount = Number(data);
      });
    },
    goToMsgpage() {
      this.$api['systems/get-by-key']({key: "MODULE_JUMP"}).then(data => {
        console.log(JSON.parse(data), '这是什么数据');
        let alldata = JSON.parse(data);
        alldata.forEach(item => {
          if (item.key == 'message') {
            let protocol = item.protocol;
            let host = item.host;
            let port = item.port;
            let location = item.location;
            let url = protocol + "://" + host + ":" + port + (location ? location : '') + "/social?path=/msgCenter/notification&tk="
              + this.$localCache.getLocal("userToken");
            window.open(url, item.key);
          }
        });
      });
    },
    twinkle() {
      if (this.noticeTimer) {
        clearInterval(this.noticeTimer);
        this.noticeTimer = null;
      }
      this.noticeTimer = setInterval(() => {
        this.showIcon = !this.showIcon;
      }, 300);
      this.noticeTimeout = setTimeout(() => {
        if (!this.showIcon) {
          this.showIcon = true;
        }
        clearInterval(this.noticeTimer);
        this.noticeTimer = null;
        clearTimeout(this.noticeTimeout);
        this.noticeTimeout = null;
      }, 3600);
    },
    changeSelfPwd() {
      this.resetSelfDialog = true;
    },
    resetSelfPwd() {
      this.$refs.pwdform.validate(valid => {
        if (valid) {
          if (this.formInline.newPassword.trim() === this.formInline.oldPassword.trim()) {
            this.$message.error('新密码不能与旧密码一致');
          } else if (this.formInline.newPassword.trim().length < 8 || this.formInline.oldPassword.trim().length < 8) {
            this.$message.error('密码长度必须8位及以上');
          } else {
            this.$api['systems/changeSelfPwd'](this.formInline).then(data => {
              this.resetSelfDialog = false;
              this.$localCache.removeLocal('userToken');
              this.$localCache.removeLocal('refreshToken');
              this.$alert('密码已重置，请重新登录', '重置成功', {
                confirmButtonText: '确定',
                callback: action => {
                  location.href = '/adminLogin';
                }
              });
            });
          }
        }
      });
    },
    toLogout() {
      this.onLogout();
    },
    logout() {
      this.handleLogOut().then(() => {
        location.href = '/adminLogin';
        this.$localCache.removeLocal('userToken');
        this.$localCache.removeLocal('refreshToken');
      });
    },
    showNotice() {
      this.$refs.notice.showDrawer = !this.$refs.notice.showDrawer;
    },
    getUserInfo() {},
    message() {
      this.$router.push({
        name: 'msg_readstatus'
      });
    },
    editPass() {
      this.$router.push({
        name: 'editPassword'
      });
    },
    personalCenter() {
      this.$router.push({
        name: 'personalCenter'
      });
    },
    handleClickLogout(name) {
      this.$confirm('您确定要退出登录吗？', '退出登录', {
        type: 'warning',
      })
        .then(_ => {
          this.logout();
        }).catch(err => {

        });
    }
  }
};
</script>
<style lang="less" scoped>
.item {
  margin-right: 20px;
}
/deep/.el-badge__content{
  top: 20px;
  right: 15px;
}
.item {
  margin-right: 10px;
}
/deep/.ivu-badge-count{
  position: absolute;
  top:7px;
}

.platformTitleGeneral {
  display: inline-block;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  right: 10px;
  top:3px
}
.logoutIcon {
  position: relative !important;
  top: 0.5vh;
  opacity: 0.9;
}
.iconStyle{
  position: relative;
  top: 5px;
  left: -5px;
  color: #fff;
}
</style>
