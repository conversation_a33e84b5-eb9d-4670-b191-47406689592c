{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\drawingManagement\\index.vue", "mtime": 1754296173293}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport DrawingFormDialog from './components/DrawingFormDialog.vue';\r\n\r\nimport _ from 'lodash';\r\n\r\nconst defaultSearchForm = {\r\n  drawingCode: '',\r\n  drawingName: '',\r\n  belongDept: ''\r\n};\r\n\r\nexport default {\r\n  name: 'DrawingManagement',\r\n  components: {\r\n    Grid,\r\n    DrawingFormDialog\r\n  },\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n\r\n    return {\r\n      deptList: [], // 部门tree\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '图纸编号',\r\n          key: 'drawingCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '图纸名称',\r\n          key: 'drawingName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '所属部门',\r\n          key: 'belongDept',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '创建人',\r\n          key: 'creatorName',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      tableData: [\r\n\r\n      ],\r\n      // 弹窗相关数据\r\n      dialogVisible: false,\r\n      dialogMode: 'add', // add: 新增, edit: 编辑\r\n      editData: {},\r\n\r\n      // 区域管理抽屉相关数据\r\n      areaDrawerVisible: false,\r\n      currentDrawingData: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getInit();\r\n  },\r\n  methods: {\r\n    handleAraeMange(row) {\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaBinding', {\r\n        drawingId: row.id,\r\n        // drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n    // 新增图纸\r\n    getInit() {\r\n      this.$api[\"visualOpsManagement/workorderFirstLineDept-findAll\"]({}).then(res => {\r\n        this.deptList = res;\r\n      });\r\n    },\r\n    handleAdd() {\r\n      this.dialogMode = 'add';\r\n      this.editData = {};\r\n      this.dialogVisible = true;\r\n    },\r\n    // 查看详情\r\n    handleView(row) {\r\n      console.log('查看区域:', row);\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaManagement', {\r\n        drawingId: row.id,\r\n        // drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogMode = 'edit';\r\n      this.editData = {...row};\r\n      this.dialogVisible = true;\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该图纸吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        console.log('删除:', row);\r\n        this.$message.success('删除成功');\r\n        this.searchTable();\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 下载全部附件\r\n    handleDownload(row) {\r\n      console.log('下载全部附件:', row);\r\n      this.$message.info('下载功能待开发');\r\n    },\r\n    // 区域管理\r\n    handleMange(row) {\r\n      console.log('区域管理:', row);\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaManagement', {\r\n        drawingId: row.id,\r\n        drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n\r\n    // 手动作废\r\n    handleCancel(row) {\r\n      this.$confirm('确认作废该图纸吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        console.log('手动作废:', row);\r\n        this.$message.success('作废成功');\r\n        this.searchTable();\r\n      }).catch(() => {\r\n        this.$message.info('已取消作废');\r\n      });\r\n    },\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.queryData();\r\n    },\r\n    // 重置表格\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    // 获取列配置\r\n    getColumn(e) {\r\n      this.columns = e;\r\n      setTimeout(() => {\r\n        this.$refs.table.doLayout();\r\n      }, 100);\r\n    },\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n\r\n    // 处理弹窗保存\r\n    handleDialogSave(formData) {\r\n      // 刷新表格\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    // 处理弹窗关闭\r\n    handleDialogClose() {\r\n      this.dialogVisible = false;\r\n      this.editData = {};\r\n    },\r\n\r\n    // 处理区域管理抽屉关闭\r\n    handleAreaDrawerClose() {\r\n      this.areaDrawerVisible = false;\r\n      this.currentDrawingData = {};\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    }\r\n  }\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoHA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/visualOpsManagement/drawingManagement", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"visualOpsManagement/visualOpsManagement-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          :auto-load=\"true\"\r\n          ref=\"grid\">\r\n          <div slot=\"search\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n              <el-form-item label=\"图纸编号\">\r\n                <el-input\r\n                  v-model.trim=\"searchForm.drawingCode\"\r\n                  size=\"small\"\r\n                  maxlength=\"32\"\r\n                  placeholder=\"请输入\"\r\n                  clearable\r\n                  style=\"width: 200px\">\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"图纸名称\">\r\n                <el-input\r\n                  v-model.trim=\"searchForm.drawingName\"\r\n                  size=\"small\"\r\n                  maxlength=\"32\"\r\n                  placeholder=\"请输入\"\r\n                  clearable\r\n                  style=\"width: 200px\">\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"所属部门\">\r\n\r\n\r\n                    <el-select style=\"width:100%\" v-model=\"searchForm.belongDeptKey\" placeholder=\"请选择\" size=\"small\" filterable clearable>\r\n                  <el-option :label=\"i.name\" :value=\"i.id\" v-for=\"i in deptList\" :key=\"i.id\">\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button size=\"small\" type=\"primary\" style=\"margin: 0 0 0 10px\" @click=\"searchTable\">查询</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button v-permission=\"'drawingManagement_add'\" type=\"primary\" size=\"small\" @click=\"handleAdd\">\r\n              <i class=\"el-icon-plus\"></i> 新增图纸\r\n            </el-button>\r\n          </div>\r\n          <el-table\r\n            slot=\"table\"\r\n            slot-scope=\"{loading}\"\r\n            v-loading=\"loading\"\r\n            :data=\"tableData\"\r\n            stripe\r\n            ref=\"table\"\r\n            style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.key == 'belongDept'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.minWidth ? item.minWidth : '150'\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.belongDept || '未分配' }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\">\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"250\">\r\n              <template slot-scope=\"scope\">\r\n                                <el-button v-permission=\"'drawingManagement_areDrawing'\" type=\"text\" size=\"small\" @click=\"handleMange(scope.row)\">区域绘制</el-button>\r\n                <el-button v-permission=\"'drawingManagement_areaManage'\" type=\"text\" size=\"small\" @click=\"handleAraeMange(scope.row)\">区域管理</el-button>\r\n                <el-button v-permission=\"'drawingManagement_edit'\" type=\"text\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n                <el-button v-permission=\"'drawingManagement_view'\" type=\"text\" size=\"small\" @click=\"handleView(scope.row)\">查看区域</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 新增/编辑图纸弹窗 -->\r\n    <DrawingFormDialog\r\n      :visible.sync=\"dialogVisible\"\r\n      :mode=\"dialogMode\"\r\n      :edit-data=\"editData\"\r\n      @save=\"handleDialogSave\"\r\n      @close=\"handleDialogClose\">\r\n    </DrawingFormDialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport DrawingFormDialog from './components/DrawingFormDialog.vue';\r\n\r\nimport _ from 'lodash';\r\n\r\nconst defaultSearchForm = {\r\n  drawingCode: '',\r\n  drawingName: '',\r\n  belongDept: ''\r\n};\r\n\r\nexport default {\r\n  name: 'DrawingManagement',\r\n  components: {\r\n    Grid,\r\n    DrawingFormDialog\r\n  },\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n\r\n    return {\r\n      deptList: [], // 部门tree\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '图纸编号',\r\n          key: 'drawingCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '图纸名称',\r\n          key: 'drawingName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '所属部门',\r\n          key: 'belongDept',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '创建人',\r\n          key: 'creatorName',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      tableData: [\r\n\r\n      ],\r\n      // 弹窗相关数据\r\n      dialogVisible: false,\r\n      dialogMode: 'add', // add: 新增, edit: 编辑\r\n      editData: {},\r\n\r\n      // 区域管理抽屉相关数据\r\n      areaDrawerVisible: false,\r\n      currentDrawingData: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getInit();\r\n  },\r\n  methods: {\r\n    handleAraeMange(row) {\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaBinding', {\r\n        drawingId: row.id,\r\n        // drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n    // 新增图纸\r\n    getInit() {\r\n      this.$api[\"visualOpsManagement/workorderFirstLineDept-findAll\"]({}).then(res => {\r\n        this.deptList = res;\r\n      });\r\n    },\r\n    handleAdd() {\r\n      this.dialogMode = 'add';\r\n      this.editData = {};\r\n      this.dialogVisible = true;\r\n    },\r\n    // 查看详情\r\n    handleView(row) {\r\n      console.log('查看区域:', row);\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaManagement', {\r\n        drawingId: row.id,\r\n        // drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogMode = 'edit';\r\n      this.editData = {...row};\r\n      this.dialogVisible = true;\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该图纸吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        console.log('删除:', row);\r\n        this.$message.success('删除成功');\r\n        this.searchTable();\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n    // 下载全部附件\r\n    handleDownload(row) {\r\n      console.log('下载全部附件:', row);\r\n      this.$message.info('下载功能待开发');\r\n    },\r\n    // 区域管理\r\n    handleMange(row) {\r\n      console.log('区域管理:', row);\r\n      // 使用新的导航工具，自动保存参数\r\n      const {navigateWithParams} = require('@/utils/routeParams');\r\n      navigateWithParams(this.$router, 'areaManagement', {\r\n        drawingId: row.id,\r\n        drawingName: row.name || row.drawingName\r\n      });\r\n    },\r\n\r\n    // 手动作废\r\n    handleCancel(row) {\r\n      this.$confirm('确认作废该图纸吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        console.log('手动作废:', row);\r\n        this.$message.success('作废成功');\r\n        this.searchTable();\r\n      }).catch(() => {\r\n        this.$message.info('已取消作废');\r\n      });\r\n    },\r\n    // 搜索表格\r\n    searchTable() {\r\n      this.$refs.grid.queryData();\r\n    },\r\n    // 重置表格\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    // 获取列配置\r\n    getColumn(e) {\r\n      this.columns = e;\r\n      setTimeout(() => {\r\n        this.$refs.table.doLayout();\r\n      }, 100);\r\n    },\r\n    // 获取表格数据\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n\r\n    // 处理弹窗保存\r\n    handleDialogSave(formData) {\r\n      // 刷新表格\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    // 处理弹窗关闭\r\n    handleDialogClose() {\r\n      this.dialogVisible = false;\r\n      this.editData = {};\r\n    },\r\n\r\n    // 处理区域管理抽屉关闭\r\n    handleAreaDrawerClose() {\r\n      this.areaDrawerVisible = false;\r\n      this.currentDrawingData = {};\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.el-select {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}