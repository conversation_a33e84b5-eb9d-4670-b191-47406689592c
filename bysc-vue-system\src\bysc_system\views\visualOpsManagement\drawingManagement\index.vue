<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="visualOpsManagement/visualOpsManagement-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          :auto-load="true"
          ref="grid">
          <div slot="search">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
              <el-form-item label="图纸编号">
                <el-input
                  v-model.trim="searchForm.drawingCode"
                  size="small"
                  maxlength="32"
                  placeholder="请输入"
                  clearable
                  style="width: 200px">
                </el-input>
              </el-form-item>
              <el-form-item label="图纸名称">
                <el-input
                  v-model.trim="searchForm.drawingName"
                  size="small"
                  maxlength="32"
                  placeholder="请输入"
                  clearable
                  style="width: 200px">
                </el-input>
              </el-form-item>
              <el-form-item label="所属部门">


                    <el-select style="width:100%" v-model="searchForm.belongDeptKey" placeholder="请选择" size="small" filterable clearable>
                  <el-option :label="i.name" :value="i.id" v-for="i in deptList" :key="i.id">
                  </el-option>
                </el-select>

              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" style="margin: 0 0 0 10px" @click="searchTable">查询</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'drawingManagement_add'" type="primary" size="small" @click="handleAdd">
              <i class="el-icon-plus"></i> 新增图纸
            </el-button>
          </div>
          <el-table
            slot="table"
            slot-scope="{loading}"
            v-loading="loading"
            :data="tableData"
            stripe
            ref="table"
            style="width: 100%">
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.key == 'belongDept'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'">
                <template slot-scope="scope">
                  <div>
                    {{ scope.row.belongDept || '未分配' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'">
              </el-table-column>
            </template>
            <el-table-column fixed="right" align="center" label="操作" type="action" width="300">
              <template slot-scope="scope">
                                <el-button v-permission="'drawingManagement_areDrawing'" type="text" size="small" @click="handleMange(scope.row)">区域绘制</el-button>
                <el-button v-permission="'drawingManagement_areaManage'" type="text" size="small" @click="handleAraeMange(scope.row)">区域管理</el-button>
                <el-button v-permission="'drawingManagement_edit'" type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button v-permission="'drawingManagement_view'" type="text" size="small" @click="handleView(scope.row)">查看区域</el-button>
                <el-button v-permission="'drawingManagement_delete'" type="text" size="small" style="color: #f56c6c;" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>

    <!-- 新增/编辑图纸弹窗 -->
    <DrawingFormDialog
      :visible.sync="dialogVisible"
      :mode="dialogMode"
      :edit-data="editData"
      @save="handleDialogSave"
      @close="handleDialogClose">
    </DrawingFormDialog>

  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import DrawingFormDialog from './components/DrawingFormDialog.vue';

import _ from 'lodash';

const defaultSearchForm = {
  drawingCode: '',
  drawingName: '',
  belongDept: ''
};

export default {
  name: 'DrawingManagement',
  components: {
    Grid,
    DrawingFormDialog
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();

    return {
      deptList: [], // 部门tree
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '图纸编号',
          key: 'drawingCode',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '图纸名称',
          key: 'drawingName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '所属部门',
          key: 'belongDept',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '创建人',
          key: 'creatorName',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 150,
        }
      ],
      tableData: [

      ],
      // 弹窗相关数据
      dialogVisible: false,
      dialogMode: 'add', // add: 新增, edit: 编辑
      editData: {},

      // 区域管理抽屉相关数据
      areaDrawerVisible: false,
      currentDrawingData: {}
    };
  },
  created() {
    this.getInit();
  },
  methods: {
    handleAraeMange(row) {
      // 使用新的导航工具，自动保存参数
      const {navigateWithParams} = require('@/utils/routeParams');
      navigateWithParams(this.$router, 'areaBinding', {
        drawingId: row.id,
        // drawingName: row.name || row.drawingName
      });
    },
    // 新增图纸
    getInit() {
      this.$api["visualOpsManagement/workorderFirstLineDept-findAll"]({}).then(res => {
        this.deptList = res;
      });
    },
    handleAdd() {
      this.dialogMode = 'add';
      this.editData = {};
      this.dialogVisible = true;
    },
    // 查看详情
    handleView(row) {
      console.log('查看区域:', row);
      // 使用新的导航工具，自动保存参数
      const {navigateWithParams} = require('@/utils/routeParams');
      navigateWithParams(this.$router, 'areaManagement', {
        drawingId: row.id,
        // drawingName: row.name || row.drawingName
      });
    },
    // 编辑
    handleEdit(row) {
      this.dialogMode = 'edit';
      this.editData = {...row};
      this.dialogVisible = true;
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该图纸吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除:', row);
        this.$message.success('删除成功');
        this.searchTable();
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 下载全部附件
    handleDownload(row) {
      console.log('下载全部附件:', row);
      this.$message.info('下载功能待开发');
    },
    // 区域管理
    handleMange(row) {
      console.log('区域管理:', row);
      // 使用新的导航工具，自动保存参数
      const {navigateWithParams} = require('@/utils/routeParams');
      navigateWithParams(this.$router, 'areaManagement', {
        drawingId: row.id,
        drawingName: row.name || row.drawingName
      });
    },

    // 手动作废
    handleCancel(row) {
      this.$confirm('确认作废该图纸吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('手动作废:', row);
        this.$message.success('作废成功');
        this.searchTable();
      }).catch(() => {
        this.$message.info('已取消作废');
      });
    },
    // 搜索表格
    searchTable() {
      this.$refs.grid.queryData();
    },
    // 重置表格
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    // 获取列配置
    getColumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    // 获取表格数据
    getDatas(e) {
      this.tableData = e;
    },

    // 处理弹窗保存
    handleDialogSave(formData) {
      // 刷新表格
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.dialogVisible = false;
      this.editData = {};
    },

    // 处理区域管理抽屉关闭
    handleAreaDrawerClose() {
      this.areaDrawerVisible = false;
      this.currentDrawingData = {};
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
};
</script>

<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
