{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue?vue&type=template&id=562b2716&scoped=true", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\techDashboard\\techData\\index.vue", "mtime": 1754276220628}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752725561194}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\n<div class=\"table-container\">\n  <Grid\n    api=\"techDashboard/techManagementRatingDesign-page\"\n    :event-bus=\"searchEventBus\"\n    :search-params=\"searchForm\"\n    :newcolumn=\"columns\"\n    @datas=\"getDatas\"\n    @columnChange=\"getcolumn\"\n    ref=\"grid\"\n  >\n    <div slot=\"search\">\n      <!-- <el-date-picker\n        style=\"width: 200px; margin: 0 10px 0 0\"\n        v-model=\"searchForm.date\"\n        type=\"month\"\n        value-format=\"yyyy-MM\"\n        placeholder=\"选择月份\"\n      ></el-date-picker>\n      <el-button\n        size=\"small\"\n        type=\"primary\"\n        style=\"margin: 0 0 0 10px\"\n        @click=\"searchTable\"\n      >搜索</el-button>\n      <el-button size=\"small\" @click=\"resetTable\">重置</el-button> -->\n    </div>\n\n    <el-table\n      slot=\"table\"\n      slot-scope=\"{ loading }\"\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      stripe\n      style=\"width: 100%\"\n      :border=\"true\"\n    >\n      <el-table-column\n        fixed=\"left\"\n        :align=\"'center'\"\n        label=\"序号\"\n        type=\"index\"\n        width=\"50\"\n      >\n      </el-table-column>\n      <el-table-column\n        v-for=\"item in columns\"\n        :key=\"item.key\"\n        :show-overflow-tooltip=\"true\"\n        :prop=\"item.key\"\n        :label=\"item.title\"\n        :min-width=\"item.width ? item.width : '150'\"\n        :align=\"item.align ? item.align : 'center'\"\n      >\n      </el-table-column>\n      <el-table-column\n        fixed=\"right\"\n        align=\"center\"\n        label=\"操作\"\n        width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            v-permission=\"'techEditBtn'\"\n            @click=\"handleEdit(scope.row)\"\n          >编辑</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n  </Grid>\n\n  <Panel ref=\"panel\" @refresh=\"searchTable\" />\n</div>\n", null]}