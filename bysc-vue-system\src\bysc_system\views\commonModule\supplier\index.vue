<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="commonModule/materialSupplier-pageinfo"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          :auto-load="true"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="供应商编码">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.supplierNo"
                  size="small"
                  maxlength="32"
                  placeholder="请输入供应商编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="供应商名称">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.supplierName"
                  size="small"
                  maxlength="32"
                  placeholder="请输入供应商名称"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'supplier_sync'" size="small" type="primary" @click="handleSyncData"
              >同步数据</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" stripe ref="table" style="width: 100%">

            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.minWidth ? item.minWidth : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'supplier_bind'"
                  @click="handleBind(scope.row)"
                  type="text"
                  size="small"
                  :disabled="isSupplierList"
                  >绑定</el-button
                >
                <el-button
                  v-permission="'supplier_edit'"
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  v-permission="'supplier_del'"
                  @click="handleDelete(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <div style="width: 100%; padding: 0 10px;margin-bottom:100px;">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="110px"
          class="demo-form"
        >
          <div style="margin:0 0 10px 0;">
            <el-row>
              <el-col :span="12">
                <el-form-item label="名称" prop="supplierName">
                  <el-input size="small" v-model.trim="form.supplierName" placeholder="请输入名称" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input size="small" v-model.trim="form.contactPerson" placeholder="请输入联系人" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="邮编" prop="postcode">
                  <el-input size="small" v-model.trim="form.postcode" placeholder="请输入邮编" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="主营业务" prop="mainBusiness">
                  <el-input size="small" v-model.trim="form.mainBusiness" placeholder="请输入主营业务" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="人员情况" prop="staffProfile">
                  <el-input size="small" v-model.trim="form.staffProfile" placeholder="请输入人员情况" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法人" prop="legalPerson">
                  <el-input size="small" v-model.trim="form.legalPerson" placeholder="请输入法人" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="电话" prop="tel">
                  <el-input size="small" v-model.trim="form.tel" placeholder="请输入电话" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="税号" prop="taxNo">
                  <el-input size="small" v-model.trim="form.taxNo" placeholder="请输入税号" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="企业概况" prop="companyProfile">
                  <el-input size="small" v-model.trim="form.companyProfile" placeholder="请输入企业概况" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地址" prop="addr">
                  <el-input size="small" v-model.trim="form.addr" placeholder="请输入地址" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="传真" prop="fax">
                  <el-input size="small" v-model.trim="form.fax" placeholder="请输入传真" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户银行" prop="depositBank">
                  <el-input size="small" v-model.trim="form.depositBank" placeholder="请输入开户银行" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="企业级别" prop="enterpriseRank">
                  <el-input size="small" v-model.trim="form.enterpriseRank" placeholder="请输入企业级别" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业类别" prop="enterpriseType">
                  <el-select
                    v-model="form.enterpriseType"
                    placeholder="请选择企业类别"
                    clearable
                    style="width: 100%;"
                    size="small"
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in enterpriseTypeList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input size="small" v-model.trim="form.email" placeholder="请输入电子邮箱" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户账号" prop="depositAccount">
                  <el-input size="small" v-model.trim="form.depositAccount" placeholder="请输入开户账号" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="企业等级" prop="enterpriseLevel">
                  <el-input size="small" v-model.trim="form.enterpriseLevel" placeholder="请输入企业等级" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            :loading="okLoading"
            @click="submitForm"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <el-dialog :title="'绑定太极供应商'" :visible.sync="supplierDialog" :close-on-click-modal="false" width="40%">
      <div>
        <el-form
          :model="supplierForm"
          :rules="supplierFormRules"
          ref="supplierForm"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="供应商" prop="taijiId">
                <el-select
                  v-model="taijiId"
                  placeholder="请选择供应商"
                  clearable
                  filterable
                  style="width: 100%; margin: 0 10px 0 0"
                  size="small"
                  value-key="id"
                  @change="changeTaijiId"
                >
                  <el-option
                    v-for="(item, index) in supplierList"
                    :key="index"
                    :label="item.counterPartnerName"
                    :value="item"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="supplierDialog = false"
          >取 消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :loading="supplierOkLoading"
          @click="submitSupplierForm"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultForm = {};
export default {
  components: {
    Grid,
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    const validateTaijiId = (rule, value, callback) => {
      if (!this.taijiId) {
        callback(new Error('请选择绑定绑定供应商'));
      } else {
        callback();
      }
    };
    return {
      isSupplierList: true,
      taijiId: null,
      supplierList: [],
      supplierFormRules: {
        taijiId: [
          {required: true, validator: validateTaijiId, trigger: 'blur,change'},
        ],
      },
      supplierForm: {},
      supplierDialog: false,
      supplierOkLoading: false,

      enterpriseTypeList: [],
      form: _.cloneDeep(defaultForm),
      okLoading: false,
      rules: {
        supplierName: [
          {required: true, message: '请输入名称', trigger: 'blur'},
        ],
        contactPerson: [
          {required: true, message: '请输入联系人', trigger: 'blur'},
        ],
        postcode: [
          {required: true, message: '请输入邮编', trigger: 'blur'},
        ],
        mainBusiness: [
          {required: true, message: '请输入主营业务', trigger: 'blur'},
        ],
        legalPerson: [
          {required: true, message: '请输入法人', trigger: 'blur'},
        ],
        tel: [
          {required: true, message: '请输入电话', trigger: 'blur'},
        ],
        taxNo: [
          {required: true, message: '请输入税号', trigger: 'blur'},
        ],
        companyProfile: [
          {required: true, message: '请输入企业概况', trigger: 'blur'},
        ],
        addr: [
          {required: true, message: '请输入地址', trigger: 'blur'},
        ],
        fax: [
          {required: true, message: '请输入传真', trigger: 'blur'},
        ],
        depositBank: [
          {required: true, message: '请输入开户银行', trigger: 'blur'},
        ],
        enterpriseType: [
          {required: true, message: '请选择企业类别', trigger: 'blur'},
        ],
        email: [
          {required: true, message: '请输入电子邮箱', trigger: 'blur'},
        ],
        depositAccount: [
          {required: true, message: '请输入开户账号', trigger: 'blur'},
        ],
      },
      drawerTitle: '新增',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '太极Id',
          key: 'counterId',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '太极供应商名称',
          key: 'counterPartnerName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '组织',
          key: 'orgcodeName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '供应商编码',
          key: 'supplierNo',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '供应商名称',
          key: 'supplierName',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 170,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
    this.getSupplierList();
    this.getEnterpriseTypeList();
  },
  methods: {
    getEnterpriseTypeList() {
      this.$api["sysDict/getParam"]({id: "QIYELEIBIE"}).then(data => {
        this.enterpriseTypeList = [];
        data.forEach(e => {
          this.enterpriseTypeList.push({
            label: e.dictName,
            value: e.dictCode,
          });
        });
      });
    },
    getSupplierList() {
      this.$api["commonModule/contractCounterPartner-list"]({}).then(data => {
        this.supplierList = data;
        this.isSupplierList = false;
      }).catch(() => {
        this.isSupplierList = false;
      });
    },
    handleSyncData() {
      this.$confirm('确定同步数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$api['commonModule/KingdeeSupplier-save']({}).then(data => {
          loading.close();
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '同步数据成功',
          });
        }).catch(() => {
          loading.close();
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消同步数据'
        });
      });
    },
    handleEdit(row) {
      this.$api['commonModule/materialSupplier-get']({id: row.id}).then(data => {
        this.form = data;
        this.drawerTitle = '编辑';
        this.drawer = true;
      });
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['commonModule/materialSupplier-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    submitForm() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.$api['commonModule/materialSupplier-suppliersave'](this.form).then(data => {
            this.drawer = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.form = _.cloneDeep(defaultForm);
      this.$refs.form.resetFields();
      this.drawer = false;
    },
    searchTable() {
      this.$refs.grid.queryData();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },

    handleBind(row) {
      if (row.counterId) {
        let arr = this.supplierList.filter(e => {
          return e.id == row.counterId;
        });
        this.taijiId = arr[0];
        this.supplierForm = {
          supplierId: row.id,
          id: row.relId ? row.relId : null,
          taijiId: this.taijiId.id,
          taijiName: this.taijiId.counterPartnerName
        };
        this.supplierDialog = true;
      } else {
        this.taijiId = null;
        this.supplierForm = {
          supplierId: row.id,
          id: row.relId ? row.relId : null,
          taijiId: '',
          taijiName: ''
        };
        this.supplierDialog = true;
      }
    },
    changeTaijiId(e) {
      this.supplierForm.taijiId = e.id;
      this.supplierForm.taijiName = e.counterPartnerName;
    },
    submitSupplierForm() {
      let that = this;
      this.$refs.supplierForm.validate(valid => {
        if (valid) {
          this.supplierOkLoading = true;
          this.$api['commonModule/materialSupplierTaijiRel-save'](this.supplierForm).then(data => {
            this.supplierDialog = false;
            this.supplierOkLoading = false;
            this.$message({
              type: 'success',
              message: '操作成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.supplierOkLoading = false;
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
  z-index: 100;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
