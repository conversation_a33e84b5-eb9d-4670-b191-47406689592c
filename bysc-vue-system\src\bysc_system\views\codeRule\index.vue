<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="codeRule/baseCodeRule-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getcolumn"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="规则编码">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.ruleCode"
                  size="small"
                  placeholder="请输入规则编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="规则名称">
                <el-input
                  style="width: 200px; margin: 0 10px 0 0"
                  v-model.trim="searchForm.ruleName"
                  size="small"
                  placeholder="请输入规则名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="启用状态">
                <el-select
                  v-model="searchForm.enable"
                  placeholder="请选择启用状态"
                  clearable
                  style="width: 200px; margin: 0 10px 0 0"
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in enableList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button
                >
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'codeRule_add'" size="small" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" :data="tableData" ref="table" stripe style="width: 100%">
            <el-table-column fixed="left" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot == 'ruleCode'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <a @click="handleCodeRuleSection(scope.row)">{{scope.row[item.slot]}}</a>
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot == 'replenish'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{scope.row[item.slot]?'是':'否'}}
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot == 'enable'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  {{scope.row[item.slot]?'是':'否'}}
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="170"
            >
              <template slot-scope="scope">
                <el-button
                  v-permission="'codeRule_edit'"
                  @click="handleEdit(scope.row)"
                  type="text"
                  size="small"
                  >编辑</el-button
                >
                <el-button
                  v-permission="'codeRule_del'"
                  @click="handleDelete(scope.row)"
                  type="text"
                  size="small"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerTitle"
      :visible.sync="drawer"
      :direction="direction"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-width="100px"
          class="demo-form"
        >
          <el-form-item label="规则编码" prop="ruleCode">
            <el-input size="small" v-model.trim="form.ruleCode" placeholder="请输入规则编码" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="规则名称" prop="ruleName">
            <el-input size="small" v-model.trim="form.ruleName" placeholder="请输入规则名称" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="depict">
            <el-input size="small" v-model.trim="form.depict" placeholder="请输入描述" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="是否补齐" prop="replenish">
            <el-radio-group v-model="form.replenish">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否启用" prop="enable">
            <el-radio-group v-model="form.enable">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否拼接" prop="montage">
            <el-radio-group v-model="form.montage">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              size="small"
              placeholder="请输入备注"
              maxlength="200"
              type="textarea"
              v-model.trim="form.remark"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer-footer">
          <el-button size="small" @click="closeDrawer"
            >取消</el-button
          >
          <el-button
            size="small"
            type="primary"
            @click="submitForm"
            :loading="okLoading"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {};
const defaultForm = {
  ruleCode: '',
  ruleName: '',
  depict: '',
  replenish: 1,
  enable: 1,
  montage: 1,
  remark: '',
};
export default {
  components: {
    Grid
  },
  destroyed() {
    this.searchEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    return {
      enableList: [
        {
          label: '是',
          value: '1'
        },
        {
          label: '否',
          value: '0'
        }
      ],
      form: _.cloneDeep(defaultForm),
      okLoading: false,
      rules: {
        ruleCode: [
          {required: true, message: '请输入规则编码', trigger: 'blur'},
          // {
          //   min: 1,
          //   max: 15,
          //   message: '长度在 1 到 15 个字符',
          //   trigger: 'blur',
          // },
        ],
        ruleName: [
          {required: true, message: '请输入规则名称', trigger: 'blur'},
        ],
      },
      detailDrawer: false,
      drawerTitle: '新增编码规则',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '规则编码',
          slot: 'ruleCode',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '规则名称',
          key: 'ruleName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '是否补齐',
          slot: 'replenish',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '是否可用',
          slot: 'enable',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '备注',
          key: 'remark',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
    };
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    handleAdd() {
      this.form = _.cloneDeep(defaultForm);
      this.drawerTitle = '新增编码规则';
      this.drawer = true;
    },
    handleEdit(row) {
      this.$api['codeRule/baseCodeRule-get']({id: row.id}).then(data => {
        this.drawerTitle = '编辑编码规则';
        this.drawer = true;
        this.form = data;
      });
    },
    submitForm() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          this.okLoading = true;
          this.$api['codeRule/baseCodeRule-save'](this.form).then(data => {
            this.drawer = false;
            this.okLoading = false;
            this.$message({
              type: 'success',
              message: '保存成功',
            });
            this.$nextTick(() => {
              this.$refs.grid.query();
            });
          }).catch(() => {
            that.okLoading = false;
          });
        } else {
          return false;
        }
      });
    },
    closeDrawer() {
      this.form = _.cloneDeep(defaultForm);
      this.$refs.form.resetFields();
      this.drawer = false;
    },
    handleDelete(e) {
      this.$confirm('确定删除该数据？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['codeRule/baseCodeRule-delete']({id: e.id}).then(data => {
          this.$refs.grid.query();
          this.$message({
            type: 'success',
            message: '删除成功',
          });
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.valueDate = '';
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getcolumn(e) {
      this.columns = e;
      setTimeout(() => {
        this.$refs.table.doLayout();
      }, 100);
    },
    getDatas(e) {
      this.tableData = e;
    },
    handleCodeRuleSection(row) {
      let {name, params, query} = {};
      name = 'codeRuleSectionTable';
      query = {id: row.id};
      this.$router.push({
        name,
        params,
        query,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.demo-drawer-footer{
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
/deep/.el-input-number .el-input__inner {
  text-align: left;
}
</style>
