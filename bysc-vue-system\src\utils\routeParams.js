/**
 * 路由参数持久化工具
 * 解决页面刷新后参数丢失的问题
 */

/**
 * 保存路由参数到localStorage和URL
 * @param {Object} router - Vue Router实例
 * @param {Object} route - 当前路由对象
 * @param {Object} params - 要保存的参数对象
 * @param {string} storageKey - localStorage的键名
 */
export function saveRouteParams(router, route, params, storageKey = 'routeParams') {
  // 保存到localStorage
  const existingParams = getStoredParams(storageKey) || {};
  const updatedParams = { ...existingParams, ...params };
  localStorage.setItem(storageKey, JSON.stringify(updatedParams));
  
  // 更新URL查询参数
  const currentQuery = { ...route.query };
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      currentQuery[key] = params[key];
    }
  });
  
  // 如果URL参数有变化，则更新URL
  if (JSON.stringify(currentQuery) !== JSON.stringify(route.query)) {
    router.replace({
      query: currentQuery
    });
  }
}

/**
 * 获取路由参数（优先从URL获取，其次从localStorage）
 * @param {Object} route - 当前路由对象
 * @param {string|Array} paramNames - 参数名称（字符串或数组）
 * @param {string} storageKey - localStorage的键名
 * @returns {Object|string} 参数值
 */
export function getRouteParams(route, paramNames, storageKey = 'routeParams') {
  const storedParams = getStoredParams(storageKey) || {};
  
  if (typeof paramNames === 'string') {
    // 单个参数
    return route.query[paramNames] || 
           route.params[paramNames] || 
           storedParams[paramNames];
  } else if (Array.isArray(paramNames)) {
    // 多个参数
    const result = {};
    paramNames.forEach(name => {
      result[name] = route.query[name] || 
                    route.params[name] || 
                    storedParams[name];
    });
    return result;
  }
  
  return null;
}

/**
 * 从localStorage获取存储的参数
 * @param {string} storageKey - localStorage的键名
 * @returns {Object|null} 存储的参数对象
 */
export function getStoredParams(storageKey = 'routeParams') {
  try {
    const stored = localStorage.getItem(storageKey);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('Error parsing stored route params:', error);
    return null;
  }
}

/**
 * 清除存储的路由参数
 * @param {string|Array} paramNames - 要清除的参数名称（可选，不传则清除所有）
 * @param {string} storageKey - localStorage的键名
 */
export function clearRouteParams(paramNames = null, storageKey = 'routeParams') {
  if (!paramNames) {
    // 清除所有参数
    localStorage.removeItem(storageKey);
  } else {
    // 清除指定参数
    const storedParams = getStoredParams(storageKey) || {};
    const names = Array.isArray(paramNames) ? paramNames : [paramNames];
    
    names.forEach(name => {
      delete storedParams[name];
    });
    
    if (Object.keys(storedParams).length === 0) {
      localStorage.removeItem(storageKey);
    } else {
      localStorage.setItem(storageKey, JSON.stringify(storedParams));
    }
  }
}

/**
 * Vue Mixin - 为组件提供路由参数持久化功能
 */
export const RouteParamsMixin = {
  methods: {
    /**
     * 保存当前页面的路由参数
     * @param {Object} params - 要保存的参数
     * @param {string} storageKey - 存储键名（可选）
     */
    saveCurrentRouteParams(params, storageKey) {
      const key = storageKey || `${this.$route.name}_params`;
      saveRouteParams(this.$router, this.$route, params, key);
    },
    
    /**
     * 获取当前页面的路由参数
     * @param {string|Array} paramNames - 参数名称
     * @param {string} storageKey - 存储键名（可选）
     */
    getCurrentRouteParams(paramNames, storageKey) {
      const key = storageKey || `${this.$route.name}_params`;
      return getRouteParams(this.$route, paramNames, key);
    },
    
    /**
     * 清除当前页面的路由参数
     * @param {string|Array} paramNames - 要清除的参数名称（可选）
     * @param {string} storageKey - 存储键名（可选）
     */
    clearCurrentRouteParams(paramNames, storageKey) {
      const key = storageKey || `${this.$route.name}_params`;
      clearRouteParams(paramNames, key);
    }
  }
};

/**
 * 页面导航时自动保存参数的工具函数
 * @param {Object} router - Vue Router实例
 * @param {string} routeName - 目标路由名称
 * @param {Object} params - 路由参数
 * @param {Object} options - 其他选项
 */
export function navigateWithParams(router, routeName, params = {}, options = {}) {
  const { query = {}, replace = false, storageKey } = options;
  
  // 保存参数到localStorage
  const key = storageKey || `${routeName}_params`;
  localStorage.setItem(key, JSON.stringify(params));
  
  // 合并查询参数
  const finalQuery = { ...query, ...params };
  
  // 导航
  const navigationMethod = replace ? 'replace' : 'push';
  router[navigationMethod]({
    name: routeName,
    query: finalQuery
  });
}
