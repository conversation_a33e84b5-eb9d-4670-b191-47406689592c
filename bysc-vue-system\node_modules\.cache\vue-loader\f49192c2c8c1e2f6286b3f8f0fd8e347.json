{"remainingRequest": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\src\\bysc_system\\views\\visualOpsManagement\\deviceBinding\\index.vue", "mtime": 1754276220638}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752725551995}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752725545295}, {"path": "D:\\boweiWorkSpace\\pc\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752725555216}], "contextDependencies": [], "result": ["\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport BindPositionDialog from './component/BindPositionDialog';\r\nimport {getRouteParams, saveRouteParams, navigateWithParams} from '@/utils/routeParams';\r\n\r\n\r\n\r\nexport default {\r\n  name: 'DeviceBinding',\r\n  components: {\r\n    Grid,\r\n    BindPositionDialog\r\n  },\r\n  destroyed() {\r\n    this.assetLocationSearchEventBus.$off();\r\n    this.assetDeviceSearchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.assetLocationSearchEventBus = new Vue();\r\n    this.assetDeviceSearchEventBus = new Vue();\r\n\r\n    return {\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      activeTab: 'assetLocation', // 当前激活的Tab\r\n      reginId: '', // 区域ID\r\n      drawingId: '', // 图纸ID\r\n\r\n      // 资产位置搜索参数\r\n      assetLocationSearchParams: {\r\n        reginId: ''\r\n      },\r\n      // 资产设备搜索参数\r\n      assetDeviceSearchParams: {\r\n        locationIds: [] // 已绑定的位置ID数组，每次获取资产设备时都会传递最新的位置ID\r\n      },\r\n\r\n      // 资产位置相关数据\r\n      assetLocationColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产位置',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetLocationTableData: [],\r\n\r\n      // 资产设备相关数据\r\n      assetDeviceColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'assetCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'recordStatus',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '所属一线部门',\r\n          key: 'firstDeptName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产描述',\r\n          key: 'assetDesc',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '规格',\r\n          key: 'specification',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '是否主设备',\r\n          key: 'mainDeviceName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '关联主设备',\r\n          key: 'relatedMainDeviceCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置编码',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置描述',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '安装位置',\r\n          key: 'installationLocation',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetDeviceTableData: []\r\n    };\r\n  },\r\n  created() {\r\n    // 使用工具函数获取参数\r\n    const params = getRouteParams(this.$route, ['reginId', 'drawingId'], 'deviceBinding_params');\r\n\r\n    if (params.reginId) {\r\n      this.reginId = params.reginId;\r\n      this.assetLocationSearchParams.reginId = params.reginId;\r\n      console.log('获取到 reginId 参数:', params.reginId);\r\n\r\n      // 获取已绑定的位置ID\r\n      this.getBindedLocationIds();\r\n    } else {\r\n      console.log('未获取到 reginId 参数');\r\n      this.$message.warning('缺少区域ID参数，请返回列表重新选择');\r\n    }\r\n\r\n    if (params.drawingId) {\r\n      this.drawingId = params.drawingId;\r\n      console.log('获取到 drawingId 参数:', params.drawingId);\r\n    } else {\r\n      console.log('未获取到 drawingId 参数');\r\n    }\r\n\r\n    // 保存参数，确保刷新后不丢失\r\n    if (params.reginId || params.drawingId) {\r\n      saveRouteParams(this.$router, this.$route, {\r\n        reginId: params.reginId,\r\n        drawingId: params.drawingId\r\n      }, 'deviceBinding_params');\r\n    }\r\n\r\n    this.getInit();\r\n  },\r\n  watch: {\r\n    // 监听 Tab 切换\r\n    activeTab(newTab) {\r\n      if (newTab === 'assetDevice' && this.reginId) {\r\n        // 切换到资产设备 Tab 时，重新获取位置ID并加载数据\r\n        this.getBindedLocationIds();\r\n      }\r\n    },\r\n\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      // 获取当前页面的参数\r\n      const drawingId = this.drawingId || this.$route.query.drawingId;\r\n\r\n      // 使用导航工具返回到区域绑定页面，并传递参数\r\n      if (drawingId) {\r\n        navigateWithParams(this.$router, 'areaBinding', {\r\n          drawingId: drawingId\r\n        });\r\n      } else {\r\n        // 如果没有参数，直接跳转\r\n        this.$router.push('/system/visualOpsManagement/areaBinding');\r\n      }\r\n    },\r\n\r\n    openDialog(row) {\r\n      // Handle case where row might be a click event or undefined\r\n      // Check if row is an event object (has type property) or not a valid data object\r\n      if (!row || row instanceof Event || row.type === 'click' || row.constructor.name.includes('Event')) {\r\n        this.currentRow = {};\r\n      } else {\r\n        this.currentRow = row;\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    handleSuccess() {\r\n      this.$message.success('绑定成功');\r\n      // 刷新资产位置列表\r\n      this.$refs.assetLocationGrid.queryData();\r\n      // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n      this.getBindedLocationIds();\r\n    },\r\n    handleCancel() {\r\n      console.log('用户取消操作');\r\n    },\r\n    refreshList() {\r\n      // 刷新数据列表\r\n    },\r\n\r\n    // 初始化数据\r\n    getInit() {\r\n      // 初始化相关数据\r\n    },\r\n\r\n    // 获取已绑定的位置ID\r\n    getBindedLocationIds() {\r\n      if (!this.reginId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['visualOpsManagement/maintenanceRegionLocationRel-get']({\r\n        reginId: this.reginId\r\n      }).then(res => {\r\n        console.log('获取已绑定位置ID结果:', res);\r\n        if (res && res.length > 0) {\r\n          // 提取位置ID数组\r\n          this.assetDeviceSearchParams.locationIds = res;\r\n        } else {\r\n          this.assetDeviceSearchParams.locationIds = [0];\r\n          console.log('该区域暂无绑定的位置');\r\n        }\r\n\r\n        // 获取位置ID后，手动触发资产设备表格数据加载\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        console.error('获取已绑定位置ID失败:', error);\r\n        this.assetDeviceSearchParams.locationIds = [0];\r\n        this.$message.error('获取已绑定位置信息失败');\r\n\r\n        // 即使失败也要触发表格加载，显示空数据\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    // 刷新资产设备数据（确保使用最新的位置ID）\r\n    refreshAssetDeviceData() {\r\n      console.log('刷新资产设备数据，重新获取最新的位置ID');\r\n      this.getBindedLocationIds();\r\n    },\r\n\r\n    // ========== 资产位置相关方法 ==========\r\n    // 资产位置 - 取消绑定\r\n    handleAssetLocationBind(row) {\r\n      this.$confirm('确认取消绑定该资产位置吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 调用取消绑定接口\r\n        this.$api['visualOpsManagement/assetLocation-delete']({\r\n          id: row.id\r\n        }).then(res => {\r\n          console.log('取消绑定资产位置成功:', res);\r\n          this.$message.success('取消绑定成功');\r\n          // 刷新资产位置表格数据\r\n          this.$refs.assetLocationGrid.queryData();\r\n          // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n          this.getBindedLocationIds();\r\n        }).catch(error => {\r\n          console.error('取消绑定资产位置失败:', error);\r\n          this.$message.error('取消绑定失败，请稍后重试');\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 资产位置 - 获取列配置\r\n    getAssetLocationColumn(e) {\r\n      this.assetLocationColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetLocationTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产位置 - 获取表格数据\r\n    getAssetLocationDatas(e) {\r\n      this.assetLocationTableData = e;\r\n    },\r\n\r\n    // ========== 资产设备相关方法 ==========\r\n    // 资产设备 - 获取列配置\r\n    getAssetDeviceColumn(e) {\r\n      this.assetDeviceColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetDeviceTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产设备 - 获取表格数据\r\n    getAssetDeviceDatas(e) {\r\n      this.assetDeviceTableData = e;\r\n    },\r\n\r\n    // 获取状态中文显示\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'DRAFT': '未启用',\r\n        'NORMAL': '正常',\r\n        'BREAKDOWN': '故障',\r\n        1: '未启用',\r\n        2: '正常',\r\n        3: '故障'\r\n      };\r\n      return statusMap[status] || status || '未知';\r\n    }\r\n  }\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8IA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/visualOpsManagement/deviceBinding", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮区域 -->\r\n    <div style=\"margin-bottom: 16px;\">\r\n      <el-button size=\"small\" @click=\"goBack\">\r\n        <i class=\"el-icon-arrow-left\"></i> 返回\r\n      </el-button>\r\n      <el-button v-permission=\"'assetLocation_add'\" type=\"primary\" size=\"small\" @click=\"openDialog\" style=\"margin-left: 10px;\">\r\n        <i class=\"el-icon-plus\"></i> 绑定资产位置\r\n      </el-button>\r\n    </div>\r\n\r\n    <el-tabs v-model=\"activeTab\" type=\"card\">\r\n      <!-- 资产位置 Tab -->\r\n      <el-tab-pane label=\"资产位置\" name=\"assetLocation\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <Grid\r\n              api=\"visualOpsManagement/assetLocation-page\"\r\n              :event-bus=\"assetLocationSearchEventBus\"\r\n              :search-params=\"assetLocationSearchParams\"\r\n              :newcolumn=\"assetLocationColumns\"\r\n              @datas=\"getAssetLocationDatas\"\r\n              @columnChange=\"getAssetLocationColumn\"\r\n              :auto-load=\"true\"\r\n              ref=\"assetLocationGrid\">\r\n              <el-table\r\n                slot=\"table\"\r\n                slot-scope=\"{loading}\"\r\n                v-loading=\"loading\"\r\n                :data=\"assetLocationTableData\"\r\n                stripe\r\n                ref=\"assetLocationTable\"\r\n                style=\"width: 100%\">\r\n                <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n                </el-table-column>\r\n                <template v-for=\"(item, index) in assetLocationColumns\">\r\n                  <el-table-column\r\n                    v-if=\"item.key == 'belongDept'\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :align=\"item.align ? item.align : 'center'\"\r\n                    :key=\"index\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div>\r\n                        {{ scope.row.belongDept || '未分配' }}\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    v-else\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :key=\"item.key\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                    :align=\"item.align ? item.align : 'center'\">\r\n                  </el-table-column>\r\n                </template>\r\n                <el-table-column fixed=\"right\" align=\"center\" label=\"操作\" type=\"action\" width=\"120\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button  type=\"text\" size=\"small\" @click=\"handleAssetLocationBind(scope.row)\">取消绑定</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </Grid>\r\n          </el-col>\r\n        </el-row>\r\n      </el-tab-pane>\r\n\r\n      <!-- 资产设备 Tab -->\r\n      <el-tab-pane label=\"资产设备\" name=\"assetDevice\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <Grid\r\n              api=\"visualOpsManagement/assetDevice-page\"\r\n              :event-bus=\"assetDeviceSearchEventBus\"\r\n              :search-params=\"assetDeviceSearchParams\"\r\n              :newcolumn=\"assetDeviceColumns\"\r\n              @datas=\"getAssetDeviceDatas\"\r\n              @columnChange=\"getAssetDeviceColumn\"\r\n              :auto-load=\"false\"\r\n              ref=\"assetDeviceGrid\">\r\n              <el-table\r\n                slot=\"table\"\r\n                slot-scope=\"{loading}\"\r\n                v-loading=\"loading\"\r\n                :data=\"assetDeviceTableData\"\r\n                stripe\r\n                ref=\"assetDeviceTable\"\r\n                style=\"width: 100%\">\r\n                <el-table-column fixed=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n                </el-table-column>\r\n                <template v-for=\"(item, index) in assetDeviceColumns\">\r\n                  <el-table-column\r\n                    v-if=\"item.key == 'recordStatus'\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :align=\"item.align ? item.align : 'center'\"\r\n                    :key=\"index\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div>\r\n                        {{ getStatusText(scope.row.recordStatus) }}\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    v-else\r\n                    :show-overflow-tooltip=\"true\"\r\n                    :key=\"item.key\"\r\n                    :prop=\"item.key\"\r\n                    :label=\"item.title\"\r\n                    :min-width=\"item.minWidth ? item.minWidth : '150'\"\r\n                    :align=\"item.align ? item.align : 'center'\">\r\n                  </el-table-column>\r\n                </template>\r\n              </el-table>\r\n            </Grid>\r\n          </el-col>\r\n        </el-row>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n\r\n      <!-- 绑定位置弹窗 -->\r\n    <BindPositionDialog\r\n      :visible.sync=\"dialogVisible\"\r\n      :row-data=\"currentRow\"\r\n      :regin-id=\"reginId\"\r\n      :bound-location-ids=\"assetDeviceSearchParams.locationIds\"\r\n      @success=\"handleSuccess\"\r\n      @cancel=\"handleCancel\"\r\n    />\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport BindPositionDialog from './component/BindPositionDialog';\r\nimport {getRouteParams, saveRouteParams, navigateWithParams} from '@/utils/routeParams';\r\n\r\n\r\n\r\nexport default {\r\n  name: 'DeviceBinding',\r\n  components: {\r\n    Grid,\r\n    BindPositionDialog\r\n  },\r\n  destroyed() {\r\n    this.assetLocationSearchEventBus.$off();\r\n    this.assetDeviceSearchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.assetLocationSearchEventBus = new Vue();\r\n    this.assetDeviceSearchEventBus = new Vue();\r\n\r\n    return {\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      activeTab: 'assetLocation', // 当前激活的Tab\r\n      reginId: '', // 区域ID\r\n      drawingId: '', // 图纸ID\r\n\r\n      // 资产位置搜索参数\r\n      assetLocationSearchParams: {\r\n        reginId: ''\r\n      },\r\n      // 资产设备搜索参数\r\n      assetDeviceSearchParams: {\r\n        locationIds: [] // 已绑定的位置ID数组，每次获取资产设备时都会传递最新的位置ID\r\n      },\r\n\r\n      // 资产位置相关数据\r\n      assetLocationColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产位置',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetLocationTableData: [],\r\n\r\n      // 资产设备相关数据\r\n      assetDeviceColumns: [\r\n        {\r\n          title: '资产编号',\r\n          key: 'assetCode',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'recordStatus',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '所属一线部门',\r\n          key: 'firstDeptName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '资产描述',\r\n          key: 'assetDesc',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '规格',\r\n          key: 'specification',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '是否主设备',\r\n          key: 'mainDeviceName',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '关联主设备',\r\n          key: 'relatedMainDeviceCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置编码',\r\n          key: 'locationCode',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '位置描述',\r\n          key: 'locationDesc',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '安装位置',\r\n          key: 'installationLocation',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        }\r\n      ],\r\n      assetDeviceTableData: []\r\n    };\r\n  },\r\n  created() {\r\n    // 使用工具函数获取参数\r\n    const params = getRouteParams(this.$route, ['reginId', 'drawingId'], 'deviceBinding_params');\r\n\r\n    if (params.reginId) {\r\n      this.reginId = params.reginId;\r\n      this.assetLocationSearchParams.reginId = params.reginId;\r\n      console.log('获取到 reginId 参数:', params.reginId);\r\n\r\n      // 获取已绑定的位置ID\r\n      this.getBindedLocationIds();\r\n    } else {\r\n      console.log('未获取到 reginId 参数');\r\n      this.$message.warning('缺少区域ID参数，请返回列表重新选择');\r\n    }\r\n\r\n    if (params.drawingId) {\r\n      this.drawingId = params.drawingId;\r\n      console.log('获取到 drawingId 参数:', params.drawingId);\r\n    } else {\r\n      console.log('未获取到 drawingId 参数');\r\n    }\r\n\r\n    // 保存参数，确保刷新后不丢失\r\n    if (params.reginId || params.drawingId) {\r\n      saveRouteParams(this.$router, this.$route, {\r\n        reginId: params.reginId,\r\n        drawingId: params.drawingId\r\n      }, 'deviceBinding_params');\r\n    }\r\n\r\n    this.getInit();\r\n  },\r\n  watch: {\r\n    // 监听 Tab 切换\r\n    activeTab(newTab) {\r\n      if (newTab === 'assetDevice' && this.reginId) {\r\n        // 切换到资产设备 Tab 时，重新获取位置ID并加载数据\r\n        this.getBindedLocationIds();\r\n      }\r\n    },\r\n\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      // 获取当前页面的参数\r\n      const drawingId = this.drawingId || this.$route.query.drawingId;\r\n\r\n      // 使用导航工具返回到区域绑定页面，并传递参数\r\n      if (drawingId) {\r\n        navigateWithParams(this.$router, 'areaBinding', {\r\n          drawingId: drawingId\r\n        });\r\n      } else {\r\n        // 如果没有参数，直接跳转\r\n        this.$router.push('/system/visualOpsManagement/areaBinding');\r\n      }\r\n    },\r\n\r\n    openDialog(row) {\r\n      // Handle case where row might be a click event or undefined\r\n      // Check if row is an event object (has type property) or not a valid data object\r\n      if (!row || row instanceof Event || row.type === 'click' || row.constructor.name.includes('Event')) {\r\n        this.currentRow = {};\r\n      } else {\r\n        this.currentRow = row;\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    handleSuccess() {\r\n      this.$message.success('绑定成功');\r\n      // 刷新资产位置列表\r\n      this.$refs.assetLocationGrid.queryData();\r\n      // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n      this.getBindedLocationIds();\r\n    },\r\n    handleCancel() {\r\n      console.log('用户取消操作');\r\n    },\r\n    refreshList() {\r\n      // 刷新数据列表\r\n    },\r\n\r\n    // 初始化数据\r\n    getInit() {\r\n      // 初始化相关数据\r\n    },\r\n\r\n    // 获取已绑定的位置ID\r\n    getBindedLocationIds() {\r\n      if (!this.reginId) {\r\n        return;\r\n      }\r\n\r\n      this.$api['visualOpsManagement/maintenanceRegionLocationRel-get']({\r\n        reginId: this.reginId\r\n      }).then(res => {\r\n        console.log('获取已绑定位置ID结果:', res);\r\n        if (res && res.length > 0) {\r\n          // 提取位置ID数组\r\n          this.assetDeviceSearchParams.locationIds = res;\r\n        } else {\r\n          this.assetDeviceSearchParams.locationIds = [0];\r\n          console.log('该区域暂无绑定的位置');\r\n        }\r\n\r\n        // 获取位置ID后，手动触发资产设备表格数据加载\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        console.error('获取已绑定位置ID失败:', error);\r\n        this.assetDeviceSearchParams.locationIds = [0];\r\n        this.$message.error('获取已绑定位置信息失败');\r\n\r\n        // 即使失败也要触发表格加载，显示空数据\r\n        this.$nextTick(() => {\r\n          if (this.$refs.assetDeviceGrid) {\r\n            this.$refs.assetDeviceGrid.queryData();\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    // 刷新资产设备数据（确保使用最新的位置ID）\r\n    refreshAssetDeviceData() {\r\n      console.log('刷新资产设备数据，重新获取最新的位置ID');\r\n      this.getBindedLocationIds();\r\n    },\r\n\r\n    // ========== 资产位置相关方法 ==========\r\n    // 资产位置 - 取消绑定\r\n    handleAssetLocationBind(row) {\r\n      this.$confirm('确认取消绑定该资产位置吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 调用取消绑定接口\r\n        this.$api['visualOpsManagement/assetLocation-delete']({\r\n          id: row.id\r\n        }).then(res => {\r\n          console.log('取消绑定资产位置成功:', res);\r\n          this.$message.success('取消绑定成功');\r\n          // 刷新资产位置表格数据\r\n          this.$refs.assetLocationGrid.queryData();\r\n          // 重新获取已绑定的位置ID，确保资产设备表格使用最新的位置ID\r\n          this.getBindedLocationIds();\r\n        }).catch(error => {\r\n          console.error('取消绑定资产位置失败:', error);\r\n          this.$message.error('取消绑定失败，请稍后重试');\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消操作');\r\n      });\r\n    },\r\n\r\n    // 资产位置 - 获取列配置\r\n    getAssetLocationColumn(e) {\r\n      this.assetLocationColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetLocationTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产位置 - 获取表格数据\r\n    getAssetLocationDatas(e) {\r\n      this.assetLocationTableData = e;\r\n    },\r\n\r\n    // ========== 资产设备相关方法 ==========\r\n    // 资产设备 - 获取列配置\r\n    getAssetDeviceColumn(e) {\r\n      this.assetDeviceColumns = e;\r\n      setTimeout(() => {\r\n        this.$refs.assetDeviceTable.doLayout();\r\n      }, 100);\r\n    },\r\n    // 资产设备 - 获取表格数据\r\n    getAssetDeviceDatas(e) {\r\n      this.assetDeviceTableData = e;\r\n    },\r\n\r\n    // 获取状态中文显示\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'DRAFT': '未启用',\r\n        'NORMAL': '正常',\r\n        'BREAKDOWN': '故障',\r\n        1: '未启用',\r\n        2: '正常',\r\n        3: '故障'\r\n      };\r\n      return statusMap[status] || status || '未知';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.el-select {\r\n  width: 100%;\r\n}\r\n/deep/.el-tabs--card>.el-tabs__header{\r\n  margin:0px;\r\n}\r\n</style>\r\n"]}]}