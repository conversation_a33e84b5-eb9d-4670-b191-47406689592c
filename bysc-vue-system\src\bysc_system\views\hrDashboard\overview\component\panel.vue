<template>
  <el-drawer
    :title="drawerTitle"
    :visible.sync="visible"
    :size="'40%'"
    :destroy-on-close="true"
  >
    <div class="drawer-content">
      <el-form :model="editForm" ref="editForm" :rules="rules">
        <el-form-item label="选择年份" prop="year">
          <el-date-picker
            v-model="editForm.year"
            type="year"
            placeholder="请选择年份"
            style="width: 100%"
            value-format="yyyy"
            :parse-on-number="true"
          />
        </el-form-item>

        <el-form-item :label="totalLabel" prop="personNum">
          <el-input-number
            v-model="editForm.personNum"
            :min="0"
            style="width: 100%"
            :placeholder="totalPlaceholder"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item :label="bwLabel" prop="bwPersonNum">
          <el-input-number
            v-model="editForm.bwPersonNum"
            :min="0"
            style="width: 100%"
            :placeholder="bwPlaceholder"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item :label="swLabel" prop="swPersonNum">
          <el-input-number
            v-model="editForm.swPersonNum"
            :min="0"
            style="width: 100%"
            :placeholder="swPlaceholder"
            controls-position="right"
          />
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button size="small" type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'EditPanel',
  data() {
    const validatePersonNum = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error(this.editForm.type === 'own' ? '年末自有人员总数不能为空' : '年末融德人员总数不能为空'));
      } else {
        callback();
      }
    };

    const validateBwPersonNum = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error(this.editForm.type === 'own' ? '博维年末自有人员不能为空' : '博维年末融德人员不能为空'));
      } else {
        callback();
      }
    };

    const validateSwPersonNum = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error(this.editForm.type === 'own' ? '首维年末自有人员不能为空' : '首维年末融德人员不能为空'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      editForm: {
        year: null,
        personNum: '',
        bwPersonNum: '',
        swPersonNum: '',
        type: '',
        etype: ''
      },
      rules: {
        year: [
          {required: true, message: '请选择年份', trigger: 'change'}
        ],
        personNum: [
          {
            required: true,
            validator: validatePersonNum,
            trigger: 'blur'
          },
          {type: 'number', message: '必须为数字'}
        ],
        bwPersonNum: [
          {required: true, validator: validateBwPersonNum, trigger: 'blur'},
          {type: 'number', message: '必须为数字'}
        ],
        swPersonNum: [
          {required: true, validator: validateSwPersonNum, trigger: 'blur'},
          {type: 'number', message: '必须为数字'}
        ]
      }
    };
  },
  computed: {
    drawerTitle() {
      const typeText = this.editForm.type === 'own' ? '自有人员' : '融德人员';
      const operationText = this.editForm.etype === 'add' ? '新增' : '编辑';
      return `${operationText}${typeText}`;
    },
    totalLabel() {
      return this.editForm.type === 'own' ? '年末自有人员总数' : '年末融德人员总数';
    },
    totalPlaceholder() {
      return this.editForm.type === 'own' ? '请输入年末自有人员总数' : '请输入年末融德人员总数';
    },
    bwLabel() {
      return `博维年末${this.editForm.type === 'own' ? '自有' : '融德'}人员`;
    },
    swLabel() {
      return `首维年末${this.editForm.type === 'own' ? '自有' : '融德'}人员`;
    },
    bwPlaceholder() {
      return `请输入博维年末${this.editForm.type === 'own' ? '自有' : '融德'}人员`;
    },
    swPlaceholder() {
      return `请输入首维年末${this.editForm.type === 'own' ? '自有' : '融德'}人员`;
    }
  },
  methods: {
    open(data, etype, type) {
      console.log(data, etype, type);
      this.visible = true;
      const normalizedType = type.toLowerCase() === 'own' || type === 'OWN' ? 'own' : 'rongde';
      if (data) {
        this.editForm = _.cloneDeep({
          ...data,
          type: data.belongOrg === 'OWN' ? 'own' : 'rongde',
          etype: etype
        });
      } else {
        this.editForm = {
          year: null,
          personNum: '',
          bwPersonNum: '',
          swPersonNum: '',
          type: normalizedType,
          etype: etype
        };
      }
    },
    handleCancel() {
      this.visible = false;
      this.$refs.editForm.resetFields();
    },
    handleSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const params = {
            year: this.editForm.year,
            personNum: this.editForm.personNum,
            bwPersonNum: this.editForm.bwPersonNum,
            swPersonNum: this.editForm.swPersonNum,
            belongOrg: this.editForm.type === 'own' ? 'OWN' : 'RONGDE'
          };
          if (this.editForm.id) {
            params.id = this.editForm.id;
          }

          this.$api['hrDashboard/financePersonDataQoq-save'](params).then(() => {
            this.$message.success(this.editForm.etype === 'add' ? '新增成功' : '更新成功');
            this.$emit('submit');
            this.visible = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: right;
  border-top: 1px solid #e8e8e8;
}

.drawer-footer .el-button {
  margin-left: 8px;
}
</style>
